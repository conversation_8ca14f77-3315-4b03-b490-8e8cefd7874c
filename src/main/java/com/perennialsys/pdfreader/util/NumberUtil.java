package com.perennialsys.pdfreader.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class NumberUtil {

    public static final String TWO_DECIMAL_PRECESSION_FORMAT = "##.00";
    public static final String THREE_DECIMAL_PRECESSION_FORMAT = "##.000";

    private static final DecimalFormat df = new DecimalFormat(TWO_DECIMAL_PRECESSION_FORMAT);

    public static double formatDecimal(double number, String decimalFormat) {
        DecimalFormat df = new DecimalFormat(decimalFormat);
        return Double.parseDouble(df.format(number));
    }

    public static double formatToTwoDecimalPrecision(double number) {
        return Double.parseDouble(df.format(number));
    }

    public static double subtractDoubles(double val1, double val2) {
        return BigDecimal.valueOf(val1).subtract(BigDecimal.valueOf(val2)).doubleValue();
    }

    public static double subtractDoubles(double... val1) {
        BigDecimal finalVal = new BigDecimal(0);
        for (double val : val1) {
            finalVal.subtract(BigDecimal.valueOf(val));
        }
        return finalVal.doubleValue();
    }

    public static double addDoubles(double val1, double val2) {
        return BigDecimal.valueOf(val1).add(BigDecimal.valueOf(val2)).doubleValue();
    }


    public static double addDoubles(double... valueArr) {
        BigDecimal sum = new BigDecimal(0);
        for (double val : valueArr) {
            sum = sum.add(BigDecimal.valueOf(val));
        }
        return sum.doubleValue();
    }

    public static double multiplyDoubles(double val1, double val2) {
        return BigDecimal.valueOf(val1).multiply(BigDecimal.valueOf(val2)).doubleValue();
    }

    public static double multiplyDoubles(double... valueArr) {
        BigDecimal product = new BigDecimal(0);
        for (double val : valueArr) {
            product = product.multiply(BigDecimal.valueOf(val));
        }
        return product.doubleValue();
    }

    public static double divideDouble(double val1, double val2) {
        return BigDecimal.valueOf(val1).divide(BigDecimal.valueOf(val2), 2, RoundingMode.HALF_UP).doubleValue();
    }

    public static double calculatePercentage(double maxRecords, double actualRecords){
        double result = 0;

        if(maxRecords > 0){
            result = multiplyDoubles(divideDouble(actualRecords, maxRecords), 100);
        }

        return result;
    }
}
