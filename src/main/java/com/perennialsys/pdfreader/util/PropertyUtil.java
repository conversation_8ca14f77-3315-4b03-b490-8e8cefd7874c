package com.perennialsys.pdfreader.util;

import org.apache.log4j.Logger;

import java.io.InputStream;
import java.util.Properties;

public class PropertyUtil {

    final static Logger LOGGER = Logger.getLogger(PropertyUtil.class);

    private PropertyUtil() {
    }

    public static Properties getProperties(String fileName) {
        InputStream addrStream = null;
        Properties properties = null;
        try {
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            addrStream = classLoader.getResourceAsStream(fileName + ".properties");
            if (addrStream != null) {
                properties = new Properties();
                properties.load(addrStream);
            }

        } catch (Exception e) {
            LOGGER.error("Error in PropertyUtility Static block : ", e);
        } finally {
            try {
                if (addrStream != null) {
                    addrStream.close();
                }
            } catch (Exception e) {
                LOGGER.error("Error in PropertyUtility Static block finally : ", e);
            }
        }
        return properties;
    }
}