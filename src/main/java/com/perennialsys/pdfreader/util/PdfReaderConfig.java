package com.perennialsys.pdfreader.util;

import com.perennialsys.pdfreader.constants.AppConstants;
import org.apache.log4j.Logger;

import java.util.Properties;

public class PdfReaderConfig {

    private static final Logger LOGGER = Logger.getLogger(PdfReaderConfig.class);
    private static PdfReaderConfig me;

    private static Properties properties = null;

    static {
        properties = PropertyUtil.getProperties("applicationConfig");
    }

    private String applicationEnviroment;
    private String applicationContextPath;
    private String resourcesRootPath;
    private String resourcesUploadFolder;
    private String resourcesExportFolder;
    private String loggerPath;
    private String applicationResourceURLPath;
    private String adminLoginUrl;
    private String userLoginUrl;
    private String mailDomainName;
    private String senderEmailAddress;
    private String senderEmailPassword;
    private String recepientEmailAddress;
    private String logLevel;
    private String truecopySignerEnv;
    private String iosPushEnv;
    private String iosP12FilePath;
    private String iosP12FilePassword;
    private boolean sendSMS;
    private boolean removeInvalidDataFiles;
    private String supportUserEmail;
    private String smsUsername;
    private String smsPassword;
    private String smsroute;
    private String smsSenderId;
    private String smsGwBaseUrl;
    private String subscriptionMode;
    private String blobcontainerName;
    private String s3UploadFolderName;
    private String s3RootFolderName;
    private String storageAccountType;
    private String awsAccessKey;
    private String awsSecretAccessKey;
    private String blobStorageAccountKey;
    private String remindersSenderEmail;
    private String remindersSenderPassword;
    private String remindersSenderRegion;
    private String notificationSenderEmail;
    private String notificationSenderPassword;
    private String notificationSenderRegion;
    private String accessKey;
    private String secretKey;
    private String zohoOauthToken;
    private String zohoOauthRefreshToken;
    private String zohoOauthTokenExpiry;
    private String zohoOauthTokenGenerationTime;

    private int sftpRetryCount;

    private PdfReaderConfig() {
    }

    synchronized public static PdfReaderConfig getInstance() {
        try {
            if (me == null) {
                me = new PdfReaderConfig();
                String tomcatEnvironment = GeneralUtil.getEnvironmentProperty(AppConstants.TOMCAT_ENVIRONMENT, AppConstants.QA_ENVIRONMENT);
                ConfigHelper.setEximApplicationLevelValues(properties, tomcatEnvironment.trim());
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("got exception while initializing singleton class GspConfig", e);
        }
        return me;
    }

    public static Properties getProperties() {
        return properties;
    }

    public String getApplicationEnviroment() {
        return applicationEnviroment;
    }

    public void setApplicationEnviroment(String applicationEnviroment) {
        this.applicationEnviroment = applicationEnviroment;
    }

    public String getApplicationContextPath() {
        return applicationContextPath;
    }

    public void setApplicationContextPath(String applicationContextPath) {
        this.applicationContextPath = applicationContextPath;
    }

    public String getResourcesRootPath() {
        return resourcesRootPath;
    }

    public void setResourcesRootPath(String resourcesRootPath) {
        this.resourcesRootPath = resourcesRootPath;
    }

    public String getResourcesUploadFolder() {
        return resourcesUploadFolder;
    }

    public void setResourcesUploadFolder(String resourcesUploadFolder) {
        this.resourcesUploadFolder = resourcesUploadFolder;
    }

    public String getResourcesExportFolder() {
        return resourcesExportFolder;
    }

    public void setResourcesExportFolder(String resourcesExportFolder) {
        this.resourcesExportFolder = resourcesExportFolder;
    }

    public String getLoggerPath() {
        return loggerPath;
    }

    public void setLoggerPath(String loggerPath) {
        this.loggerPath = loggerPath;
    }

    public String getApplicationResourceURLPath() {
        return applicationResourceURLPath;
    }

    public void setApplicationResourceURLPath(String applicationResourceURLPath) {
        this.applicationResourceURLPath = applicationResourceURLPath;
    }

    public String getAdminLoginUrl() {
        return adminLoginUrl;
    }

    public void setAdminLoginUrl(String adminLoginUrl) {
        this.adminLoginUrl = adminLoginUrl;
    }

    public String getUserLoginUrl() {
        return userLoginUrl;
    }

    public void setUserLoginUrl(String userLoginUrl) {
        this.userLoginUrl = userLoginUrl;
    }

    public String getMailDomainName() {
        return mailDomainName;
    }

    public void setMailDomainName(String mailDomainName) {
        this.mailDomainName = mailDomainName;
    }

    public String getSenderEmailAddress() {
        return senderEmailAddress;
    }

    public void setSenderEmailAddress(String senderEmailAddress) {
        this.senderEmailAddress = senderEmailAddress;
    }

    public String getSenderEmailPassword() {
        return senderEmailPassword;
    }

    public void setSenderEmailPassword(String senderEmailPassword) {
        this.senderEmailPassword = senderEmailPassword;
    }

    public String getRecepientEmailAddress() {
        return recepientEmailAddress;
    }

    public void setRecepientEmailAddress(String recepientEmailAddress) {
        this.recepientEmailAddress = recepientEmailAddress;
    }

    public String getLogLevel() {
        return logLevel;
    }

    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    public String getTruecopySignerEnv() {
        return truecopySignerEnv;
    }

    public void setTruecopySignerEnv(String truecopySignerEnv) {
        this.truecopySignerEnv = truecopySignerEnv;
    }

    public String getIosPushEnv() {
        return iosPushEnv;
    }

    public void setIosPushEnv(String iosPushEnv) {
        this.iosPushEnv = iosPushEnv;
    }

    public String getIosP12FilePath() {
        return iosP12FilePath;
    }

    public void setIosP12FilePath(String iosP12FilePath) {
        this.iosP12FilePath = iosP12FilePath;
    }

    public String getIosP12FilePassword() {
        return iosP12FilePassword;
    }

    public void setIosP12FilePassword(String iosP12FilePassword) {
        this.iosP12FilePassword = iosP12FilePassword;
    }

    public boolean isSendSMS() {
        return sendSMS;
    }

    public void setSendSMS(boolean sendSMS) {
        this.sendSMS = sendSMS;
    }

    public boolean isRemoveInvalidDataFiles() {
        return removeInvalidDataFiles;
    }

    public void setRemoveInvalidDataFiles(boolean removeInvalidDataFiles) {
        this.removeInvalidDataFiles = removeInvalidDataFiles;
    }

    public String getSupportUserEmail() {
        return supportUserEmail;
    }

    public void setSupportUserEmail(String supportUserEmail) {
        this.supportUserEmail = supportUserEmail;
    }

    public String getSmsUsername() {
        return smsUsername;
    }

    public void setSmsUsername(String smsUsername) {
        this.smsUsername = smsUsername;
    }

    public String getSmsPassword() {
        return smsPassword;
    }

    public void setSmsPassword(String smsPassword) {
        this.smsPassword = smsPassword;
    }

    public String getSmsroute() {
        return smsroute;
    }

    public void setSmsroute(String smsroute) {
        this.smsroute = smsroute;
    }

    public String getSmsSenderId() {
        return smsSenderId;
    }

    public void setSmsSenderId(String smsSenderId) {
        this.smsSenderId = smsSenderId;
    }

    public String getSmsGwBaseUrl() {
        return smsGwBaseUrl;
    }

    public void setSmsGwBaseUrl(String smsGwBaseUrl) {
        this.smsGwBaseUrl = smsGwBaseUrl;
    }

    public String getSubscriptionMode() {
        return subscriptionMode;
    }

    public void setSubscriptionMode(String subscriptionMode) {
        this.subscriptionMode = subscriptionMode;
    }

    public String getBlobcontainerName() {
        return blobcontainerName;
    }

    public void setBlobcontainerName(String blobcontainerName) {
        this.blobcontainerName = blobcontainerName;
    }

    public String getStorageAccountType() {
        return storageAccountType;
    }

    public void setStorageAccountType(String storageAccountType) {
        this.storageAccountType = storageAccountType;
    }

    public String getAwsAccessKey() {
        return awsAccessKey;
    }

    public void setAwsAccessKey(String awsAccessKey) {
        this.awsAccessKey = awsAccessKey;
    }

    public String getAwsSecretAccessKey() {
        return awsSecretAccessKey;
    }

    public void setAwsSecretAccessKey(String awsSecretAccessKey) {
        this.awsSecretAccessKey = awsSecretAccessKey;
    }

    public String getBlobStorageAccountKey() {
        return blobStorageAccountKey;
    }

    public void setBlobStorageAccountKey(String blobStorageAccountKey) {
        this.blobStorageAccountKey = blobStorageAccountKey;
    }

    public String getRemindersSenderEmail() {
        return remindersSenderEmail;
    }

    public void setRemindersSenderEmail(String remindersSenderEmail) {
        this.remindersSenderEmail = remindersSenderEmail;
    }

    public String getRemindersSenderPassword() {
        return remindersSenderPassword;
    }

    public void setRemindersSenderPassword(String remindersSenderPassword) {
        this.remindersSenderPassword = remindersSenderPassword;
    }

    public String getRemindersSenderRegion() {
        return remindersSenderRegion;
    }

    public void setRemindersSenderRegion(String remindersSenderRegion) {
        this.remindersSenderRegion = remindersSenderRegion;
    }

    public String getNotificationSenderEmail() {
        return notificationSenderEmail;
    }

    public void setNotificationSenderEmail(String notificationSenderEmail) {
        this.notificationSenderEmail = notificationSenderEmail;
    }

    public String getNotificationSenderPassword() {
        return notificationSenderPassword;
    }

    public void setNotificationSenderPassword(String notificationSenderPassword) {
        this.notificationSenderPassword = notificationSenderPassword;
    }

    public String getNotificationSenderRegion() {
        return notificationSenderRegion;
    }

    public void setNotificationSenderRegion(String notificationSenderRegion) {
        this.notificationSenderRegion = notificationSenderRegion;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getZohoOauthToken() {
        return zohoOauthToken;
    }

    public void setZohoOauthToken(String zohoOauthToken) {
        this.zohoOauthToken = zohoOauthToken;
    }

    public String getZohoOauthRefreshToken() {
        return zohoOauthRefreshToken;
    }

    public void setZohoOauthRefreshToken(String zohoOauthRefreshToken) {
        this.zohoOauthRefreshToken = zohoOauthRefreshToken;
    }

    public String getZohoOauthTokenExpiry() {
        return zohoOauthTokenExpiry;
    }

    public void setZohoOauthTokenExpiry(String zohoOauthTokenExpiry) {
        this.zohoOauthTokenExpiry = zohoOauthTokenExpiry;
    }

    public String getS3UploadFolderName() {
        return s3UploadFolderName;
    }

    public void setS3UploadFolderName(String s3UploadFolderName) {
        this.s3UploadFolderName = s3UploadFolderName;
    }


    public String getS3RootFolderName() {
        return s3RootFolderName;
    }

    public void setS3RootFolderName(String s3RootFolderName) {
        this.s3RootFolderName = s3RootFolderName;
    }

    public String getZohoOauthTokenGenerationTime() {
        return zohoOauthTokenGenerationTime;
    }

    public void setZohoOauthTokenGenerationTime(String zohoOauthTokenGenerationTime) {
        this.zohoOauthTokenGenerationTime = zohoOauthTokenGenerationTime;
    }

    public int getSftpRetryCount() {
        return sftpRetryCount;
    }

    public void setSftpRetryCount(int sftpRetryCount) {
        this.sftpRetryCount = sftpRetryCount;
    }
}
