package com.perennialsys.pdfreader.util;

import com.perennialsys.pdfreader.constants.AppConstants;
import org.apache.log4j.Logger;

import java.util.Properties;

public class ConfigHelper {

    private static final Logger LOGGER = Logger.getLogger(ConfigHelper.class);

    static Properties properties = null;

    static Properties emailProperties;

    public static void setEximApplicationLevelValues(Properties props, String tomcatEnvironment) throws Exception {

        PdfReaderConfig gspSingeton = PdfReaderConfig.getInstance();
        properties = props;
        String storageAccout = getProp("Storage_Account_To_Use");
        gspSingeton.setStorageAccountType(storageAccout);
        gspSingeton.setS3UploadFolderName(getProp("gsp.aws.s3.upload.folder.name"));
        gspSingeton.setS3RootFolderName(getProp("gsp.aws.s3.root.folder.name"));
        gspSingeton.setSftpRetryCount(Integer.parseInt(getProp("sftp.retry.count")));
        switch (tomcatEnvironment) {
            case AppConstants.DEV_ENVIRONMENT: {
                gspSingeton.setApplicationEnviroment(getProp("dev.environment"));
                gspSingeton.setApplicationContextPath(getProp("dev.application.context.path"));
                gspSingeton.setResourcesRootPath(getProp("dev.resources.root.path"));
                gspSingeton.setResourcesUploadFolder(getProp("dev.resources.upload.folder"));
                gspSingeton.setResourcesExportFolder(getProp("dev.resources.export.folder"));
                gspSingeton.setLoggerPath(getProp("dev.logger.path"));
                if (AppConstants.AZURE_STORAGE_ACCOUNT.equals(storageAccout)) {
                    gspSingeton.setBlobcontainerName(getProp("dev.asp.gsp.azure.blobcontainer.name"));
                    gspSingeton.setBlobStorageAccountKey(getProp("dev.asp.gsp.azure.account.key"));
                } else if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(storageAccout)) {
                    gspSingeton.setBlobcontainerName(getProp("dev.asp.gsp.aws.s3.bucket.name"));
                }
                break;
            }
            case AppConstants.QA_ENVIRONMENT: {
                gspSingeton.setApplicationEnviroment(getProp("qa.environment"));
                gspSingeton.setApplicationContextPath(getProp("qa.application.context.path"));
                gspSingeton.setResourcesRootPath(getProp("qa.resources.root.path"));
                gspSingeton.setResourcesUploadFolder(getProp("qa.resources.upload.folder"));
                gspSingeton.setResourcesExportFolder(getProp("qa.resources.export.folder"));
                gspSingeton.setLoggerPath(getProp("qa.logger.path"));
                if (AppConstants.AZURE_STORAGE_ACCOUNT.equals(storageAccout)) {
                    gspSingeton.setBlobcontainerName(getProp("qa.asp.gsp.azure.blobcontainer.name"));
                    gspSingeton.setBlobStorageAccountKey(getProp("qa.asp.gsp.azure.account.key"));
                } else if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(storageAccout)) {
                    gspSingeton.setBlobcontainerName(getProp("qa.asp.gsp.aws.s3.bucket.name"));
                }
                break;
            }
            case AppConstants.PROD_ENVIRONMENT: {
                gspSingeton.setApplicationEnviroment(getProp("prod.environment"));
                gspSingeton.setApplicationContextPath(getProp("prod.application.context.path"));
                gspSingeton.setResourcesRootPath(getProp("prod.resources.root.path"));
                gspSingeton.setResourcesUploadFolder(getProp("prod.resources.upload.folder"));
                gspSingeton.setResourcesExportFolder(getProp("prod.resources.export.folder"));
                gspSingeton.setLoggerPath(getProp("prod.logger.path"));
                if (AppConstants.AZURE_STORAGE_ACCOUNT.equals(storageAccout)) {
                    gspSingeton.setBlobcontainerName(getProp("prod.asp.gsp.azure.blobcontainer.name"));
                    gspSingeton.setBlobStorageAccountKey(getProp("prod.asp.gsp.azure.account.key"));
                } else if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(storageAccout)) {
                    gspSingeton.setBlobcontainerName(getProp("prod.asp.gsp.aws.s3.bucket.name"));
                }
                break;
            }
        }


        // AMAZON S3 IAM user credentials setting
        gspSingeton.setAwsAccessKey(getProp("s3.aws_access_key_id"));
        gspSingeton.setAwsSecretAccessKey(getProp("s3.aws_secret_access_key"));

        gspSingeton.setRemoveInvalidDataFiles(getProp("remove.invalid.data.files").equals("true"));

    }

    private static String getProp(String key) {
        return properties.getProperty(key);
    }

    private static String getEmailProp(String key) {
        return emailProperties.getProperty(key);
    }

    private static String getProp(String key, String defaultValue) {
        String value = defaultValue;
        try {
            value = properties.getProperty(key, defaultValue);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("got error while fetching property with key : " + key, e);
        }
        return value;
    }

}
