package com.perennialsys.pdfreader.util;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;


public class DateFormatUtil {

    public static final long ONE_MINUTE_IN_MILLIS = 60000;// millisecs
    public static final String ddMMYYYY_Hyphen = "dd-MM-yyyy";
    public static final String yyyyMMdd_Hyphen = "yyyy-MM-dd";
    public static final String ddMMYYYY_Slash = "dd/MM/yyyy";
    public static final String ddMMYYYY_Dot = "dd.MM.yyyy";
    public static final String ddMMYYYY_Hyphen_with_time = "dd-MM-yyyy hh:mm:ss a";
    public static final String ddMMYYYY_Slash_with_time = "dd/MM/yyyy hh:mm:ss a";
    public static final String ddMMYYYYwith_time = "dd MMM yyyy hh:mm:ss a";
    public static final String ddMMYYYY = "dd MMM yyyy";
    public static final String ddMMMYY_Hyphen = "dd-MMM-yy";
    public static final String MMYYYY_HYPHEN = "MM-yyyy";
    private static final SimpleDateFormat GSTR_DATE_SDF = new SimpleDateFormat(ddMMYYYY_Hyphen);
    private static final SimpleDateFormat GSTR_MONTH_SDF = new SimpleDateFormat("MMyyyy");
    private static final SimpleDateFormat GSTR_MONTH_STRING_SDF = new SimpleDateFormat("MMM yyyy");
    private static final SimpleDateFormat GSTR_MONTH_NAME_STRING_SDF = new SimpleDateFormat("MMMM");
    public static long ONE_DAY_MILIS = 1000 * 60 * 60 * 24;
    public static long ONE_MONTH_MILIS = ONE_DAY_MILIS * 30;
    public static long ONE_YEAR_MILIS = ONE_MONTH_MILIS * 12;

    long ONE_MIN_MILIS = 1000 * 60;

    /**
     * @param date1
     * @param date2
     * @param timeUnit
     * @return
     */
    public static long getDateDiff(Date date1, Date date2, TimeUnit timeUnit) {
        long diffInMillies = date2.getTime() - date1.getTime();
        return timeUnit.convert(diffInMillies, TimeUnit.MILLISECONDS);
    }

    public static long getDateDiffInDays(Date date1, Date date2) {
        long diff = 0;
        diff = date2.getTime() - date1.getTime();
        return TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
    }

    public static String getNotificationDate(Date date) {
        Date todaysDate = new Date();
        String result = "";
        if (todaysDate.compareTo(date) > 0) {
            long diff = todaysDate.getTime() - date.getTime();
            int hours = (int) diff / (1000 * 60 * 60);
            int mins = (int) diff / (1000 * 60);
            int sec = (int) diff / (1000);
            if (hours > 24) {
                result = formatDateToString(date, "dd-MMMM-yyyy");
            } else if (hours > 0) {
                result = hours + " hours ago";
            } else if (mins > 0) {
                result = mins + " minutes ago";
            } else if (sec > 0) {
                result = sec + " seconds ago";
            }
        }
        return result;
    }

    /**
     * Format date to date.
     *
     * @param date        the date
     * @param formatStyle the format style
     * @return the date
     */
    public static Date formatDateToDate(String date, String formatStyle) {
        Date formattedDate = null;
        if (date != null) {
            SimpleDateFormat format = new SimpleDateFormat(formatStyle);
            try {
                formattedDate = format.parse(date);
            } catch (ParseException e) {
                System.out.println(e.getMessage());
            }
        }
        return formattedDate;
    }

    public static Date getEndOfDaydate(Date date){
        if(date != null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH));
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            cal.set(Calendar.MILLISECOND, 999);
            return cal.getTime();
        }
        return null;
    }


    /**
     * Format date to string.
     *
     * @param date        the date
     * @param formatStyle the format style
     * @return the string
     */
    public static String formatDateToString(Date date, String formatStyle) {
        String formattedDate = null;
        if (date != null) {
            SimpleDateFormat format = new SimpleDateFormat(formatStyle);
            formattedDate = format.format(date);
        }
        return formattedDate;
    }

    public static Date formatDateToDate2(Date date, String formatStyle) throws ParseException {
        Date formattedDate = null;
        if (date != null) {
            SimpleDateFormat format = new SimpleDateFormat(formatStyle);
            formattedDate = format.parse(formatDateToString(date, formatStyle));
        }
        return formattedDate;
    }

    public static Date addDays(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);

        return cal.getTime();
    }

    public static Date substractDays(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -days);

        return cal.getTime();
    }

    /**
     * @param date
     * @return date
     */

    public static Date removeTimeFromDate(Date date) {
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTime(date);
        gc.set(Calendar.HOUR_OF_DAY, 0);
        gc.set(Calendar.MINUTE, 0);
        gc.set(Calendar.SECOND, 0);
        gc.set(Calendar.MILLISECOND, 0);
        Date returnDate = gc.getTime();
        return returnDate;
    }

    public static boolean isValidDate(Object object, String format) {
        boolean result = false;

        if(!Objects.isNull(object)){
            SimpleDateFormat dateSdf = new SimpleDateFormat(format);
            try {
                Date date = dateSdf.parse(object.toString());
                result = true;
            }catch (Exception e){
                result = false;
            }
        }

        return result;
    }

    public static String formatToGstnDateString(Object object) {
        SimpleDateFormat GSTR_DATE_SDF = new SimpleDateFormat(ddMMYYYY_Hyphen);
        if (object == null) {
            return null;
        }
        if (!(object instanceof Date)) {
            try {
                String dateStr = String.valueOf(object);
                if (isDDMMYY_Slash(object)) {
                    dateStr = dateStr.replaceAll("/", "-");
                } else if (isDDMMYY_Dot(object)) {
                    dateStr = dateStr.replaceAll("\\.", "-");
                } else if (isDDMMMYY_Hyphen(object)) {
                    SimpleDateFormat sdf = new SimpleDateFormat(ddMMMYY_Hyphen);
                    dateStr = GSTR_DATE_SDF.format(sdf.parse(String.valueOf(object)));
                }
                GSTR_DATE_SDF.setLenient(false);
                object = GSTR_DATE_SDF.parse(dateStr);
            } catch (ParseException e) {
                return object.toString();
            }
        }
        Date date = (Date) object;
        String dateStr = GSTR_DATE_SDF.format(date);
        return dateStr;
    }

    public static Date formatToDate(Object object) {
        SimpleDateFormat GSTR_DATE_SDF = new SimpleDateFormat(ddMMYYYY_Hyphen);
        Date dateToReturn = null;
        if (object == null) {
            return null;
        }
        if (object instanceof String) {
            try {
                String dateStr = String.valueOf(object);
                if (isDDMMYY_Slash(object)) {
                    dateToReturn = GSTR_DATE_SDF.parse(dateStr.replaceAll("/", "-"));
                } else if (isDDMMYY_Dot(object)) {
                    dateToReturn = GSTR_DATE_SDF.parse(dateStr.replaceAll("\\.", "-"));
                } else if (isDDMMMYY_Hyphen(object)) {
                    GSTR_DATE_SDF = new SimpleDateFormat(ddMMMYY_Hyphen);
                    dateToReturn = GSTR_DATE_SDF.parse(String.valueOf(object));
                } else if (isDDMMYYYY_Hyphen(dateStr)) {
                    dateToReturn = GSTR_DATE_SDF.parse(dateStr);
                } else if (isDDMMYYYYWithTime(dateStr)) {
                    GSTR_DATE_SDF = new SimpleDateFormat(ddMMYYYYwith_time);
                    dateToReturn = GSTR_DATE_SDF.parse(dateStr);
                } else if (isDDMMYYYY(dateStr)) {
                    GSTR_DATE_SDF = new SimpleDateFormat(ddMMYYYY);
                    dateToReturn = GSTR_DATE_SDF.parse(dateStr);
                }
            } catch (ParseException e) {
                return dateToReturn;
            }
        } else if (object instanceof Date) {
            dateToReturn = (Date) object;
        }
        return dateToReturn;
    }

    public static java.sql.Date toSqlDate(Object object) {
        if (object == null) {
            return null;
        }
        if (!(object instanceof Date)) {
            try {
                SimpleDateFormat GSTR_DATE_SDF = new SimpleDateFormat(ddMMYYYY_Hyphen);
                object = GSTR_DATE_SDF.parse(String.valueOf(object));
            } catch (ParseException e) {
                return null;
            }
        }
        Date date = (Date) object;
        return new java.sql.Date(date.getTime());
    }

    public static java.sql.Date toSqlDateForEwb(Object object) {
        if (object == null) {
            return null;
        }
        if (!(object instanceof Date)) {
            try {
                SimpleDateFormat GSTR_DATE_SDF = new SimpleDateFormat(ddMMYYYY_Slash);
                object = GSTR_DATE_SDF.parse(String.valueOf(object));
            } catch (ParseException e) {
                return null;
            }
        }
        Date date = (Date) object;
        return new java.sql.Date(date.getTime());
    }

    public static String formatToGstrMonthString(Object object) {
        if (object == null) {
            return null;
        }
        Date date = (Date) object;
        String dateStr = GSTR_MONTH_SDF.format(date);
        return dateStr;
    }

    public static String parseToGstrMonthString(String monthString) {
        Date date = new Date();
        try {
            date = GSTR_MONTH_SDF.parse(monthString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String dateStr = GSTR_MONTH_STRING_SDF.format(date);
        return dateStr;
    }

    /**
     * This method return month name as per return period
     *
     * @param retPeriod
     * @return
     */
    public static String parseMonthNameFromRetPeriod(String retPeriod) {
        Date date = new Date();
        try {
            date = GSTR_MONTH_SDF.parse(retPeriod);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String dateStr = GSTR_MONTH_NAME_STRING_SDF.format(date);
        return dateStr;
    }

    private static boolean isDDMMYY_Hyphen(Object value) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(ddMMYYYY_Hyphen);
            value = sdf.parse(String.valueOf(value));
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    private static boolean isDDMMYYYY_Hyphen(Object value) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(ddMMYYYY_Hyphen);
            sdf.parse(String.valueOf(value));
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    private static boolean isDDMMYYYYWithTime(Object value) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(ddMMYYYYwith_time);
            sdf.parse(String.valueOf(value));
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    private static boolean isDDMMYYYY(Object value) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(ddMMYYYY);
            sdf.parse(String.valueOf(value));
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    private static boolean isDDMMYY_Slash(Object value) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(ddMMYYYY_Slash);
            sdf.setLenient(false);
            value = sdf.parse(String.valueOf(value));
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    private static boolean isDDMMYY_Dot(Object value) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(ddMMYYYY_Dot);
            value = sdf.parse(String.valueOf(value));
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    public static int getDateDifferenceInMinutes(Date currentDate, Date lastLogidate) {
        int timeDifference = 0;
        if (lastLogidate == null) {
        } else {
            timeDifference = (int) ((currentDate.getTime() / 60000) - (lastLogidate.getTime() / 60000));
        }
        return timeDifference;
    }

    public static boolean isDateStrValidformat(String value, String format) {

        boolean isDate = false;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            sdf.parse(String.valueOf(value));
            isDate = true;
        } catch (ParseException e) {
            return false;
        }
        return isDate;
    }

    public static String formatDateToTimezoneString(Date date, String formatStyle, String timezone) {
        String formattedDate = null;
        if (date != null) {
            SimpleDateFormat format = new SimpleDateFormat(formatStyle);
            format.setTimeZone(TimeZone.getTimeZone(timezone));
            formattedDate = format.format(date);
        }
        return formattedDate;
    }

    private static boolean isDDMMMYY_Hyphen(Object value) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(ddMMMYY_Hyphen);
            value = sdf.parse(String.valueOf(value));
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    public static boolean isValidEwbDateFormat(Object value) {
        boolean isValid = true;
        if (StringUtils.isBlank((String) value)) {
            return isValid;
        }
        if (!(value instanceof Date)) {
            if (!DateFormatUtil.isDDMMYY_Slash(value)) {
                isValid = false;
            }
        }
        return isValid;
    }

    public static String formatToEwbDateString(Object object) {
        SimpleDateFormat GSTR_DATE_SDF = new SimpleDateFormat(ddMMYYYY_Slash);
        if (object == null) {
            return null;
        }
        if (!(object instanceof Date)) {
            try {
                String dateStr = String.valueOf(object);
                if (isDDMMYY_Hyphen(object)) {
                    dateStr = dateStr.replaceAll("-", "/");
                } else if (isDDMMYY_Dot(object)) {
                    dateStr = dateStr.replaceAll("\\.", "/");
                } else if (isDDMMYY_Slash(object)) {
                    SimpleDateFormat sdf = new SimpleDateFormat(ddMMYYYY_Slash);
                    dateStr = GSTR_DATE_SDF.format(sdf.parse(String.valueOf(object)));
                }
                GSTR_DATE_SDF.setLenient(false);
                object = GSTR_DATE_SDF.parse(dateStr);
            } catch (ParseException e) {
                return object.toString();
            }
        }
        Date date = (Date) object;
        String dateStr = GSTR_DATE_SDF.format(date);
        return dateStr;
    }

    public static boolean isValidGstnDateFormat(Object value) {
        boolean isValid = true;
        if (value == null) {
            return isValid;
        }
        if (!(value instanceof Date)) {
            if (!DateFormatUtil.isDDMMYY_Hyphen(value) && !DateFormatUtil.isDDMMYY_Slash(value)
                    && !DateFormatUtil.isDDMMYY_Dot(value) && !DateFormatUtil.isDDMMMYY_Hyphen(value)) {
                isValid = false;
            }
        }
        return isValid;
    }

    public static Date convertMMYYYYToDate(String dateStr) throws PdfReaderException {
        SimpleDateFormat dateFormat = new SimpleDateFormat(MMYYYY_HYPHEN);
        try {
            return dateFormat.parse(dateStr);
        } catch (Exception e) {
            throw new PdfReaderException(ResponseCode.INVALID_DATE_FORMAT, ResponseMessage.INVALID_DATE_FORMAT);
        }
    }

    /**
     * @param date - Date object
     * @return - Returns the end of the last day of the month
     * <AUTHOR> Nagare
     * @since 27/01/2023
     * This method returns end of the last day of the month
     */
    public static Date getLastDaydate(Date date) {
        if (date != null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            cal.set(Calendar.MILLISECOND, 999);
            return cal.getTime();
        }

        return null;
    }

    public static Date getDayEndTime(Date date){
        if (date != null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            cal.set(Calendar.MILLISECOND, 999);
            return cal.getTime();
        }

        return null;
    }

    public static String convertDateToStringAndFormat(String date, String format) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);

        LocalDateTime ldt = LocalDateTime.parse(date, dateTimeFormatter);

        ZonedDateTime utc =  ldt.atZone(ZoneId.of("UTC"));
        ZonedDateTime ist =  utc.withZoneSameInstant(ZoneId.of("Asia/Kolkata"));

        return ist.format(dateTimeFormatter);
    }

    public static String convertDateToStringAndFormat(Date date, String format) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);

        LocalDateTime ldt = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        ZonedDateTime utc =  ldt.atZone(ZoneId.of("UTC"));
        ZonedDateTime ist =  utc.withZoneSameInstant(ZoneId.of("Asia/Kolkata"));

        return ist.format(dateTimeFormatter);
    }

    /**
     * <AUTHOR> Nagare
     * This method returns the financial year from the return period.
     * @param date Date
     * @return Financial year
     */
    public static String getFinancialYearFromDate(Date date) {
        String fy = null;

        if(null != date) {
            SimpleDateFormat dateFormatter = new SimpleDateFormat("MMyyyy");
            String mmYyyyDate = dateFormatter.format(date);
            int returnMonth = Integer.parseInt(mmYyyyDate.substring(0, 2));
            int returnYear = Integer.parseInt(mmYyyyDate.substring(2));
            if (returnMonth <= 3) {
                fy = (returnYear - 1) + "-" + mmYyyyDate.substring(4);
            } else {
                fy = returnYear + "-"
                        + String.valueOf(returnYear + 1).substring(2);
            }
        }

        return fy;
    }
}
