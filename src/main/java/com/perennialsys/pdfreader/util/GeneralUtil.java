package com.perennialsys.pdfreader.util;

import com.mongodb.BasicDBObject;
import com.perennialsys.pdfreader.constants.AppConstants;
import com.perennialsys.pdfreader.enums.DocumentAttributeBeanFields;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import org.apache.log4j.Logger;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

public class GeneralUtil {

    private static final Logger logger = Logger.getLogger(GeneralUtil.class);

    public static String storeFileAndReturnPath(InputStream inputFileStream, String folderPathToStoreFile,
                                                String fileNameWithExtension, String blobContainerName) throws PdfReaderException {
        try {

            byte[] bytes = org.apache.commons.io.IOUtils.toByteArray(inputFileStream);
            File dir = new File(folderPathToStoreFile);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String completeFilePath = dir.getAbsolutePath() + File.separator + fileNameWithExtension;
            completeFilePath.concat("");

            File fileToStore = new File(completeFilePath);

            try (BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream(fileToStore))) {
                stream.write(bytes);
            } catch (Exception e) {
                throw e;
            }
            // Saving file to Amazon S3
            if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
                AmazonS3StorageUtil.uploadFileToAmazonS3Storage(blobContainerName, completeFilePath,
                        fileNameWithExtension);
            }
            if (!AppConstants.LOCAL_STORAGE.equals(PdfReaderConfig.getInstance().getStorageAccountType())
                    && PdfReaderConfig.getInstance().isRemoveInvalidDataFiles()) {
                removeLocalCopy(Collections.singletonList(completeFilePath));
            }

            return completeFilePath;
        } catch (Exception e) {
            throw new PdfReaderException(e);
        }
    }

    public static String getEnvironmentProperty(String propertyKey, String defaultValue) {

        String value = defaultValue;
        Context initCtx;
        try {
            initCtx = new InitialContext();
            Context envCtx = (Context) initCtx.lookup("java:comp/env");
            // Look up our data source
            value = (String) envCtx.lookup(propertyKey);
            // System.setProperty(propertyKey, value);
        } catch (NamingException e) {
            logger.error(e);
        }

        /*
         * String mm = System.getenv().toString(); String hhh = System.getProperty(propertyKey);
         * System.out.println(System.getProperties().toString());
         */

        System.out.println("\n\n\n\n : " + value + "\n\n\n\n");

        return value;
    }

    public static void removeLocalCopy(List<String> fileList) throws PdfReaderException {
        try {
            for (String filePath : fileList) {
                File file = new File(filePath);
                if (file.exists()) {
                    file.delete();
                }
            }
        } catch (Exception exception) {
            logger.error("Exception occur while deleting local file copy", exception);
        }
    }

    public static String createCommaSeperatedStr(String... attributeArr) {
        StringBuilder attributeBuilder = new StringBuilder();
        int count = 0;
        for (String attribute : attributeArr) {
            if (count != 0) {
                attributeBuilder.append(", ");
            }
            attributeBuilder.append(attribute);
            count++;
        }
        return attributeBuilder.toString();
    }
}
