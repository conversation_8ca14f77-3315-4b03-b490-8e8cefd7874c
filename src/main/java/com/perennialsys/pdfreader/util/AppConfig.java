package com.perennialsys.pdfreader.util;

import org.apache.log4j.Logger;

import java.util.Properties;

public class AppConfig {

    private static final Logger LOGGER = Logger.getLogger(AppConfig.class);

    private static AppConfig me;

    private static Properties properties = null;

    static {
        properties = PropertyUtil.getProperties("application");
    }

    private AppConfig() {
    }

    synchronized public static AppConfig getInstance() {
        try {
            if (me == null) {
                me = new AppConfig();
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("got exception while initializing singleton class AppConfig", e);
        }
        return me;
    }

    public static Properties getProperties() {
        return properties;
    }

    public static String getAppProperty(String key) {
        return properties.getProperty(key);
    }

    public static String getAppProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }

}
