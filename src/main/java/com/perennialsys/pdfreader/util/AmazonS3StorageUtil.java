package com.perennialsys.pdfreader.util;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import org.apache.log4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class AmazonS3StorageUtil {

    private static final Logger LOGGER = Logger.getLogger(AmazonS3StorageUtil.class);
    private static final String bucketName = PdfReaderConfig.getInstance().getBlobcontainerName();
    private static AmazonS3 s3Client = null;

    static {
        try {
            // Creating amazon S3 storage client from IAM user
            // credentials
            BasicAWSCredentials awsCredencials = new BasicAWSCredentials(PdfReaderConfig.getInstance().getAwsAccessKey(),
                    PdfReaderConfig.getInstance().getAwsSecretAccessKey());
            s3Client = AmazonS3ClientBuilder.standard()
                    .withCredentials(new AWSStaticCredentialsProvider(awsCredencials)).withRegion(Regions.AP_SOUTH_1)
                    .build();
        } catch (Exception exception) {
            LOGGER.error("Exception occur while creating the Amazon S3 Cloud storage client .", exception);
            exception.printStackTrace();
        }
    }

    /**
     * This method will upload file in given folder with given file name on s3 bucket
     *
     * @param folderName
     * @param filePath
     * @param fileName
     * @throws com.perennialsys.pdfreader.exception.PdfReaderException
     */
    public static void uploadFileToAmazonS3Storage(String folderName, String filePath, String fileName)
            throws PdfReaderException {
        try {
            LOGGER.info("START :  upload file with name :" + fileName + " and from folder name : " + folderName);
            ObjectMetadata metadata = new ObjectMetadata();
            Path path = Paths.get(filePath);
            byte[] data = Files.readAllBytes(path);
            metadata.setContentLength(data.length);

            try (InputStream inputStream = new ByteArrayInputStream(data)) {
                // send request to S3 to create folder and upload file
                s3Client.putObject(new PutObjectRequest(bucketName,
                        folderName.toLowerCase() + fileName, inputStream, metadata));
            }

            LOGGER.info("END :  upload file with name :" + fileName + " and from folder name : " + folderName);
        } catch (Exception e) {
            LOGGER.error("Exception occur while uploading the file.");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_UPLOAD_FILE, ResponseMessage.UNABLE_TO_UPLOAD_FILE);
        }
    }

    /**
     * This method will download file in given folder with given file name from s3 bucket
     *
     * @param folderName
     * @param fileName
     * @param filePath
     * @throws PdfReaderException
     */
    public static void downloadFromAmazonS3Storage(String folderName, String fileName, String filePath)
            throws PdfReaderException {
        LOGGER.info("START :  download file with name :" + fileName + " and from folder name : " + folderName);
        try {
            s3Client.getObject(new GetObjectRequest(bucketName, folderName.toLowerCase() + fileName),
                    new File(filePath));
            LOGGER.info("END :  download file with name :" + fileName + " and from folder name : " + folderName);
        } catch (Exception e) {
            LOGGER.error("exception while downloading the file from amazon s3 storage", e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_DOWNLOAD_FILE, ResponseMessage.UNABLE_TO_DOWNLOAD_FILE);
        }

    }

    /**
     * Delete files from Amazon S3 bucket
     *
     * @param folderName
     * @param fileName
     * @throws PdfReaderException
     */
    public static void deleteFileFromAmazonS3Stoorage(String folderName, String fileName) throws PdfReaderException {
        try {
            LOGGER.info("START : deleting file from Amazon SES " + folderName + fileName);
            s3Client.deleteObject(bucketName, folderName.toLowerCase() + fileName);
            LOGGER.info("END : deleted file from Amazon SES " + folderName + fileName);
        } catch (AmazonServiceException e) {
            LOGGER.error(
                    "The call was transmitted successfully, but Amazon S3 couldn't process it, so it returned an error response",
                    e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_DELETE_FILE, ResponseMessage.UNABLE_TO_DELETE_FILE);
        } catch (SdkClientException e) {
            LOGGER.error(
                    "Amazon S3 couldn't be contacted for a response, or the client couldn't parse the response from Amazon S3.",
                    e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_DELETE_FILE, ResponseMessage.UNABLE_TO_DELETE_FILE);
        } catch (Exception e) {
            LOGGER.error("exception while deleting the file from amazon s3 storage", e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_DELETE_FILE, ResponseMessage.UNABLE_TO_DELETE_FILE);
        }
    }
}
