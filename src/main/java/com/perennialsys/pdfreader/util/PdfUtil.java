package com.perennialsys.pdfreader.util;

import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.helper.FileHelper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.fit.pdfdom.PDFDomTree;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.EOFException;
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.RandomAccessFile;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Nagare
 * @since 06/12/2022
 * </p>
 * This util class will help in converting the PDF into text or html format.
 */
public class PdfUtil {

    @Getter
    @AllArgsConstructor
    private enum PdfDivStyleAttributeEnum {
        TOP("top"),
        LEFT("left"),
        LINE_HEIGHT("line-height"),
        FONT_WEIGHT("font-weight"),
        WIDTH("width"),
        COLOR("color");

        private final String value;

        public static PdfDivStyleAttributeEnum getEnumFromValue(String value) throws PdfReaderException {
            for (PdfDivStyleAttributeEnum divEnum : values()) {
                if (divEnum.getValue().equals(value)) {
                    return divEnum;
                }
            }

            throw new PdfReaderException("", "");
        }
    }

    private static final Logger LOGGER = Logger.getLogger(PdfUtil.class);

    public static String parseAndStorePDFAsHtml(File pdfFile, String pan, String txnId) throws PdfReaderException, IOException {
        String fileName = "pdfHTMLData_" + pan + "_" + txnId + "_" + new Date().getTime() + ".html";
        PDDocument pdfDocument = Loader.loadPDF(pdfFile);

        return saveHtmlFileToStorage(pdfDocument, fileName, pan);
    }

    /**
     * @param pdfFile - PDF file to be parsed to text.
     * @return String - String data from the PDF.
     * @throws PdfReaderException - This method throws PdfReaderException if any unexpected error occurs.
     * <AUTHOR> Nagare
     * @since 06/12/2022
     * </p>
     * Steps -
     * 1. Read the PDF file as string.
     * 2. Store the text file in the local storage for parsing.
     */
    public static String parsePDFToText(File pdfFile) throws PdfReaderException {

        if (!pdfFile.exists()) {
            //File does not exist exception.
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_DATA_MISSING_FILE);
        }

        String pdfString;

        try (PDDocument pdDocument = Loader.loadPDF(pdfFile)) {

            if (pdDocument.isEncrypted()) {
                //PDF File is encrypted so we cannot read the file
                throw new PdfReaderException(ResponseCode.INVALID_FILE_FORMAT, ResponseMessage.INVALID_FILE_FORMAT_ENCRYPTED_PDF);
            }

            //Convert the PDF file into text.
            PDFTextStripper textStripper = new PDFTextStripper();
            pdfString = textStripper.getText(pdDocument);

        } catch (IOException e) {
            LOGGER.error("ERROR >> PdfReader >> parsePDFToTextFile >> Error while loading the PDF File >> ", e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_READ_FILE_DATA, ResponseMessage.UNABLE_TO_READ_FILE_DATA);
        }

        return pdfString;
    }

    /**
     * @param pdDocument            - Pd Document for the PDF file
     * @param fileNameWithExtension - File name with Extension which should be used to store the file.
     * @throws PdfReaderException - Method throws Exim Exception in case of any unexpected error.
     * <AUTHOR> Nagare
     * @since 06/12/2022
     * </p>
     * This method will convert the String data into file and store it to the storage.
     */
/*    public static String saveHtmlFileToStorage(PDDocument pdDocument, String fileNameWithExtension, String pan) throws PdfReaderException, IOException {
        //Save the file to local storage or S3 based on the configuration.
        String folderPath = FileHelper.getLocalFolderPath(pan, FileHelper.HTML_FILE_OPERATION, null, false);
        String filePath = folderPath + fileNameWithExtension;
        File file = new File(filePath);

        if (!file.exists()) {
            file.getParentFile().mkdirs();
            file.createNewFile();
        }

        try (PrintWriter writer = new PrintWriter(file, "utf-8")) {
            new PDFDomTree().writeText(pdDocument, writer);
        } catch (IOException e) {
            LOGGER.error("ERROR >> PdfReader >> parsePDFToTextFile >> Error while loading the PDF File >> ", e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_READ_FILE_DATA, ResponseMessage.UNABLE_TO_READ_FILE_DATA);
        }

        return filePath;
    }*/

    public static String generateShortHtmlFileName() {
        String timestamp = new SimpleDateFormat("yyMMdd_HHmmss").format(new Date());
        String randomSuffix = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 4).toUpperCase();
        return "pdf_" + timestamp + "_" + randomSuffix + ".html";
    }

    public static String saveHtmlFileToStorage(PDDocument pdDocument, String fileNameWithExtension, String pan) throws PdfReaderException, IOException {
        // Static path to store HTML files
        String staticFolderPath = "D:/PDF/";
        String filePath = staticFolderPath + generateShortHtmlFileName();

        File file = new File(filePath);

        try {
            // Ensure the directory exists
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }

            // Create the file if it doesn't exist
            if (!file.exists()) {
                file.createNewFile();
            }

            // Write the PDF content as HTML
            try (PrintWriter writer = new PrintWriter(file, "utf-8")) {
                new PDFDomTree().writeText(pdDocument, writer);
            }

        } catch (IOException e) {
            LOGGER.error("ERROR >> PdfReader >> saveHtmlFileToStorage >> Error while saving the HTML file >> ", e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_READ_FILE_DATA, ResponseMessage.UNABLE_TO_READ_FILE_DATA);
        }

        return filePath;
    }

    /**
     * @param filePath - File Path with the file name and extension.
     * @return BufferedReader - It returns the Buffered reader for the file.
     * @throws PdfReaderException - Method throws Exim Exception in case of any unexpected error.
     * <AUTHOR> Nagare
     * @since 06/12/2022
     * </p>
     * This Method gets the file, creates the buffer reader from the exception and return this.
     */
    public static RandomAccessFile getPdfHtmlFile(String filePath) throws PdfReaderException {
        try {
            //TODO Mrunal Nagare 06/12/2022 - Need to add code to get the file from local storage or from S3 based on the configurations
            File file = new File(filePath);
            if (!file.exists()) {
                //File does not exist exception.
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_DATA_MISSING_FILE);
            }
            return new RandomAccessFile(file, "r");
        } catch (IOException e) {
            LOGGER.error("ERROR >> PdfReader >> parsePDFToTextFile >> Error while loading the PDF File >> ", e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_READ_FILE_DATA, ResponseMessage.UNABLE_TO_READ_FILE_DATA);
        }
    }

    /**
     * @param line - Random access HTML file reader
     * <AUTHOR> Nagare
     * @since 19/12/2022
     *
     * </p>
     * This Method will read one div string and convert it into pdfCivBean which will be used for further processing.
     */
    public static PdfDivBean parseDataPdfHtmlDiv(String line) {

        PdfDivBean pdfDivBean = null;

        Document jsoupHtmlDocument = Jsoup.parse(line);

        Elements elements = jsoupHtmlDocument.getElementsByTag("div");
        if (elements.size() == 1 && elements.get(0) != null) {
            //Get the first element as the elements will only have 1 entry as each row will have only one div.
            Element element = elements.get(0);
            pdfDivBean = new PdfDivBean();
            setDivElementDetails(pdfDivBean, element);
        } else {
            //TODO Mrunal Nagare - 19/12/2022 - Throw an exception.
        }

        return pdfDivBean;
    }

    /**
     * @param pdfDivBean - Pdf Div Bean
     * @param element    - Jsoup element
     * <AUTHOR> Nagare
     * @since 19/12/2022
     *
     * </p>
     * This method will convert the Div Element into the PDF div Bean
     */
    private static void setDivElementDetails(PdfDivBean pdfDivBean, Element element) {
        pdfDivBean.setValue(element.text());
        pdfDivBean.setDivClass(element.className());
        pdfDivBean.setId(element.id());

        //Set the style attributes to the bean.
        String styleAttributes = element.attributes().get("style");
        if (StringUtils.isNotBlank(styleAttributes)) {
            String[] styleAttrArray = styleAttributes.split(";");
            for (String styleAttr : styleAttrArray) {
                //Splitting the Style attribute string in to the attribute name and attribute value.
                String[] styleAttrValArray = styleAttr.split(":");

                try {
                    switch (PdfDivStyleAttributeEnum.getEnumFromValue(styleAttrValArray[0])) {
                        case TOP: {
                            pdfDivBean.setTop(Double.parseDouble(styleAttrValArray[1].replace("pt", "")));
                            break;
                        }
                        case LEFT: {
                            pdfDivBean.setLeft(Double.parseDouble(styleAttrValArray[1].replace("pt", "")));
                            break;
                        }
                        case LINE_HEIGHT: {
                            pdfDivBean.setLineHeight(Double.parseDouble(styleAttrValArray[1].replace("pt", "")));
                            break;
                        }
                        case FONT_WEIGHT: {
                            pdfDivBean.setFontWeight(styleAttrValArray[1]);
                            break;
                        }
                        case WIDTH: {
                            pdfDivBean.setWidth(Double.parseDouble(styleAttrValArray[1].replace("pt", "")));
                            break;
                        }
                        case COLOR: {
                            pdfDivBean.setColorCode(styleAttrValArray[1]);
                            break;
                        }
                        default: {
                            break;
                        }
                    }
                } catch (PdfReaderException e) {
                    continue;
                }
            }
        }
    }

    public static List<PdfDivBean> getAllRowsForThePage(RandomAccessFile fileReader, int pageCount, boolean searchFromStart) throws PdfReaderException {

        List<PdfDivBean> pdfDivBeanList = new ArrayList<>();

        PdfDivBean pdfDivBean;

        try {
            if(searchFromStart) {
                //Go to the start of file
                fileReader.seek(0);
            }
            //Go to the start of the page
            do {
                String line = fileReader.readLine();

                if (StringUtils.isBlank(line)) {
                    throw new EOFException();
                }

                pdfDivBean = PdfUtil.parseDataPdfHtmlDiv(line);
            } while (pdfDivBean == null || !pdfDivBean.getDivClass().equals("page") ||
                    !pdfDivBean.getId().equals("page_" + pageCount));

            pdfDivBeanList.add(pdfDivBean);

            long previousLinePointer = 0;
            //Get all the data dives for that page
            while (null == pdfDivBean || !pdfDivBean.getId().equals("page_" + (pageCount + 1))) {
                if (null != pdfDivBean && pdfDivBean.getDivClass().equals("p")) {
                    pdfDivBeanList.add(pdfDivBean);
                }

                previousLinePointer = fileReader.getFilePointer();
                String line = fileReader.readLine();

                if (StringUtils.isBlank(line)) {
                    throw new EOFException();
                }

                pdfDivBean = PdfUtil.parseDataPdfHtmlDiv(line);
            }

            fileReader.seek(previousLinePointer);

        } catch (EOFException eofException) {
            LOGGER.error("ERROR >> PdfUtil >> getAllRowsForThePage >> We have reached the end of file", eofException);
            throw new PdfReaderException(ResponseCode.REACHED_END_OF_PDF_FILE, ResponseMessage.REACHED_END_OF_PDF_FILE);
        } catch (IOException e) {
            LOGGER.error("ERROR >> PdfUtil >> getAllRowsForThePage >> ", e);
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }
        return pdfDivBeanList;
    }

    public static long getAllRowsForThePage(RandomAccessFile fileReader, int pageCount, boolean searchFromStart,
                                            List<PdfDivBean> dataDivList, List<PdfDivBean> otherDivList, boolean startFromPageStart,
                                            long pageStartPointer) throws PdfReaderException {

        PdfDivBean pdfDivBean;
        try {
            if(startFromPageStart && pageStartPointer > 0){
                fileReader.seek(pageStartPointer);
            }else if(searchFromStart) {
                //Go to the start of file
                fileReader.seek(0);
            }
            //Go to the start of the page
            do {
                pageStartPointer = fileReader.getFilePointer();
                String line = fileReader.readLine();

                if (StringUtils.isBlank(line)) {
                    throw new EOFException();
                }

                pdfDivBean = PdfUtil.parseDataPdfHtmlDiv(line);
            } while (pdfDivBean == null || !pdfDivBean.getDivClass().equals("page") ||
                    !pdfDivBean.getId().equals("page_" + pageCount));

            dataDivList.add(pdfDivBean);

            long previousLinePointer = 0;
            //Get all the data dives for that page
            while (null == pdfDivBean || !pdfDivBean.getId().equals("page_" + (pageCount + 1))) {
                if (null != pdfDivBean) {
                    if(pdfDivBean.getDivClass().equals("p")) {
                        dataDivList.add(pdfDivBean);
                    }else {
                        otherDivList.add(pdfDivBean);
                    }
                }

                previousLinePointer = fileReader.getFilePointer();
                String line = fileReader.readLine();
                if (StringUtils.isBlank(line)) {
                    throw new EOFException();
                }

                pdfDivBean = PdfUtil.parseDataPdfHtmlDiv(line);

            }


            fileReader.seek(previousLinePointer);

        } catch (EOFException eofException) {
            LOGGER.error("ERROR >> PdfUtil >> getAllRowsForThePage >> We have reached the end of file", eofException);
            throw new PdfReaderException(ResponseCode.REACHED_END_OF_PDF_FILE, ResponseMessage.REACHED_END_OF_PDF_FILE);
        } catch (IOException e) {
            LOGGER.error("ERROR >> PdfUtil >> getAllRowsForThePage >> ", e);
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }

        return pageStartPointer;
    }

    public static double calculateTopOfNextRow(PdfDivBean currentRowDiv, double bufferLineHeight) {
        return NumberUtil.formatDecimal(NumberUtil.addDoubles(currentRowDiv.getTop(), currentRowDiv.getLineHeight(),
                bufferLineHeight), NumberUtil.THREE_DECIMAL_PRECESSION_FORMAT);
    }

    /**
     * @param pageDivList - List of All the dives in the page.
     * @return - Page width
     * @throws PdfReaderException - Throws an exception if we do not find page width.
     * <AUTHOR> Nagare
     * @since 21/12/2022
     * </p>
     * This method will iterate through the PDF div list and get the width from the 1st div with class p.
     */
    public static double fetchPageWidth(List<PdfDivBean> pageDivList) throws PdfReaderException {
        double pageWidth;

        Optional<PdfDivBean> pageDivOptional = pageDivList.stream().filter(pageDiv -> pageDiv.getDivClass().equals("page")).findFirst();

        if (pageDivOptional.isPresent()) {
            pageWidth = pageDivOptional.get().getWidth();
        } else {
            LOGGER.error("ERROR >> PdfUtil >> fetchPageWidthFrom >> Error while getting the page width.");
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }

        return pageWidth;
    }

    public static List<PdfDivBean> getAllFieldNameDivs(List<PdfDivBean> pageDivList) {
        return pageDivList.stream()
                .filter(pageDiv -> null != pageDiv.getFontWeight() && pageDiv.getFontWeight().equals("bold"))
                .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator())
                .collect(Collectors.toList());
    }

    /**
     * @param pageDivList      - List of all the divs in the page.
     * @param maxLeftAttrValue - maximum left attribute value.
     * @param topOfNextRow     - top attribute value of the next row.
     * @return - returns the List of divs with same top and which have left attribute value less than the max left attribute value.
     * <AUTHOR> Nagare
     * @since 22/12/2022
     * </p>
     * This method finds all the divs with same top value and which have left attribute value less than max left attribute value.
     */
    public static List<PdfDivBean> getSameTopDivList(List<PdfDivBean> pageDivList, double maxLeftAttrValue, double topOfNextRow) {
        return pageDivList.stream()
                .filter(pageDiv -> pageDiv.getLeft() < maxLeftAttrValue
                        && pageDiv.getTop() == topOfNextRow)
                .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                .collect(Collectors.toList());
    }

    /**
     * @param targetedDivList  - Divs to search to get the value.
     * @param fieldDivMetadata - Field Div Metadata object.
     * @return - Return the index of the nearest value div for the field value.
     * <AUTHOR> Nagere
     * @since 21/12/2022
     *
     * </p>
     * Steps -
     * 1. Get the div just below the field div.
     * 1. If we get the div return the index of the div as nearest div.
     * 2. If we do not get the div then traverse to the left by 1pt till we get the first div.
     * We will traverse till we reach the mid of distance between the fields.
     * Once we get the div then return the index of the div.
     */
    public static boolean getNearestDivToField(List<PdfDivBean> targetedDivList, PdfFieldDivMetadata fieldDivMetadata) {
        boolean nearestFieldFound = false;

        //Get the div just below the field div.
        for (int i = 0; i < targetedDivList.size(); i++) {
            PdfDivBean pageDiv = targetedDivList.get(i);
            if (pageDiv.getDivClass().equals("p")
                    && pageDiv.getLeft() >= fieldDivMetadata.getFieldStartDiv().getLeft()
                    && pageDiv.getLeft() < NumberUtil.addDoubles(fieldDivMetadata.getFieldEndDiv().getLeft(), fieldDivMetadata.getFieldEndDiv().getWidth())) {
                fieldDivMetadata.setNearestDivIndex(i);
                nearestFieldFound = true;
                break;
            }
        }

        //If we do not get the div
        if (!nearestFieldFound || null != fieldDivMetadata.getFieldWrapperDiv()) {
            //traverse to the left by 1pt till we get the first div,
            //till we get the div or we reach the mid of the distance between the Previous field and current field div.

            double minLeftAttrValue;
            double divLeftVal;
            if(null != fieldDivMetadata.getFieldWrapperDiv()){
                minLeftAttrValue = fieldDivMetadata.getFieldWrapperDiv().getLeft();
                divLeftVal = fieldDivMetadata.getFieldWrapperDiv().getLeft();
            }else {
                minLeftAttrValue = fieldDivMetadata.getPrevFieldDiv() != null ?
                    NumberUtil.subtractDoubles(fieldDivMetadata.getFieldStartDiv().getLeft(),
                        NumberUtil.divideDouble(calculateDistance(fieldDivMetadata.getFieldStartDiv(),
                                fieldDivMetadata.getPrevFieldDiv()), 2)) : 0;
                divLeftVal = NumberUtil.subtractDoubles(fieldDivMetadata.getFieldStartDiv().getLeft(), 1);
            }

            do {
                for (int i = 0; i < targetedDivList.size(); i++) {
                    PdfDivBean pageDiv = targetedDivList.get(i);
                    if (pageDiv.getDivClass().equals("p")
                            && pageDiv.getLeft() >= divLeftVal
                            && pageDiv.getLeft() < NumberUtil.addDoubles(fieldDivMetadata.getFieldStartDiv().getLeft(), fieldDivMetadata.getFieldStartDiv().getWidth())) {
                        fieldDivMetadata.setNearestDivIndex(i);
                        nearestFieldFound = true;
                        break;
                    }
                }

                if (nearestFieldFound) {
                    break;
                }

                divLeftVal = NumberUtil.subtractDoubles(divLeftVal, 1);

            } while (divLeftVal >= minLeftAttrValue);
        }

        return nearestFieldFound;
    }

    public static int getStartingValueDivIndex(List<PdfDivBean> targetedValueDivs, int nearestDivIndex) {
        int startingValDivIndex = 0;
        //Get the previous div until we get a div with differance between 2 divs is not equals to the width of a space.
        for (int i = nearestDivIndex; i >= 0; i--) {
            if (i != 0) {
                PdfDivBean pdfDivBean = targetedValueDivs.get(i);
                PdfDivBean previousDiv = targetedValueDivs.get(i - 1);

                if (!checkIfDivHaveSpaceBetween(previousDiv, pdfDivBean)) {
                    startingValDivIndex = i;
                    break;
                }
            }
        }

        return startingValDivIndex;
    }

    public static boolean checkIfDivHaveSpaceBetween(PdfDivBean previousDivBean, PdfDivBean pdfDivBean) {
        //TODO - Mrunal Nagare - 21/12/2022 - need to check the value and correct accordingly.
        double distance = NumberUtil.formatToTwoDecimalPrecision(calculateDistance(previousDivBean, pdfDivBean));
        return distance <= 4.5;
    }

    /**
     * @param div1 - First div.
     * @param div2 - Second div.
     * @return - Returns the distance between the 2 divs.
     * <AUTHOR> Nagare
     * @since 21/12/2022
     * </p>
     * This method calculates the distance between the 2 divs using the left attribute and width of the div.
     */
    public static double calculateDistance(PdfDivBean div1, PdfDivBean div2) {
        double distance;
        if (div1.getLeft() > div2.getLeft()) {
            distance = NumberUtil.subtractDoubles(div1.getLeft(), NumberUtil.addDoubles(div2.getLeft(), div2.getWidth()));
        } else {
            distance = NumberUtil.subtractDoubles(div2.getLeft(), NumberUtil.addDoubles(div1.getLeft(), div1.getWidth()));
        }
        return distance;
    }

    public static long getTotalPageCount(RandomAccessFile fileReader) {
        PdfDivBean pdfDivBean;
        long pageCount = 0;

        try {
            fileReader.seek(0);
            //Go to the start of the page
            do {
                String line = fileReader.readLine();

                if (StringUtils.isBlank(line)) {
                    break;
                }

                pdfDivBean = PdfUtil.parseDataPdfHtmlDiv(line);

                if(null != pdfDivBean && pdfDivBean.getDivClass().equals("page")){
                    String [] pageDivArr = pdfDivBean.getId().split("_");
                    if(pageDivArr.length > 1) {
                        pageCount = Long.parseLong(pageDivArr[1]) + 1;
                    }
                }
            } while (true);
        } catch (EOFException eofException) {
            LOGGER.error("ERROR >> PdfUtil >> getAllRowsForThePage >> We have reached the end of file", eofException);
        } catch (IOException e) {
            LOGGER.error("ERROR >> PdfUtil >> getAllRowsForThePage >> ", e);
        }
        return pageCount;
    }
}
