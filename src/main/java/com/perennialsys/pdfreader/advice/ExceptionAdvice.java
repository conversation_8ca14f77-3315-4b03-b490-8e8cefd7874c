package com.perennialsys.pdfreader.advice;

import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class ExceptionAdvice {

    private static final Logger LOG = Logger.getLogger(ExceptionAdvice.class);

    @ExceptionHandler(PdfReaderException.class)
    public ResponseEntity<Map<String, Object>> globalExceptionHandler(PdfReaderException e) {
        LOG.error("ERROR_OCCURRED", e);
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error-code", e.getErrorCode());
        errorResponse.put("error-message", e.getErrorMessge());
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> globalExceptionHandler(Exception e) {
        LOG.error("UNEXPECTED_ERROR_OCCURRED", e);
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error-code", ResponseCode.UNABLE_TO_PROCESS_REQUEST);
        errorResponse.put("error-message", ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(SubscriptionException.class)
    public ResponseEntity<Map<String, Object>> authExceptionHandler(SubscriptionException e) {
        LOG.error("AUTH_ERROR_OCCURED", e);
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error-code", e.getErrorCode());
        errorResponse.put("error-message", e.getErrorMessge());
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
