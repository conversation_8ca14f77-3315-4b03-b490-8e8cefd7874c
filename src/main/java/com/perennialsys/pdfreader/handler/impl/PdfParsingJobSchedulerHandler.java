package com.perennialsys.pdfreader.handler.impl;

import com.perennialsys.pdfreader.db.mongo.MongoDao;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.enums.TransactionType;
import com.perennialsys.pdfreader.handler.IReportsHandler;
import com.perennialsys.pdfreader.helper.SftpHandler;
import com.perennialsys.pdfreader.processor.ConcurrentPdfParsingProcessor;
import com.perennialsys.pdfreader.processor.ConcurrentReportGenerationProcessor;
import com.perennialsys.pdfreader.processor.ConcurrentSftpPdfParsingProcessor;
import com.perennialsys.pdfreader.processor.ExportReportProcessor;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.subscriptionService.handler.IMeteringHandler;
import com.perennialsys.pdfreader.util.AppConfig;
import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Component
public class PdfParsingJobSchedulerHandler {

    private static final Logger LOG = Logger.getLogger(PdfParsingJobSchedulerHandler.class);

    private final IService service;
    private final MongoDao mongoDao;
    private final ExportReportProcessor exportReportProcessor;
    private final SftpHandler sftpHandler;
    private final TransactionHandler txnHandler;
    private final IReportsHandler reportsHandler;
    private final IMeteringHandler meteringHandler;

    @Autowired
    PdfParsingJobSchedulerHandler(IService service, MongoDao mongoDao, ExportReportProcessor exportReportProcessor,
                                  SftpHandler sftpHandler, TransactionHandler txnHandler, IReportsHandler reportsHandler,
                                  IMeteringHandler meteringHandler){
        this.service = service;
        this.mongoDao = mongoDao;
        this.exportReportProcessor = exportReportProcessor;
        this.sftpHandler = sftpHandler;
        this.txnHandler = txnHandler;
        this.reportsHandler = reportsHandler;
        this.meteringHandler = meteringHandler;
    }

    public void startPdfProcessing() {
        LOG.info("START >> CLASS >> PdfParsingJobSchedulerHandler >> METHOD >> startPdfProcessing");
        List<FileUploadDetailsVO> fileUploadDtlsList = service.getFileUploadDetailsRepo()
            .findAllByStatusInOrderByIdAsc(Collections.singletonList(TransactionStatus.UPLOADED.name()));

        if (fileUploadDtlsList != null && !fileUploadDtlsList.isEmpty()) {
            ExecutorService executorService = Executors.newFixedThreadPool(10);

            //Create PAN level list and pass to the Executor service
            fileUploadDtlsList.stream().map(FileUploadDetailsVO :: getPan).distinct().forEach(pan ->
                    executorService.submit(new ConcurrentPdfParsingProcessor(fileUploadDtlsList.stream()
                    .filter(fileUploadDetailsVO -> fileUploadDetailsVO.getPan().equals(pan))
                    .collect(Collectors.toList()), service, mongoDao, txnHandler, meteringHandler))
            );

            executorService.shutdown();
        }

        LOG.info("END >> CLASS >> PdfParsingJobSchedulerHandler >> METHOD >> startPdfProcessing");
    }

    /**
     * <AUTHOR> Nagar
     * @since 20/06/2023
     *
     * This method will get the sftp file download transaction and start the PDF file processing for them.
     */
    public void startSftpPdfProcessing() {
        LOG.info("START >> CLASS >> PdfParsingJobSchedulerHandler >> METHOD >> startSftpPdfProcessing");
        List<TransactionManagerVO> fileDnldTxnManagerList = service.getTransactionManagerRepo()
            .findAllByStatusAndTypeInOrderByUpdatedAtAsc(TransactionStatus.SFTP_FILE_DOWNLOAD_DONE.name(),
                Collections.singletonList(TransactionType.SFTP_PDF_FILE_PROCESSING.name())
            );

        if (fileDnldTxnManagerList != null && !fileDnldTxnManagerList.isEmpty()) {
            ExecutorService executorService = Executors.newFixedThreadPool(10);

            fileDnldTxnManagerList.stream()
                .map(TransactionManagerVO::getPan)
                .collect(Collectors.toSet())
                .forEach(pan -> {
                    List<TransactionManagerVO> panLevelTxnList = fileDnldTxnManagerList.stream()
                        .filter(txnManager -> txnManager.getPan().equals(pan)).collect(Collectors.toList());

                    executorService.submit(new ConcurrentSftpPdfParsingProcessor(panLevelTxnList, service, mongoDao,
                        exportReportProcessor, sftpHandler, meteringHandler));
                });

            executorService.shutdown();
        }

        LOG.info("END >> CLASS >> PdfParsingJobSchedulerHandler >> METHOD >> startSftpPdfProcessing");
    }

    public void startGenerateReportProcessing() {
        LOG.info("START >> CLASS >> PdfParsingJobSchedulerHandler >> METHOD >> startGenerateReportProcessing");
        Pageable pageable = PageRequest.of(0,  Integer.parseInt(AppConfig.getAppProperty(
                "max.allowed.processing.export.files", "5")));

        List<ExportReportsManagerVO> exportReportsManagerVOList = service.getReportsManagerRepo()
                .findAllByStatusOrderByIdAsc(TransactionStatus.REPORT_READY_FOR_GENERATION.name(), pageable);

        if (exportReportsManagerVOList != null && !exportReportsManagerVOList.isEmpty()) {
            ExecutorService executorService = Executors.newFixedThreadPool(10);

            exportReportsManagerVOList.forEach(exportReportsManagerVO ->
                    executorService.submit(new ConcurrentReportGenerationProcessor(exportReportsManagerVO, service,
                    reportsHandler))
            );

            executorService.shutdown();
        }else {
            LOG.error("ERROR >> CLASS >> PdfParsingJobSchedulerHandler >> METHOD >> startGenerateReportProcessing >>" +
                " No ready to generate request found.");
            return;
        }

        LOG.info("END >> CLASS >> PdfParsingJobSchedulerHandler >> METHOD >> startGenerateReportProcessing >>" +
            " Started report generation for " + exportReportsManagerVOList.size() + " requests.");
    }
}
