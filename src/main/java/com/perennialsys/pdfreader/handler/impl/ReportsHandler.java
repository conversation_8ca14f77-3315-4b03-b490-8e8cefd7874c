package com.perennialsys.pdfreader.handler.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.perennialsys.pdfreader.bean.TenantStore;
import com.perennialsys.pdfreader.constants.PdfReaderApiHeaders;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.db.mongo.MongoDbCursor;
import com.perennialsys.pdfreader.dto.ExportHistoryDetailsDto;
import com.perennialsys.pdfreader.dto.ExportRequestDetailsDto;
import com.perennialsys.pdfreader.dto.GenerateReportDetailsDto;
import com.perennialsys.pdfreader.dto.PaginatedResponseDto;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.IReportsHandler;
import com.perennialsys.pdfreader.helper.ResponseHelper;
import com.perennialsys.pdfreader.processor.ExportReportProcessor;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class ReportsHandler implements IReportsHandler {

    private final Logger LOG = Logger.getLogger(ReportsHandler.class);
    private final IService service;

    private final ExportReportProcessor exportReportProcessor;

    private final TenantStore tenantStore;

    @Autowired
    public ReportsHandler(IService service, ExportReportProcessor exportReportProcessor, TenantStore tenantStore){
        this.service = service;
        this.exportReportProcessor = exportReportProcessor;
        this.tenantStore = tenantStore;
    }

    /**
     * @param pan            PAN number
     * @param txnId
     * @param fileIdList     Export request details DTO
     * @param description
     * @param exportFileType Export file format
     * @param reportType     Report to be exported
     * @throws PdfReaderException Throws Exim exception.
     * <AUTHOR> Nagare
     * @since 07-11-2022
     */
    @Override
    public File exportReportForFileTxn(String pan, String txnId, String startPeriod, String endPeriod, 
                                       boolean returnFileStream, List<String> fileIdList, String description, 
                                       String exportFileType, String reportType) throws PdfReaderException {
        LOG.info("START >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> FILE_TXN_ID >> " +
                fileIdList + " >> REPORT_TYPE >> " + reportType);
        File file;
        switch (PdfFileType.valueOf(exportFileType)) {
            case SHIPPING_BILL: {
                file = exportReportProcessor.exportSbDetailsRecordFile(pan,
                        txnId, fileIdList, startPeriod, endPeriod, false, description, PdfFileType.valueOf(exportFileType)
                );
                break;
            }
            case BILL_OF_ENTRY: {
                file = exportReportProcessor.exportBoeDetailsRecordFile(pan,
                        txnId, fileIdList, startPeriod, endPeriod, false, description, PdfFileType.valueOf(exportFileType)
                );
                break;
            }
            case BILL_OF_ENTRY_DUTY_DRAWBACK:
            case BILL_OF_ENTRY_MOOWR: {
                MongoDbCursor<Document> cursor = service.getBoeDetailsDocuments(pan, fileIdList);

                file = exportReportProcessor.exportReportFile(pan,
                        txnId, null, null, null, false, description, cursor, PdfFileType.valueOf(exportFileType)
                );
                break;
            }
            default: {
                LOG.error("ERROR >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> FILE_TXN_ID >> " +
                    fileIdList + " >> REPORT_TYPE >> " + reportType + " >> " +
                    ResponseMessage.INVALID_FILE_TYPE);
                throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_FILE_TYPE);
            }
        }

        LOG.info("END >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> FILE_TXN_ID >> " +
            fileIdList + " >> REPORT_TYPE >> " + reportType);
        return file;
    }

    /**
     * @param pan                   PAN number
     * @param reportType            Report to be exported
     * @param requestDetails        Export request details DTO
     * @param exportFileType        Export file format
     * @throws PdfReaderException   Throws Pdf Reader Exception
     * <AUTHOR> Nagare
     * @since                       15-11-2022
     * @description                 </p>This method gets the sub txn Ids from which we have to generate the report and
     *                              adds the entry in the Export report manager to be picked by the generate report
     *                              scheduler for report generation.
     */
    @Override
    public Map<String, Object> generateReportForFileTxnOrPeriod(String pan, String reportType, String startPeriod, String endPeriod,
                                                                ExportRequestDetailsDto requestDetails, String exportFileType
    ) throws PdfReaderException {

        LOG.info("START >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> REPORT_TYPE >> " + reportType +
                " >> FILE_TXN_ID_LIST >> " + requestDetails.getFileIdList()+ " >> START_PERIOD >> " + startPeriod +
                " >> END_PERIOD >> " + endPeriod);
        Map<String, Object> result = new HashMap<>();
        String fileGenerationId;
        List<String> fileIdList =  requestDetails.getFileIdList();
        if((null == fileIdList || fileIdList.isEmpty())) {
            if (StringUtils.isNotBlank(startPeriod) && StringUtils.isNotBlank(endPeriod)) {
                //Get the list of file txn Ids form the start and end period
                fileIdList = getSubTxnIdList(pan, exportFileType, startPeriod, endPeriod);
            }else {
                LOG.error("ERROR >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> REPORT_TYPE >> " + reportType +
                        " >> FILE_TXN_ID_LIST >> " + requestDetails.getFileIdList()+ " >> START_PERIOD >> " + startPeriod +
                        " >> END_PERIOD >> " + endPeriod + " >> Missing fileIds or start and end period");
                throw new PdfReaderException(ResponseCode.MISSING_MANDATORY_HEADERS, ResponseMessage.MISSING_MANDATORY_HEADERS);
            }
        }

        fileGenerationId = createExportReportEntry(pan, fileIdList, PdfFileType.valueOf(exportFileType), startPeriod,
                endPeriod, requestDetails.getDescription());
        result.put(PdfReaderApiHeaders.FILE_ID, fileGenerationId);

        LOG.info("END >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> REPORT_TYPE >> " + reportType +
                " >> FILE_TXN_ID_LIST >> " + requestDetails.getFileIdList()+ " >> START_PERIOD >> " + startPeriod +
                " >> END_PERIOD >> " + endPeriod);
        return ResponseHelper.success(result, ResponseMessage.REPORT_GENERATION_STARTED_SUCCESSFULLY);
    }

    /**
     * <AUTHOR> Nagare
     * @since                       15-11-2023
     * @description                 </p>This method gets all the sub transaction Ids form mongo db based on the file type
     *                              and start and end period.
     * @param pan                   PAN
     * @param fileType              File Type
     * @param startPeriod           Start Period
     * @param endPeriod             End Period
     * @return                      Returns list of Sub transaction Ids
     * @throws PdfReaderException   Throws Pdf Reader Exception
     */
    private List<String> getSubTxnIdList(String pan, String fileType, String startPeriod, String endPeriod
    ) throws PdfReaderException{
        LOG.info("START >> ReportsHandler >> getSubTxnIdList >> PAN >> " + pan + " >> REPORT_TYPE >> " + fileType +
                " >> START_PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod);

        List<String> subTxnIdList;
        Date startPeriodDate = DateFormatUtil.convertMMYYYYToDate(startPeriod);
        Date endPeriodDate = DateFormatUtil.getLastDaydate(DateFormatUtil.convertMMYYYYToDate(endPeriod));

        switch (PdfFileType.valueOf(fileType)) {
            case SHIPPING_BILL: {
                subTxnIdList = service.getShippingBillSubTxnIds(pan, startPeriodDate, endPeriodDate);
                break;
            }
            case BILL_OF_ENTRY:
            case BILL_OF_ENTRY_DUTY_DRAWBACK:
            case BILL_OF_ENTRY_MOOWR:{
                subTxnIdList = service.getBoeSubTxnIds(pan, startPeriodDate, endPeriodDate);
                break;
            }
            default: {
                LOG.error("START >> ReportsHandler >> getSubTxnIdList >> PAN >> " + pan + " >> REPORT_TYPE >> " + fileType +
                        " >> START_PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> Invalid file type");
                throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_FILE_TYPE);
            }
        }

        LOG.info("END >> ReportsHandler >> getSubTxnIdList >> PAN >> " + pan + " >> REPORT_TYPE >> " + fileType +
                " >> START_PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod);
        return subTxnIdList;
    }

    /**
     * <AUTHOR> Nagare
     * @since                       15-11-2023
     * @description                 </p>This method gets all the sub transaction Ids from the transaction Id and
     *                              adds an entry in the Export report manager VO which is then picked by the
     *                              generate report scheduler for report generation.
     * @param pan                   PAN
     * @param txnID                 Transaction Id
     * @param reportType            Report type
     * @param requestDetails        Request details DTO
     * @param exportFileType        Export file type
     * @return                      Returns file generation Id
     * @throws PdfReaderException   Throws PDF Reader Exception
     */
    @Override
    public Map<String, Object> generateReportForTxnId(String pan, String txnID, String reportType,
                                                      ExportRequestDetailsDto requestDetails, String exportFileType
    ) throws PdfReaderException {
        LOG.info("START >> ReportsHandler >> generateReportForTxnId >> PAN >> " + pan + " >> TXN_ID >> " + txnID +
                " >> REPORT_TYPE >> " + reportType);

        String description = null != requestDetails ? requestDetails.getDescription() : null;

        List<Object[]> fileTxnList = service.getFileUploadDetailsRepo().getSubTxnIdsByTxnIDPanAndFileTypeAndStatus(txnID, pan,
                reportType, TransactionStatus.PDF_PARSING_COMPLETE.name(), false);
        Map<String, Object> result = new HashMap<>();

        if(null != fileTxnList && !fileTxnList.isEmpty()) {
            String fileGenerationId = createExportReportEntry(pan, fileTxnList.stream().map(obj -> obj[0].toString())
                            .collect(Collectors.toList()), PdfFileType.valueOf(exportFileType), null,
                    null, description);

            result.put(PdfReaderApiHeaders.FILE_ID, fileGenerationId);
        }else {
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.NO_FILES_FOUND_FOR_TXN);
        }

        LOG.info("END >> ReportsHandler >> generateReportForTxnId >> PAN >> " + pan + " >> TXN_ID >> " + txnID +
                " >> REPORT_TYPE >> " + reportType);

        return ResponseHelper.success(result, ResponseMessage.REPORT_GENERATION_STARTED_SUCCESSFULLY);
    }

    /**
     * <AUTHOR> Nagare
     * @since                       15-11-2023
     * @description
     *                              </p>This method will create an entry in the export report manager VO based on
     *                              the details passed. This entry will be picked by the generate report scheduler
     *                              for report generation.
     *
     * @param pan                   PAN
     * @param fileIdList            File ID list
     * @param fileType              File Type to be exported
     * @param startPeriod           Start period
     * @param endPeriod             End Period
     * @param description           Description of the export
     * @return                      Returns the File Generation Id. This Id can be used to download the report
     *                              once generated using the export report API
     * @throws PdfReaderException   Throws Pdf Reader Exception
     */
    private String createExportReportEntry(String pan, List<String> fileIdList, PdfFileType fileType,
                                        String startPeriod, String endPeriod, String description) throws PdfReaderException{
        LOG.info("START >> CLASS >> ExportReportProcessor >> METHOD >> createExportReportEntry >> PAN " + pan +
                " >> FILE_TYPE >> " + fileType.name() + " >> FILE_ID_LIST >> " + fileIdList +" START_PERIOD >> " +
                startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " + description);
        String fileGenerationId;
        if (null != fileIdList && !fileIdList.isEmpty()) {
            //Add entry into the export report manager VO so that it will be picked by the scheduler for report generation.
            GenerateReportDetailsDto generateReportDetailsDto = new GenerateReportDetailsDto(description, fileIdList.size(),
                    fileIdList, startPeriod, endPeriod, null, 0);

            ExportReportsManagerVO exportReportsManagerVO = service.checkAndAddEntryIntoExportReportManager(pan,
                    UUID.randomUUID().toString(), fileType.name(), null,
                    TransactionStatus.REPORT_READY_FOR_GENERATION.name(), null,
                    generateReportDetailsDto);

            fileGenerationId = exportReportsManagerVO.getFileId();
        } else {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> createExportReportEntry >> PAN " + pan +
                    " >> FILE_TYPE >> " + fileType.name() + " >> FILE_ID_LIST >> " + fileIdList +" START_PERIOD >> " +
                    startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " + description +
                    " >> File ID list is empty");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_UPLD_DTLS_NOT_FOUND);
        }

        LOG.info("END >> CLASS >> ExportReportProcessor >> METHOD >> createExportReportEntry >> PAN " + pan +
                " >> FILE_TYPE >> " + fileType.name() + " >> FILE_ID_LIST >> " + fileIdList +" START_PERIOD >> " +
                startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " + description);

        return fileGenerationId;
    }

    @Override
    public File exportReportForTxnId(String pan, String txnId, String reportType, boolean returnFileStream,
                                                  ExportRequestDetailsDto requestDetails, String exportFileType)
            throws PdfReaderException {
        LOG.info("START >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> TXN_ID >> " + txnId +
            " >> REPORT_TYPE >> " + reportType);
        File file;
        String description = null != requestDetails ? requestDetails.getDescription() : null;
        List<Object[]> fileTxnList = service.getFileUploadDetailsRepo().getSubTxnIdsByTxnIDPanAndFileTypeAndStatus(txnId, pan,
                reportType, TransactionStatus.PDF_PARSING_COMPLETE.name(), false);

        switch (PdfFileType.valueOf(exportFileType)) {
            case SHIPPING_BILL: {
                file = exportReportProcessor.exportSbDetailsRecordFile(pan, null, fileTxnList.stream().map(obj -> obj[0].toString())
                                .collect(Collectors.toList()), null, null, false, description, PdfFileType.valueOf(exportFileType));
                break;
            }
            case BILL_OF_ENTRY: {
                file = exportReportProcessor.exportBoeDetailsRecordFile(pan,
                        null, fileTxnList.stream().map(obj -> obj[0].toString())
                                .collect(Collectors.toList()), null, null, false, description, PdfFileType.valueOf(exportFileType));
                break;
            }
            case BILL_OF_ENTRY_DUTY_DRAWBACK:
            case BILL_OF_ENTRY_MOOWR: {
                MongoDbCursor<Document> cursor = service.getBoeDetailsDocuments(pan,
                        fileTxnList.stream().map(obj -> obj[0].toString()).collect(Collectors.toList()));

                file = exportReportProcessor.exportReportFile(pan,
                        txnId, null, null, null, false, description, cursor, PdfFileType.valueOf(exportFileType)
                );
                break;
            }
            default: {
                LOG.error("ERROR >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> TXN_ID >> " + txnId +
                    " >> REPORT_TYPE >> " + reportType+" >> "+ResponseMessage.INVALID_FILE_TYPE);
                throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_FILE_TYPE);
            }
        }

        LOG.info("END >> ReportsHandler >> exportReports >> PAN >> " + pan + " >> TXN_ID >> " + txnId + " >> REPORT_TYPE >> "
            + reportType);
        return file;
    }

    @Override
    public File exportReport(String pan, String reportGenerationId, String reportType, String startPeriod,
                                          String endPeriod, boolean returnFileStream,
                                          ExportRequestDetailsDto requestDetails, String exportFileType) throws PdfReaderException {
        LOG.info("START >> ReportsHandler >> exportReport >> PAN >> " + pan + " >> REPORT_GENERATION_ID >> " +
            reportGenerationId + " >> REPORT_TYPE >> " + reportType + " >> START_PERIOD >> " + startPeriod +
            " >> END_PERIOD >> "+endPeriod);
        File file;
        String description = null != requestDetails ? requestDetails.getDescription() : null;
        switch (PdfFileType.valueOf(exportFileType)) {
            case SHIPPING_BILL: {
                file = exportReportProcessor.exportSbDetailsRecordFile(pan,
                        PdfFileType.valueOf(exportFileType), reportGenerationId, startPeriod, endPeriod, false,
                        description);
                break;
            }
            case BILL_OF_ENTRY: {
                file = exportReportProcessor.exportBoeDetailsRecordFile(pan,
                        PdfFileType.valueOf(exportFileType), reportGenerationId, startPeriod, endPeriod, false,
                        description);
                break;
            }
            case BILL_OF_ENTRY_DUTY_DRAWBACK:
            case BILL_OF_ENTRY_MOOWR: {
                MongoDbCursor<Document> cursor = null;
                if(StringUtils.isBlank(reportGenerationId)) {
                    LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportReport >>" +
                            " Report Generation Id is missing >> Data Not found");
                    throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.MISSING_MANDATORY_HEADERS);
                } else if (StringUtils.isBlank(startPeriod) && StringUtils.isBlank(endPeriod)) {
                    LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportReport >>" +
                            " Report Generation Id is missing >> Data Not found");
                    throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.MISSING_MANDATORY_HEADERS);
                }

                if(StringUtils.isBlank(reportGenerationId) &&
                    StringUtils.isNotBlank(startPeriod) && StringUtils.isNotBlank(endPeriod)){
                    //Get the BOE details based on the start and end period
                    Date startPeriodDate = DateFormatUtil.convertMMYYYYToDate(startPeriod);
                    Date endPeriodDate = DateFormatUtil.getLastDaydate(DateFormatUtil.convertMMYYYYToDate(endPeriod));

                    cursor = service.getBoeDetailsDocuments(pan, startPeriodDate, endPeriodDate);
                }

                file = exportReportProcessor.exportReportFile(pan, null, reportGenerationId, null, null, false,
                        description, cursor, PdfFileType.valueOf(exportFileType));
                break;
            }
            default: {
                LOG.error("ERROR >> ReportsHandler >> exportReport >> PAN >> " + pan + " >> REPORT_GENERATION_ID >> " +
                        reportGenerationId + " >> REPORT_TYPE >> " + reportType + " >> START_PERIOD >> " +
                        startPeriod + " >> END_PERIOD >> " + endPeriod + " >> "+ResponseMessage.INVALID_FILE_TYPE);
                throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_FILE_TYPE);
            }
        }

        LOG.info("END >> ReportsHandler >> exportReport >> PAN >> " + pan + " >> FILE_TXN_ID >> " + reportGenerationId +
                " >> REPORT_TYPE >> " + reportType);
        return file;
    }

    /**
     * @param pan PAN
     * @param reportType Report Type
     * @param pageNo Page No
     * @param limit Limit
     * @return Returns the Export Report History
     * <AUTHOR> Nagare
     * @since 11/08/2023
     * @description </p>This method returns the export report history from the export manager table
     * for the provided pan and report type.
     */
    @Override
    public Map<String, Object> getExportReportHistory(String pan, String reportType, int pageNo, int limit,
                                                      String searchKey, String searchValue,String sortBy,
                                                      short sortingOrder) throws PdfReaderException {
        LOG.info("START >> ReportsHandler >> exportReport >> PAN >> " + pan + " >> FILE_TXN_ID >> " + reportType);
        PaginatedResponseDto<ExportHistoryDetailsDto> paginatedResponseDto = new PaginatedResponseDto<>();
        List<ExportHistoryDetailsDto> exportHistoryDtoList = new ArrayList<>();

        List<String> reportTypeList = PdfFileType.BILL_OF_ENTRY.name().equals(reportType)
                ? Arrays.asList(PdfFileType.BILL_OF_ENTRY.name(), PdfFileType.BILL_OF_ENTRY_DUTY_DRAWBACK.name(),
                    PdfFileType.BILL_OF_ENTRY_MOOWR.name())
                : Collections.singletonList(reportType);

        List<ExportReportsManagerVO> exportReportManagerVoList = service.getExportReportManagerVoList(pan,
                reportTypeList, pageNo, limit, searchKey, searchValue, sortBy, sortingOrder);

        if(null != exportReportManagerVoList && !exportReportManagerVoList.isEmpty()){
            //Create the Export history Details Dto List
            exportReportManagerVoList.stream().map(exportReportManager -> {
                ExportHistoryDetailsDto historyDetailsDto = ExportHistoryDetailsDto.builder()
                        .exportTime(DateFormatUtil.convertDateToStringAndFormat(exportReportManager.getCreatedAt(),
                                DateFormatUtil.ddMMYYYY_Hyphen_with_time))
                        .exportedBy(exportReportManager.getUserName())
                        .exportId(exportReportManager.getFileId())
                        .remark(exportReportManager.getRemark())
                        .build();

                if(StringUtils.isNotBlank(exportReportManager.getOtherDetails())){
                    try{
                        GenerateReportDetailsDto generateReportDetailsDto = new ObjectMapper()
                                .readValue(exportReportManager.getOtherDetails(),
                                GenerateReportDetailsDto.class);

                        if(StringUtils.isNotBlank(generateReportDetailsDto.getStartPeriod()) &&
                                StringUtils.isNotBlank(generateReportDetailsDto.getEndPeriod())){

                            historyDetailsDto.setExportPeriod(generateReportDetailsDto.getStartPeriod() +
                                    " to "+ generateReportDetailsDto.getEndPeriod());
                        }

                        if(generateReportDetailsDto.getFileCount() > 0){
                            historyDetailsDto.setFileCount(generateReportDetailsDto.getFileCount());
                        }
                    }catch (JSONException e){
                        LOG.error("ERROR >> ReportsHandler >> exportReport >> PAN >> " + pan + " >> FILE_TXN_ID >> " +
                                reportType + " >> OTHER_DETAILS >> "+exportReportManager.getOtherDetails()+
                                " >> Error while getting other details from export report manager.");
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }

                historyDetailsDto.setStatus(TransactionStatus.valueOf(exportReportManager.getStatus()).getDisplayName());

                return historyDetailsDto;
            }).forEach(exportHistoryDtoList::add);

            paginatedResponseDto.setRecords(exportHistoryDtoList);
            paginatedResponseDto.setTotalRecords(service.getExportReportManagerVoCount(pan, reportTypeList, searchKey, searchValue));
        }

        LOG.info("END >> ReportsHandler >> exportReport >> PAN >> " + pan + " >> FILE_TXN_ID >> " + reportType);
        return ResponseHelper.success(paginatedResponseDto);
    }

    public Map<String, Object> editExportRemark(String pan, String reportGenId,  ExportRequestDetailsDto requestDetails
    ) throws PdfReaderException {
        LOG.info("START >> ReportsHandler >> editExportRemark >> PAN >> " + pan + " >> FILE_TXN_ID >> " + reportGenId);

        ExportReportsManagerVO exportReportManager = service.getReportsManagerRepo()
                .findByFileIdAndPan(reportGenId, pan);

        if(null != exportReportManager){
            //Create the Export history Details Dto List
            String remark;

            if(null == requestDetails || StringUtils.isBlank(requestDetails.getDescription())){
                remark = null;
            }else {
                remark = requestDetails.getDescription();
            }

            exportReportManager.setRemark(remark);
            service.getReportsManagerRepo().save(exportReportManager);
        }else {
            LOG.error("ERROR >> ReportsHandler >> editExportRemark >> PAN >> " + pan + " >> FILE_TXN_ID >> " +
                    reportGenId + ">> Export report details not found for provided file ID.");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_EXPORT_DETAILS_NOT_FOUND);
        }

        LOG.info("END >> ReportsHandler >> editExportRemark >> PAN >> " + pan + " >> FILE_TXN_ID >> " + reportGenId);
        return ResponseHelper.success(ResponseMessage.REMARK_UPDATED_SUCCESSFULLY);
    }
}
