package com.perennialsys.pdfreader.handler.impl;

import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.dto.FieldIdentificationStrategyDetailsDto;
import com.perennialsys.pdfreader.dto.FieldIdentificationStrategyDto;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.IPdfFieldIdentificationStrategyHandler;
import com.perennialsys.pdfreader.helper.ResponseHelper;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.FieldIdentificationStrategyVO;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class PdfFieldIdentificationStrategyHandler implements IPdfFieldIdentificationStrategyHandler {

    private final Logger LOG = Logger.getLogger(PdfFieldIdentificationStrategyHandler.class);
    private final IService service;

    @Autowired
    public PdfFieldIdentificationStrategyHandler(IService service){
        this.service = service;
    }


    /**
     * @param pan         PAN
     * @param strategyDto Field Identification Strategy details
     * @return Returns Map
     * @throws PdfReaderException Throws PDF Reader Exception
     * <AUTHOR> Nagare
     * @description </p>This Method sets the field identification strategy for the pan and file type.
     * <p>
     * Steps -
     * <p>1. Check if the strategy for the field is already present.
     * <p> 1.1 if YES - Update the existing entry.
     * <p> 1.2 id NO - Create a new entry.
     * <p>2. Return all the field identification strategies.
     * @since 06/07/2023
     */
    @Override
    public Map<String, Object> setFieldIdentificationStrategy(String pan,
                                                              FieldIdentificationStrategyDto strategyDto) throws PdfReaderException {
        LOG.info("START >> PdfParsingHandler >> setFieldIdentificationStrategy >> PAN >> " + pan + " >> FILE_TYPE >> "
                + strategyDto.getFileType());

        FieldIdentificationStrategyVO strategyVO;

        strategyVO = service.getFieldIdentificationStrategyRepo().findFirstByPanAndFileTypeAndTargetField(pan,
                strategyDto.getFileType(), strategyDto.getTargetField());

        if (null == strategyVO) {
            strategyVO = new FieldIdentificationStrategyVO();
            strategyVO.setPan(pan);
            strategyVO.setCreatedAt(new Date());
        } else {
            strategyVO.setUpdatedAt(new Date());
        }

        strategyVO.setFileType(strategyDto.getFileType());
        strategyVO.setTargetField(strategyDto.getTargetField());
        strategyVO.setSourceField(strategyDto.getSourceField());
        strategyVO.setStrategy(strategyDto.getStrategy());
        strategyVO.setTermination(strategyDto.getTermination());
        strategyVO.setPrefixSuffix(strategyDto.getPrefixSuffix());
        strategyVO.setLength(strategyDto.getLength());
        strategyVO.setSpecialChar(strategyDto.getSpecialChar());
        strategyVO.setActive(strategyDto.isActive());

        service.getFieldIdentificationStrategyRepo().save(strategyVO);

        List<FieldIdentificationStrategyDetailsDto> strategyDetailsDtoList =
                getFieldIdentificationStrategyDetails(pan, null);
        LOG.info("END >> PdfParsingHandler >> setFieldIdentificationStrategy >> PAN >> " + pan + " >> FILE_TYPE >> "
                + strategyDto.getFileType());
        return ResponseHelper.success(strategyDetailsDtoList, ResponseMessage.FIELD_IDENTIFICATION_STRATEGY_ADDED_SUCCESSFULLY);
    }

    /**
     * @param pan PAN
     * @return Returns list of active and inactive strategies
     * @throws PdfReaderException Throws PDF Reader Exception
     * <AUTHOR> Nagare
     * @description </p>This method returns all the field identification strategies for the pan. It returns the strategy for
     * particular file type if file type is passed
     * @since 06/07/2023
     */
    @Override
    public Map<String, Object> getFieldIdentificationStrategies(String pan, String fileType) throws PdfReaderException {
        LOG.info("START >> PdfParsingHandler >> getFieldIdentificationStrategies >> PAN >> " + pan);

        List<FieldIdentificationStrategyDetailsDto> strategyDetailsDtoList =
                getFieldIdentificationStrategyDetails(pan, fileType);

        LOG.info("END >> PdfParsingHandler >> getFieldIdentificationStrategies >> PAN >> " + pan);
        return ResponseHelper.success(strategyDetailsDtoList);
    }

    private List<FieldIdentificationStrategyDetailsDto> getFieldIdentificationStrategyDetails(String pan, String fileType) {
        List<FieldIdentificationStrategyVO> strategyVOList;
        List<FieldIdentificationStrategyDetailsDto> strategyDetailsDtoList = new ArrayList<>();

        if (StringUtils.isNotBlank(fileType)) {
            strategyVOList = service.getFieldIdentificationStrategyRepo().findAllByPanAndFileType(pan, fileType);
        } else {
            strategyVOList = service.getFieldIdentificationStrategyRepo().findAllByPan(pan);
        }

        if (null != strategyVOList && !strategyVOList.isEmpty()) {
            if (StringUtils.isNotBlank(fileType)) {
                strategyDetailsDtoList.add(new FieldIdentificationStrategyDetailsDto(fileType, strategyVOList));
            } else {
                strategyVOList.stream()
                        .map(FieldIdentificationStrategyVO::getFileType)
                        .forEach(strategyFileType ->
                                strategyDetailsDtoList.add(
                                        new FieldIdentificationStrategyDetailsDto(strategyFileType, strategyVOList.stream()
                                                .filter(strategyVO -> strategyVO.getFileType().equals(strategyFileType))
                                                .collect(Collectors.toList())
                                        )
                                )
                        );
            }
        }

        return strategyDetailsDtoList;
    }

    @Override
    public Map<String, Object> deleteFieldIdentificationStrategies(String pan, String fileType, String targetField) throws PdfReaderException {
        LOG.info("START >> PdfParsingHandler >> getFieldIdentificationStrategies >> PAN >> " + pan);

        service.getFieldIdentificationStrategyRepo().deleteByPanAndFileTypeAndTargetField(pan, fileType, targetField);

        LOG.info("END >> PdfParsingHandler >> getFieldIdentificationStrategies >> PAN >> " + pan);
        return ResponseHelper.success(ResponseMessage.FIELD_IDENTIFICATION_STRATEGY_DELETED_SUCCESSFULLY);
    }

}
