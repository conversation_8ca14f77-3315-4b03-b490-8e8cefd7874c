package com.perennialsys.pdfreader.handler.impl;

import com.perennialsys.pdfreader.helper.SftpHandler;
import com.perennialsys.pdfreader.processor.ConcurrentSftpFileDownloadProcessor;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.subscriptionService.handler.IMeteringHandler;
import com.perennialsys.pdfreader.vo.SftpDetailsVO;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> <PERSON>e
 * @since 20/06/2023
 *
 * This handler will be used to perform scheduled operations related to SFTP
 */
@Component
public class SftpJobSchedulerHandler {

    private static final Logger LOG = Logger.getLogger(SftpJobSchedulerHandler.class);
    private final IService service;

    private final TransactionHandler txnHandler;

    protected final SftpHandler sftpHandler;
    private IMeteringHandler meteringHandler;

    @Autowired
    SftpJobSchedulerHandler(IService service, TransactionHandler txnHandler, SftpHandler sftpHandler,
                            IMeteringHandler meteringHandler){
        this.service = service;
        this.txnHandler = txnHandler;
        this.sftpHandler = sftpHandler;
        this.meteringHandler = meteringHandler;
    }


    /**
     * <AUTHOR> Nagare
     * @since 20/06/2023
     * </p>
     * This method will check if there are any new filed in the input folder for the SFTP and start the download file
     * job accordingly
     */
    public void checkAndStartSftpFileDownloadJob(int allowedJobs){
        LOG.info("START >> CLASS >> SftpJobSchedulerHandler >> METHOD >> checkAndStartSftpFileDownloadJob");

        //Get all the active SFTP details
        List<SftpDetailsVO> sftpDetailsList = service.getSftpDetailsRepo().findAllByIsActiveOrderByUpdatedAtAsc(true);
        if(null != sftpDetailsList && !sftpDetailsList.isEmpty()){
            ExecutorService executorService = Executors.newFixedThreadPool(10);

            for (SftpDetailsVO sftpDetails : sftpDetailsList){
                if(allowedJobs > 0) {
                    executorService.submit(new ConcurrentSftpFileDownloadProcessor(sftpDetails, service, txnHandler,
                            sftpHandler, meteringHandler));

                    sftpDetails.setUpdatedAt(new Date());
                    service.getSftpDetailsRepo().save(sftpDetails);
                    allowedJobs--;
                }else{
                    break;
                }
            }
            executorService.shutdown();
        }else {
            LOG.debug("INTERMEDIATE >> CLASS >> SftpJobSchedulerHandler >> METHOD >> checkAndStartSftpFileDownloadJob >> No Active SFTP found.");
        }

        LOG.info("END >> CLASS >> SftpJobSchedulerHandler >> METHOD >> checkAndStartSftpFileDownloadJob");
    }
}
