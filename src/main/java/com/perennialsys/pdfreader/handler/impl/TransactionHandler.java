package com.perennialsys.pdfreader.handler.impl;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;

@Component
public class TransactionHandler {

    private final IService service;

    @Autowired
    public TransactionHandler(IService service) {
        this.service = service;
    }

    public TransactionManagerVO addEntryInTransactionManager(String txnId, String pan, String txnType, String subTxnId, String status) throws PdfReaderException {
        TransactionManagerVO transactionManagerVO = new TransactionManagerVO();
        transactionManagerVO.setTxnId(txnId);
        transactionManagerVO.setPan(pan);
        transactionManagerVO.setStatus(status);
        transactionManagerVO.setType(txnType);
        transactionManagerVO.setSubTxnId(subTxnId);
        transactionManagerVO.setCreatedAt(new Date());

        service.addUserDetails(transactionManagerVO);

        return service.getTransactionManagerRepo().save(transactionManagerVO);
    }

    public void updateTransactionManger(String txnId, String txnType, String pan, String status) {
        TransactionManagerVO txnVo = service.getTransactionManagerRepo()
                .getTxnByTxnAndTypeAndPan(txnId, txnType, pan);
        if (txnVo != null) {
            txnVo.setStatus(status);
            txnVo.setUpdatedAt(new Date());

            service.getTransactionManagerRepo().save(txnVo);
        }
    }

    public void updateTransactionManger(String txnId, String txnType, String pan, String status, String OtherDetails,
                                        boolean considerErrorStatus) throws PdfReaderException {
        TransactionManagerVO txnVo = service.getTransactionManagerRepo()
                .getTxnByTxnAndTypeAndPan(txnId, txnType, pan);
        if (txnVo != null) {
            if(!considerErrorStatus || !TransactionStatus.getErrorStatusList().contains(txnVo.getStatus())){
                txnVo.setStatus(status);
                txnVo.setOtherDetails(OtherDetails);
                txnVo.setUpdatedAt(new Date());

                service.getTransactionManagerRepo().save(txnVo);
            }
        }else {
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.TXN_NOT_FOUND);
        }
    }

    public void checkAndUpdateTxnStatusForFileParsing(String txnId, String txnType, String pan, String status, String otherDetails) throws PdfReaderException{

        int ipFileCount = service.getFileUploadDetailsRepo().countByTxnIdAndStatusIn(txnId,
                Collections.singletonList(TransactionStatus.PDF_PARSING_IN_PROGRESS.name()));

        if(ipFileCount <= 0 || !TransactionStatus.PDF_PARSING_COMPLETE.name().equals(status)){
            updateTransactionManger(txnId, txnType, pan, status, otherDetails, true);
        }
    }
}
