package com.perennialsys.pdfreader.handler.impl;

import com.perennialsys.pdfreader.bean.TenantStore;
import com.perennialsys.pdfreader.constants.DbQueries;
import com.perennialsys.pdfreader.constants.MeteringEntities;
import com.perennialsys.pdfreader.constants.MeteringEvents;
import com.perennialsys.pdfreader.constants.MysqlDbQueryFields;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.db.mongo.service.IMongoDbService;
import com.perennialsys.pdfreader.dto.FileDetailsDto;
import com.perennialsys.pdfreader.dto.FileProcessingDetailsDto;
import com.perennialsys.pdfreader.dto.PaginatedResponseDto;
import com.perennialsys.pdfreader.dto.PdfFileTxnDetailsDto;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.BoeType;
import com.perennialsys.pdfreader.enums.FileProcessingStatus;
import com.perennialsys.pdfreader.enums.FileSources;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.enums.SftpOperations;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.enums.TransactionType;
import com.perennialsys.pdfreader.excel.StreamHelper;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.IPdfParsingHandler;
import com.perennialsys.pdfreader.helper.FileHelper;
import com.perennialsys.pdfreader.helper.ResponseHelper;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.subscriptionService.handler.IMeteringHandler;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.KeyValue;
import com.perennialsys.pdfreader.util.PdfUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import org.apache.commons.io.FilenameUtils;
import org.apache.log4j.Logger;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.bson.Document;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class PdfParsingHandler implements IPdfParsingHandler {

    private final Logger LOG = Logger.getLogger(PdfParsingHandler.class);
    private final IService service;
    private final TransactionHandler transactionHandler;
    private final IMeteringHandler meteringHandler;
    private final TenantStore tenantStore;
    private final IMongoDbService mongoDbService;


    @Autowired
    PdfParsingHandler(IService service, TransactionHandler transactionHandler, IMeteringHandler meteringHandler,
                      TenantStore tenantStore, IMongoDbService mongoDbService) {

        this.service = service;
        this.transactionHandler = transactionHandler;
        this.meteringHandler = meteringHandler;
        this.tenantStore = tenantStore;
        this.mongoDbService = mongoDbService;
    }

    /**
     * @param pan               - PAN Number.
     * @param multipartFileList - List Of multipart files
     * @param pdfFileType       - File type being uploaded
     * @return Returns result map
     * @throws PdfReaderException - Throws PDF Reader Exception when unexpected error occurs.
     * @throws IOException        - Throws IO Exception when IO related error occur.
     * <AUTHOR> Nagare
     * @since 29/12/2022
     * </p>
     * @description
     * Steps -<br>
     * 1. Upload file to AMAZON S3 or local based on the property value.<br>
     * 2. Create a txn manager entry with txn type PDF_FILE_PARSING and Status UPLOADED.<br>
     * 3. Create an entry in file upload details with File type status UPLOADED.<br>
     * 4. Return the response to UI.<br>
     */
    @Override
    public Map<String, Object> uploadPdfInvoices(String pan, List<MultipartFile> multipartFileList, PdfFileType pdfFileType)
            throws PdfReaderException, IOException {
        LOG.info("START >> PdfReaderHandler >> uploadPdfInvoices >> PAN >> " + pan);
        Map<String, Object> result = new HashMap<>();
        String folderPathToStoreFile = FileHelper.getLocalFolderPath(pan, FileHelper.FILE_UPLOAD_OPERATION, pdfFileType.name(), false);
        String txnId = UUID.randomUUID().toString();
        transactionHandler.addEntryInTransactionManager(txnId, pan, TransactionType.PDF_FILE_PARSING.name(),
                null, TransactionStatus.READY_TO_UPLOAD.name());
        for (MultipartFile multipartFile : multipartFileList) {
            InputStream fileStream = multipartFile.getInputStream();
            String subTxnId = UUID.randomUUID().toString();

            String fileNameWithExtension = FileHelper.getFileNameWithExtension(pdfFileType.getPdfFileName(), pan);

            // Saving file to Amazon
            String filePath = FileHelper.storeFileAndReturnPath(fileStream, folderPathToStoreFile, fileNameWithExtension,
                    FileHelper.getDynamicS3FilePath(pan, pdfFileType.name(), false));

            FileUploadDetailsVO fileDetails = new FileUploadDetailsVO(pan, fileNameWithExtension, multipartFile.getOriginalFilename(), txnId, pdfFileType.name(),
                    FileProcessingStatus.UPLOADED.name(), filePath, FileSources.API.name());
            fileDetails.setSubTxnId(subTxnId);
            fileDetails.setCreatedAt(new Date());
            fileDetails.setUpdatedAt(new Date());

            service.addUserDetails(fileDetails);

            service.saveFileUploadDetailsVo(fileDetails);

            //update transaction to uploaded
            transactionHandler.updateTransactionManger(txnId, TransactionType.PDF_FILE_PARSING.name(), pan, TransactionStatus.UPLOADED.name());
        }

       /* try {
            if(!multipartFileList.isEmpty()) {
                meteringHandler.addOrUpdateMeteringDetails(pan,
                        PdfFileType.SHIPPING_BILL.equals(pdfFileType) ? MeteringEvents.SB_FILE_UPLD.name() : MeteringEvents.BOE_FILE_UPLD.name(),
                        MeteringEntities.FILE_COUNT.getMeteringEntityKey(), multipartFileList.size(), 0, 0, null);
            }
        }catch (SubscriptionException e){
            LOG.error("ERROR >> PdfReaderHandler >> uploadPdfInvoices >> PAN >> " +pan + " >> ERROR_MESSAGE >> " + e.getErrorMessge());
            throw new PdfReaderException(e.getErrorCode(), e.getErrorMessge());
        }*/

        result.put("txnId", txnId);
        LOG.info("END >> PdfReaderHandler >> uploadPdfInvoices >> PAN >> " + pan);
        return ResponseHelper.success(result, ResponseMessage.FILE_UPLOADED_SUCCESS);

    }

    /**
     * @param pan      PAN Number
     * @param fileType File Type
     * @return List of DTO objects which has been recently updated based on pan number and filetype.
     * Strps
     * 1. Fetching recently updated VOs based on PAN and fileType.
     * 2. Preparing DTO from VOs.
     * 3. Returning List Of DTOs.
     * <AUTHOR> Bhavin Majitia
     */
    @Override
    public List<PdfFileTxnDetailsDto> getFileProcessingDetails(String pan, String fileType) {
        LOG.info("START >> PdfReaderHandler >> getFileProcessingDetails >> PAN >> " + pan + " >> fileType >> " + fileType);
        List<PdfFileTxnDetailsDto> responseBeanList = new ArrayList<>();
        if (fileType.equals(PdfFileType.ALL.name())) {
            List<Object[]> txnDtls;
            for (PdfFileType pdfFileType : PdfFileType.values()) {
                if (!pdfFileType.equals(PdfFileType.ALL)) {
                    txnDtls = service.getTransactionManagerRepo().getLatestTxnFromTypeAndPan(pan, pdfFileType.name());
                    responseBeanList.addAll(getAndPrepareFileParsingDetails(txnDtls, pdfFileType.name()));
                }
            }
        } else {
            List<Object[]> txnDtls = service.getTransactionManagerRepo().getLatestTxnFromTypeAndPan(pan, fileType);
            responseBeanList.addAll(getAndPrepareFileParsingDetails(txnDtls, fileType));
        }
        LOG.info("END >> PdfReaderHandler >> getFileProcessingDetails >> PAN >> " + pan + " >> fileType >> " + fileType);
        return responseBeanList;
    }

    private List<PdfFileTxnDetailsDto> getAndPrepareFileParsingDetails(List<Object[]> txnDtlsList, String fileType) {
        List<PdfFileTxnDetailsDto> responseBeanList = new ArrayList<>();
        if (txnDtlsList != null && !txnDtlsList.isEmpty()) {
            for (Object[] txnDtls : txnDtlsList) {
                responseBeanList.add(prepareFileParsingDetails(txnDtls, fileType));
            }
        }

        return responseBeanList;
    }

    private List<PdfFileTxnDetailsDto> getAndPrepareFileUpldHistoryDetails(List<Object[]> fileUplodaDetailsList, String fileType,
                                                                           String pan, boolean getDeletedRecords) {
        LOG.info("START >> PdfReaderHandler >> getAndPrepareFileUpldHistoryDetails >> PAN >> " +pan+ " >> FILE_TYPE >> "
                + fileType + " >> GET_DELETE_RECORDS >> " + getDeletedRecords);
        List<PdfFileTxnDetailsDto> responseBeanList = new ArrayList<>();
        if (fileUplodaDetailsList != null && !fileUplodaDetailsList.isEmpty()) {
            for (Object[] txnDtls : fileUplodaDetailsList) {
                responseBeanList.add(prepareFileUploadHistoryDetails(txnDtls, fileType));
            }
        }

        if(getDeletedRecords && !responseBeanList.isEmpty()){
            responseBeanList = responseBeanList.stream().filter(response ->
                    response.getFileProcessingDtls().containsKey(TransactionStatus.DELETED.getDisplayKey())
                    && response.getTotalFileCount() == response.getFileProcessingDtls().get(TransactionStatus.DELETED.getDisplayKey()))
                .collect(Collectors.toList());
        }

        LOG.info("START >> PdfReaderHandler >> getAndPrepareFileUpldHistoryDetails >> PAN >> " +pan+ " >> FILE_TYPE >> "
                + fileType + " >> GET_DELETE_RECORDS >> " + getDeletedRecords);
        return responseBeanList;
    }

    private PdfFileTxnDetailsDto prepareFileUploadHistoryDetails(Object[] txnDtls, String fileType) {
        LOG.info("START >> PdfReaderHandler >> prepareFileUploadHistoryDetails >> FILE_TYPE >> " + fileType);
        PdfFileTxnDetailsDto responseBean = new PdfFileTxnDetailsDto();

        responseBean.setFileType(fileType);

        if (txnDtls != null && txnDtls.length > 0) {
            responseBean.setTxnId(txnDtls[0].toString());

            responseBean.setProcessingStatus(txnDtls[2].toString());

            if(null != txnDtls[7]) {
                responseBean.setLastUpdatedBy(txnDtls[7].toString());
            }

            if(txnDtls[3] != null){
                responseBean.setLastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat((Date) txnDtls[4],
                        DateFormatUtil.ddMMYYYY_Hyphen_with_time));
            }else if(txnDtls[4] != null){
                responseBean.setLastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat((Date) txnDtls[4],
                        DateFormatUtil.ddMMYYYY_Hyphen_with_time));
            }

            if (!TransactionStatus.PDF_PARSING_IN_PROGRESS.name().equals(responseBean.getProcessingStatus()) && txnDtls[5] != null) {
                try{
                    JSONObject errorJson = new JSONObject(txnDtls[5].toString());
                    if(errorJson.has("error-display-message")){
                        responseBean.setErrorMessage(errorJson.get("error-display-message").toString());
                    }else {
                        responseBean.setErrorMessage(ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                    }
                }catch (JSONException e){
                    responseBean.setErrorMessage(ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                }
            }

            setFileCountByStatus(responseBean);
        }
        LOG.info("END >> PdfReaderHandler >> prepareFileUploadHistoryDetails >> FILE_TYPE >> " + fileType);
        return responseBean;
    }

    private PdfFileTxnDetailsDto prepareFileParsingDetails(Object[] txnDtls, String fileType) {
        LOG.info("START >> PdfReaderHandler >> prepareFileParsingDetails >> fileType >> " + fileType);
        PdfFileTxnDetailsDto responseBean = new PdfFileTxnDetailsDto();
        if (txnDtls == null || txnDtls.length <= 0) {
            responseBean.setFileType(fileType);
            responseBean.setFileName(PdfFileType.valueOf(fileType).getDisplayName());
        } else {
            responseBean.setFileType(fileType);
            responseBean.setFileName(txnDtls[1].toString());
            responseBean.setProcessingStatus(txnDtls[2].toString());

            if (txnDtls[3] != null) {
                responseBean.setLastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat((Date) txnDtls[3],
                        DateFormatUtil.ddMMYYYY_Hyphen_with_time));
            }

            responseBean.setTxnId(txnDtls[4].toString());
            responseBean.setTxnType(txnDtls[5].toString());

            if (!TransactionStatus.PDF_PARSING_IN_PROGRESS.name().equals(responseBean.getProcessingStatus()) && txnDtls[6] != null) {
                try{
                    JSONObject errorJson = new JSONObject(txnDtls[6].toString());
                    if(errorJson.has("error-display-message")){
                        responseBean.setErrorMessage(errorJson.get("error-display-message").toString());
                    }else {
                        responseBean.setErrorMessage(ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                    }
                }catch (JSONException e){
                    responseBean.setErrorMessage(ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                }
            }

            if (txnDtls.length > 7 && txnDtls[7] != null){
                responseBean.setLastUpdatedBy(txnDtls[7].toString());
            }

            setFileCountByStatus(responseBean);
        }
        LOG.info("END >> PdfReaderHandler >> prepareFileParsingDetails >> fileType >> " + fileType);
        return responseBean;
    }

    private void setFileCountByStatus(PdfFileTxnDetailsDto txnDetailsDto) {
        List<Object[]> fileCountList = service.getFileUploadDetailsRepo().countByTxnId(txnDetailsDto.getTxnId());
        int totalFilesUploaded = 0;
        if (null != fileCountList && !fileCountList.isEmpty()) {
            Map<String, Integer> fileProcessingDetailsMap = new HashMap<>();
            for (Object[] fileCountDtls : fileCountList) {
                int fileCount = Integer.parseInt(fileCountDtls[0].toString());
                String key = TransactionStatus.valueOf(fileCountDtls[1].toString()).getDisplayKey();

                if(!Objects.isNull(fileCountDtls[2]) && Boolean.parseBoolean(fileCountDtls[2].toString())){
                    key = TransactionStatus.DELETED.getDisplayKey();
                }

                totalFilesUploaded += fileCount;
                if (fileProcessingDetailsMap.containsKey(key)) {
                    fileProcessingDetailsMap.put(key, fileProcessingDetailsMap.get(key) + fileCount);
                } else {
                    fileProcessingDetailsMap.put(key, fileCount);
                }
            }

            txnDetailsDto.setFileProcessingDtls(fileProcessingDetailsMap);
            txnDetailsDto.setTotalFileCount(totalFilesUploaded);

            if(!fileProcessingDetailsMap.containsKey(TransactionStatus.PDF_PARSING_FAILED.getDisplayKey())){
                txnDetailsDto.setErrorMessage(null);
            }
        }
    }

    public ResponseEntity<?> convertPdfToHtml(MultipartFile multipartFile) throws PdfReaderException, IOException {

        String fileName = "pdfHTMLData_" + multipartFile.getName() + "_" + new Date().getTime() + ".html";
        PDDocument pdfDocument = Loader.loadPDF(multipartFile.getInputStream());

        String htmlPath = PdfUtil.saveHtmlFileToStorage(pdfDocument, fileName, "HTML_Conversion");
        File htmlFile = new File(htmlPath);
        InputStreamResource resource = new InputStreamResource(StreamHelper.prepareExcelFileStream(htmlFile));
        String headerValue = "attachment; filename=\"" + htmlFile.getName() + "\"";

        return ResponseEntity
                .ok()
                .contentLength(htmlFile.length())
                .header(HttpHeaders.CONTENT_DISPOSITION, headerValue)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @Override
    public Map<String, Object> getFileUploadHistory(String pan, String fileType, Integer pageNo, Integer limit,
                                                    String searchKey, String searchValue, String sortBy,
                                                    short sortingOrder) throws PdfReaderException {
        LOG.info("START >> PdfParsingHandler >> getFileUploadHistory >> fileType >> " + fileType);
        int skips = limit * pageNo;
        List<Object[]> fileUploadDetailsList = service.getFileUploadDeatails(pan, fileType, skips, limit, searchKey, searchValue,
                sortBy, sortingOrder);
        boolean getDeletedRecords = StringUtils.isNotBlank(searchValue) && TransactionStatus.isDeletedStatus(searchValue);
        Map<String, Object> fileUploadHistory= new HashMap<>();

        if (fileUploadDetailsList != null && !fileUploadDetailsList.isEmpty()) {
            List<PdfFileTxnDetailsDto> responseBeanList = getAndPrepareFileUpldHistoryDetails(fileUploadDetailsList,
                    fileType, pan, getDeletedRecords);

            fileUploadHistory.put("file-upload-history", responseBeanList);
        }
        fileUploadHistory.put("total-records", service.getFileUploadDeatailsCount(pan, fileType, searchKey, searchValue));

        LOG.info("END >> PdfParsingHandler >> getFileUploadHistory >> fileType >> " + fileType);
        return ResponseHelper.success(fileUploadHistory);
    }

    /**
     * @param pan               PAN
     * @param txnId             Transaction Id for file upload transaction.
     * @param returnFailedFiles Flag to get error files or success files.
     * @param pageNo            Page number.
     * @param limit             Number of records to send.
     * @return Returns the transaction details with the file upload details
     * @throws PdfReaderException Throws PDFReaderException.
     *
     * <AUTHOR> Nagare
     * @since 02/08/2023
     */
    @Override
    public Map<String, Object> getFileUploadDetails(String pan, String txnId, boolean returnFailedFiles, Integer pageNo,
                                                    Integer limit, String searchKey, String searchValue,String sortBy,
                                                    short sortingOrder) throws PdfReaderException {
        LOG.info("START >> PdfParsingHandler >> getFileUploadDetails >> PAN >> " + pan + " >> TXN_ID >> " + txnId);

        PdfFileTxnDetailsDto fileTxnDtlsDto;
        List<FileUploadDetailsVO> fileUploadDtlsList;

        //Get transaction details
        TransactionManagerVO txnVo = service.getTransactionManagerRepo().findFirstByTxnIdAndPan(txnId, pan);

        if (null == txnVo) {
            LOG.error("ERROR >> PdfParsingHandler >> getFileUploadDetails >> PAN >> " + pan + " >> TXN_ID >> " + txnId +
                    " >> Transaction details not found.");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.TXN_NOT_FOUND);
        }

        if (!TransactionType.PDF_FILE_PARSING.name().equals(txnVo.getType())) {
            LOG.error("ERROR >> PdfParsingHandler >> getFileUploadDetails >> PAN >> " + pan + " >> TXN_ID >> " + txnId +
                    " >> Invalid Transaction found.");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_TXN_TYPE);
        }

        List<String> statusList = Collections.singletonList(returnFailedFiles ?
                TransactionStatus.PDF_PARSING_FAILED.name() : TransactionStatus.PDF_PARSING_COMPLETE.name());

        //Get File upload details from the file upload details table.
        fileUploadDtlsList = service.getFileUploadDeatails(pan, txnId, statusList, pageNo, limit, searchKey,
                searchValue, sortBy, sortingOrder);

        fileTxnDtlsDto = setFileUploadDetails(txnVo, fileUploadDtlsList);
        setFileCountByStatus(fileTxnDtlsDto);

        LOG.info("END >> PdfParsingHandler >> getFileUploadDetails >> PAN >> " + pan + " >> TXN_ID >> " + txnId);
        return ResponseHelper.success(fileTxnDtlsDto);
    }

    /**
     * @param pan PAN
     * @param txnId File upload transaction Id
     * @param fileTxnId File Transaction Id.
     * @param fileType File type.
     * @param shouldReturnFile Flag to specify if we have to return the file stream or base 64 string.
     * @return Returns the base 64 string or file stream based on the return file flag.
     * @throws PdfReaderException Throws PdfReaderException
     *
     * <AUTHOR> Nagare
     * @since 02/08/2023
     * @description </p><strong>This method returns either file stream or base 64 file based on the shouldReturnFile flag value.</strong><br>
     *              Conditions - <br>
     *              If transaction id is passed then it returns zip of all the files uploaded under that transaction.<br>
     *              If file transaction Id is passed then it returns that particular file.<br>
     *              If both transaction Id and file transaction Id is passed then transaction Id takes the preference.
     */
    @Override
    public ResponseEntity<?> getFiles(String pan, String txnId, String fileTxnId, String fileType, boolean shouldReturnFile)
            throws PdfReaderException {
        LOG.info("START >> PdfParsingHandler >> getFiles >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+" >> TXN_ID >> "
            +txnId+" >> FILE_TYPE "+fileType);

        File fileToReturn;

        if(StringUtils.isNotBlank(fileTxnId)){
            fileToReturn = getFileUsingFileTxnId(pan, fileTxnId, fileType);
        }else {
            fileToReturn = getAllFilesUsingForTxnId(pan, txnId, fileType);
        }

        LOG.info("END >> PdfParsingHandler >> getFiles >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+" >> TXN_ID >> "
            +txnId+" >> FILE_TYPE "+fileType);
        return ResponseHelper.handleFileResponse(fileToReturn, shouldReturnFile);
    }

    /**
     * @param pan PAN.
     * @param fileTxnId File transaction Id.
     * @param fileType File type.
     * @return Returns the requested file.
     * @throws PdfReaderException Throws PdfReaderException
     *
     * <AUTHOR> Nagare
     * @since 02/08/2023
     * @description </p>This method gets the file upload details using the file transaction Id and returns the file.
     */
    private File getFileUsingFileTxnId(String pan, String fileTxnId, String fileType) throws PdfReaderException{
        LOG.info("START >> PdfParsingHandler >> getFileUsingFileTxnId >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+
            " >> FILE_TYPE "+fileType);

        File fileToReturn;

        FileUploadDetailsVO fileUpldDtlsVo = service.getFileUploadDetailsRepo().findFirstByPanAndSubTxnId(pan, fileTxnId);

        if(null == fileUpldDtlsVo){
            LOG.info("ERROR >> PdfParsingHandler >> getFileUsingFileTxnId >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+
                    " >> FILE_TYPE "+fileType+" >> File Upload Details not found");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_UPLD_DTLS_NOT_FOUND);
        }

        if(!fileUpldDtlsVo.getFileType().equals(fileType)){
            LOG.info("ERROR >> PdfParsingHandler >> getFileUsingFileTxnId >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+
                    " >> FILE_TYPE "+fileType+" >> Invalid File Upload Details Found");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_FILE);
        }

        fileToReturn = StreamHelper.getFileFromLocation(fileUpldDtlsVo.getPan(), fileUpldDtlsVo.getFileType(),
                fileUpldDtlsVo.getFileName(), fileUpldDtlsVo.getFileLoc(), false);

        LOG.info("END >> PdfParsingHandler >> getFileUsingFileTxnId >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+
            " >> FILE_TYPE "+fileType);

        return fileToReturn;
    }

    /**
     * @param pan PAN.
     * @param txnId File upload transaction Id.
     * @param fileType File Type.
     * @return Returns the Zip file for all the files uploaded under the provided transaction.
     * @throws PdfReaderException Throws PdfReaderException.
     *
     * <AUTHOR> Nagare
     * @since 02/08/2023
     * @description </p>This method gets all the files uploaded under the provided transaction,
     *                  <strong>creates the zip file of them and returns the zip file.</strong>
     */
    private File getAllFilesUsingForTxnId(String pan, String txnId, String fileType) throws PdfReaderException{
        LOG.info("START >> PdfParsingHandler >> getAllFilesUsingForTxnId >> PAN >> "+pan+" >> TXN_ID >> "+txnId+
            " >> FILE_TYPE "+fileType);

        File fileToReturn;
        List<String> fileList;
        List<FileUploadDetailsVO> fileUpldDtlsList = service.getFileUploadDetailsRepo()
                .findAllByPanAndTxnIdAndIsDeleted(pan, txnId, false);

        if(null == fileUpldDtlsList || fileUpldDtlsList.isEmpty()){
            LOG.info("ERROR >> PdfParsingHandler >> getAllFilesUsingForTxnId >> PAN >> "+pan+" >> TXN_ID >> "+txnId+
                    " >> FILE_TYPE "+fileType+" >> File Upload Details not found");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_UPLD_DTLS_NOT_FOUND);
        }

        if(fileUpldDtlsList.stream().anyMatch(fileUpldDtls -> !fileUpldDtls.getFileType().equals(fileType))){
            LOG.info("ERROR >> PdfParsingHandler >> getAllFilesUsingForTxnId >> PAN >> "+pan+" >> TXN_ID >> "+txnId+
                    " >> FILE_TYPE "+fileType+" >> Invalid File Upload Details Found");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_FILE);
        }

        fileList = new ArrayList<>();

        for (FileUploadDetailsVO fileUpldDtls : fileUpldDtlsList) {
            fileToReturn = StreamHelper.getFileFromLocation(fileUpldDtls, false);
            if(null != fileToReturn) {
                fileList.add(fileToReturn.getPath());
            }
        }

        try {
            if (!fileList.isEmpty()) {
                String fileTypeNmWoExt = FilenameUtils.removeExtension(PdfFileType.valueOf(fileType).getPdfFileName());

                String zipFileNameToExport = FileHelper.getFileNameWithExtension("zip", pan, fileTypeNmWoExt);
                String destFolderPath = FileHelper.getLocalFolderPath(pan, SftpOperations.DOWNLOAD_FILE.getValue(),
                        fileType, false);
                String filePathWithName = destFolderPath + zipFileNameToExport;

                FileHelper.checkAndCreateDirectory(destFolderPath);

                FileHelper.createZipFile(filePathWithName, fileList);

                fileToReturn = new File(filePathWithName);
            } else {
                LOG.info("ERROR >> PdfParsingHandler >> getAllFilesUsingForTxnId >> PAN >> " + pan + " >> TXN_ID >> " + txnId +
                        " >> FILE_TYPE " + fileType + " >> Invalid File Upload Details Found");
                throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_NOT_FOUND);
            }
        }catch (IOException e){
            LOG.info("ERROR >> PdfParsingHandler >> getAllFilesUsingForTxnId >> PAN >> " + pan + " >> TXN_ID >> " + txnId +
                    " >> FILE_TYPE " + fileType + " >> Error while creating zip file >> ", e);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.ERROR_WHILE_CREATING_ZIP_FILE);
        }

        LOG.info("END >> PdfParsingHandler >> getAllFilesUsingForTxnId >> PAN >> "+pan+" >> TXN_ID >> "+txnId+
            " >> FILE_TYPE "+fileType);

        return fileToReturn;
    }

    /**
     * @param pan PAN.
     * @param fileTxnId File transaction Id.
     * @param fileType File Type.
     * @return Returns success or failure message.
     * @throws PdfReaderException Throws PdfReaderException.
     *
     * <AUTHOR> Nagare
     * @since 02/08/2023
     * @description </p>This method gets the file upload details for the provided file transaction Id and marks it as deleted.<br>
     *              <strong>STEPS -</strong><br>
     *              1. Get the file upload details.<br>
     *              2. Delete the parsed data form the mongo db for the file type from the respective collection.<br>
     *              3. Delete the file form the Storage.<br>
     *              4. Mark the file upload details as deleted.<br>
     */
    @Override
    public Map<String, Object> deleteFile(String pan, String fileTxnId, String txnId, String fileType) throws PdfReaderException{
        LOG.info("START >> PdfParsingHandler >> deleteFile >> PAN >> "+pan+" >> TXN_ID >> "+txnId+" >> FILE_ID >> "+
                fileTxnId+" >> FILE_TYPE "+fileType);
        long deleteCount = 0;
        String source = null;
        if(StringUtils.isNotBlank(fileTxnId)) {
            FileUploadDetailsVO fileUploadDetailsVO = service.getFileUploadDetailsRepo().findFirstByPanAndSubTxnId(pan,
                    fileTxnId);
            deletePdfFile(fileUploadDetailsVO, pan, fileType);
            deleteCount ++;
            source = fileUploadDetailsVO.getSource();
            txnId = fileUploadDetailsVO.getTxnId();
        }else if (StringUtils.isNotBlank(txnId)){
            List<FileUploadDetailsVO> fileUpldDtlsList = service.getFileUploadDetailsRepo()
                    .findAllByPanAndTxnIdAndFileType(pan, txnId, fileType);

            if(null == fileUpldDtlsList || fileUpldDtlsList.isEmpty()){
                LOG.info("ERROR >> PdfParsingHandler >> deleteFile >> PAN >> " + pan + " >> TXN_ID >> " +
                        txnId + " >> FILE_TYPE " + fileType + " >> File Upload details not found.");
                throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_UPLD_DTLS_NOT_FOUND);
            }

            if(fileUpldDtlsList.stream().anyMatch(fileUpldDtls -> !fileType.equals(fileUpldDtls.getFileType()))){
                LOG.info("ERROR >> PdfParsingHandler >> deleteFile >> PAN >> " + pan + " >> TXN_ID >> " +
                        txnId + " >> FILE_TYPE " + fileType +" >> Invalid Transaction details.");
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_TXN_TYPE);
            }

            for (FileUploadDetailsVO fileUploadDetailsVO : fileUpldDtlsList) {
                deletePdfFile(fileUploadDetailsVO, pan, fileType);
                deleteCount ++;
            }

            source = fileUpldDtlsList.get(0).getSource();
        }

        if(deleteCount > 0) {

            checkAndUpdateTxnAfterDelete(pan, txnId, fileType);

            try {
                String event = FileSources.SFTP.name().equals(source) ?
                            PdfFileType.SHIPPING_BILL.name().equals(fileType) ?
                                MeteringEvents.SB_SFTP_FILE_DLT.name() : MeteringEvents.BOE_SFTP_FILE_DLT.name()
                            :
                            PdfFileType.SHIPPING_BILL.name().equals(fileType) ?
                                MeteringEvents.SB_FILE_DLT.name() : MeteringEvents.BOE_FILE_DLT.name();

                meteringHandler.addOrUpdateMeteringDetails(pan, event,MeteringEntities.FILE_COUNT.getMeteringEntityKey(),
                        deleteCount, 0, 0, null);
            } catch (SubscriptionException e) {
                LOG.error("ERROR >> PdfParsingHandler >> deleteFile >> PAN >> " + pan + " >> TXN_ID >> " + txnId + " >> FILE_ID >> " +
                        fileTxnId + " >> FILE_TYPE " + fileType + " >> DELETE_RECORD_COUNT >> " + deleteCount + e.getErrorMessge());
                throw new PdfReaderException(e.getErrorCode(), e.getErrorMessge());
            }
        }

        LOG.info("END >> PdfParsingHandler >> deleteFile >> PAN >> "+pan+" >> TXN_ID >> "+txnId+" >> FILE_ID >> "+
                fileTxnId+" >> FILE_TYPE "+fileType+" >> DELETE_RECORD_COUNT >> "+deleteCount);
        return ResponseHelper.success(ResponseMessage.FILE_DELETE_SUCCESS);
    }

    /**
     * <AUTHOR> Nagare
     * @since 04-22-2024
     * @Description This method will check if all the files from the transaction are completed and will update the
     * status to Completed and remove the other details field and if all files are deleted this will update the
     * status to Deleted and remove the other details from the database entry
     * @param pan PAN
     * @param txnId Transaction Id
     * @param fileType File Type
     */
    private void checkAndUpdateTxnAfterDelete(String pan, String txnId, String fileType){
        LOG.info("START >> PdfParsingHandler >> checKAndUpdateTxnAfterDelete >> PAN >> "+pan+" >> TXN_ID >> "+txnId
                +" >> FILE_TYPE "+fileType);

        if(StringUtils.isNotBlank(txnId)){
            //Check if all the files are completed for this transaction.
            List<KeyValue> conditions = new ArrayList<>();
            conditions.add(new KeyValue(MysqlDbQueryFields.PAN, pan));
            conditions.add(new KeyValue(MysqlDbQueryFields.TXN_ID, txnId));
            conditions.add(new KeyValue(MysqlDbQueryFields.FILE_TYPE, fileType));

            List<Object[]> objArrList = service.executeSql(DbQueries.GET_FILE_PARSING_DELETED_DIFF_FROM_STATUS_SQL,
                    conditions, 0, 0);

            if(null != objArrList && !objArrList.isEmpty() && null != objArrList.get(0)
                    && Long.parseLong(objArrList.get(0)[0].toString()) == 0){

                LOG.info("INTERMEDIATE >> PdfParsingHandler >> checKAndUpdateTxnAfterDelete >> PAN >> "+pan+" >> TXN_ID >> "+txnId
                        +" >> FILE_TYPE "+fileType+" >> Setting the status as Deleted as all files are deleted");

                //Set Status as Deleted and remove the other details.
                service.getTransactionManagerRepo().updateByTxnId(TransactionStatus.DELETED.name(), null, txnId);
            }else {
                conditions.add(new KeyValue(MysqlDbQueryFields.IS_DELETED, false));

                objArrList = service.executeSql(DbQueries.GET_FILE_PARSING_STATUS_DIFF_FROM_STATUS_SQL,
                        conditions, 0, 0);

                if(null != objArrList && !objArrList.isEmpty() && null != objArrList.get(0)
                        && Long.parseLong(objArrList.get(0)[0].toString()) == 0){

                    LOG.info("INTERMEDIATE >> PdfParsingHandler >> checKAndUpdateTxnAfterDelete >> PAN >> "+pan+" >> TXN_ID >> "+txnId
                            +" >> FILE_TYPE "+fileType+" >> Setting the status as Completed as all files are completed");

                    //Set Status as Completed and remove the other details.
                    service.getTransactionManagerRepo().updateByTxnId(TransactionStatus.PDF_PARSING_COMPLETE.name(), null, txnId);
                }
            }
        }

        LOG.info("END >> PdfParsingHandler >> checKAndUpdateTxnAfterDelete >> PAN >> "+pan+" >> TXN_ID >> "+txnId
                +" >> FILE_TYPE "+fileType);
    }

    private long deletePdfFile(FileUploadDetailsVO fileUploadDetailsVO, String pan, String fileType)
            throws PdfReaderException{
        if(null == fileUploadDetailsVO){
            LOG.info("ERROR >> PdfParsingHandler >> deleteFile >> PAN >> " + pan + " >> FILE_TYPE " + fileType +
                    " >> File Upload details not found.");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_UPLD_DTLS_NOT_FOUND);
        }

        if(!fileUploadDetailsVO.getFileType().equals(fileType)){
            LOG.info("ERROR >> PdfParsingHandler >> deleteFile >> PAN >> " + pan + " >> TXN_ID >> " +
                    fileUploadDetailsVO.getTxnId() + " >> FILE_ID >> "+fileUploadDetailsVO.getSubTxnId()+
                    " >> FILE_TYPE " + fileType + " >> Invalid File type found.");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_FILE);
        }

        //Delete the file form storage
        StreamHelper.deleteFileFromStorage(fileUploadDetailsVO, false);

        //Check and delete the parsed data from mongo db ase well.
        long deleteCount = service.deleteMongoRecordsBasedOnSubTxnId(PdfFileType.valueOf(fileType).getMongoCollection(),
                fileUploadDetailsVO.getSubTxnId(), pan);

        //Set the file upload details status as deleted.
        fileUploadDetailsVO.setDeleted(true);
        fileUploadDetailsVO.setUpdatedAt(new Date());
        service.getFileUploadDetailsRepo().save(fileUploadDetailsVO);

        LOG.info("END >> PdfParsingHandler >> deleteFile >> PAN >> "+pan+ " >> TXN_ID >> " +
                fileUploadDetailsVO.getTxnId() +" >> FILE_ID >> "+fileUploadDetailsVO.getSubTxnId()+
                " >> FILE_TYPE "+fileType+" >> DELETE_RECORD_COUNT >> "+deleteCount);
        return deleteCount;
    }

    /**
     *
     * @param txnVo TransactionManagerVO
     * @param fileUploadDtlsList List of FileUploadDetailsVO
     *
     * <AUTHOR> Nagare
     * @since 02/08/2023
     * @description </p>This method creates the PdfFileTxnDetailsDto from the TransactionManagerVO and List of
     * FileUploadDetailsVO.
     */
    private PdfFileTxnDetailsDto setFileUploadDetails(TransactionManagerVO txnVo,
                                                      List<FileUploadDetailsVO> fileUploadDtlsList) {
        PdfFileTxnDetailsDto fileTxnDtlsDto = new PdfFileTxnDetailsDto();
        fileTxnDtlsDto.setProcessingStatus(TransactionStatus.valueOf(txnVo.getStatus()).getDisplayName());
        fileTxnDtlsDto.setLastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat(null != txnVo.getUpdatedAt() ?
                txnVo.getUpdatedAt() : txnVo.getCreatedAt(), DateFormatUtil.ddMMYYYY_Hyphen_with_time));
        fileTxnDtlsDto.setLastUpdatedBy(txnVo.getUserName());

        fileTxnDtlsDto.setTxnId(txnVo.getTxnId());
        fileTxnDtlsDto.setTxnType(TransactionType.valueOf(txnVo.getType()).getDisplayName());

        List<FileDetailsDto> fileDtlsDtoList = new ArrayList<>();

        if(null != fileUploadDtlsList && !fileUploadDtlsList.isEmpty()) {
            fileUploadDtlsList.stream().map(fileUploadDtls ->
                            FileDetailsDto.builder()
                                    .fileId(fileUploadDtls.getSubTxnId())
                                    .fileType(PdfFileType.valueOf(fileUploadDtls.getFileType()).getDisplayName())
                                    .fileName(fileUploadDtls.getFileDisplayName())
                                    .processingStatus(TransactionStatus.valueOf(fileUploadDtls.getStatus()).getDisplayName())
                                    .lastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat(
                                            fileUploadDtls.getUpdatedAt() != null ? fileUploadDtls.getUpdatedAt() :
                                                    fileUploadDtls.getCreatedAt(), DateFormatUtil.ddMMYYYY_Hyphen_with_time))
                                    .lastUpdatedBy(fileUploadDtls.getUserName()).build()
            ).forEach(fileDtlsDtoList::add);
        }

        fileTxnDtlsDto.setFileDetailsDtoList(fileDtlsDtoList);
        return fileTxnDtlsDto;
    }

    /**
     * @param pan PAN
     * @param fileType File type
     * @param startPeriod Start period of the file type.
     * @param endPeriod End Period of the file type.
     * @param txnId File Upload Transaction Id.
     * @return Returns the File details for the processed files.
     * @throws PdfReaderException Throws PdfReaderException
     *
     * <AUTHOR> Nagare
     * @since 11/08/2023
     * @description </p>This method gets the processed files details based on the transaction Id or start and end period.<br>
     * <strong>If Txn Id is passed -</strong><br>
     * 1. Get the file transaction Ids for all the processed files.<br>
     * 2. Get all unique shipping bill or bill of entry numbers from the mongo db for the file transaction Ids.<br>
     * 3. Create the Processed file details DTO.<br>
     * 4. Get the in progress file count.<br>
     * 5. Return response.<br>
     * <strong>Else -</strong><br>
     * 1. Get the file transaction Ids for all the BOE's or SB's based on the start and end period along with the other details.<br>
     * 2. Get File Processing details from the file transaction Ids.<br>
     * 3. Create the Processed file details DTO.<br>
     * 4. Get the in progress file count.<br>
     * 5. Return response.<br>
     */
    @Override
    public Map<String, Object> getProcessedFilesDetails(String pan, String fileType, String startPeriod, String endPeriod,
                                                        String txnId, int pageNo, int limit,
                                                        String searchKey, String searchValue,String sortBy,
                                                        short sortingOrder) throws PdfReaderException {
        LOG.info("START >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> TXN_ID >> "+txnId);

        FileProcessingDetailsDto fileProcessingDetailsDto;

        if(StringUtils.isNotBlank(txnId)){
            fileProcessingDetailsDto = getProcessedFileDetailsFromTxnId(pan, fileType, txnId, pageNo, limit, searchKey,
                    searchValue, sortBy, sortingOrder);
        }else {
            fileProcessingDetailsDto = getProcessedFileDetailsFromPeriod(pan, fileType, startPeriod, endPeriod, pageNo,
                    limit, searchKey, searchValue, sortBy, sortingOrder);
        }

        if(null == fileProcessingDetailsDto){
            fileProcessingDetailsDto = new FileProcessingDetailsDto();
        }

        //Get the in-progress file count for the pan and file type.
        fileProcessingDetailsDto.setInProgressFilesCount(service.getFileUploadDetailsRepo()
                .countByPanAndFileTypeAndStatusIn(pan, fileType, Arrays.asList(TransactionStatus.UPLOADED.name(),
                        TransactionStatus.PDF_PARSING_IN_PROGRESS.name())));

        LOG.info("END >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> TXN_ID >> "+txnId);
        return ResponseHelper.success(fileProcessingDetailsDto);
    }


    /**
     * @param pan PAN
     * @param fileType File Type
     * @param txnId File upload Transaction Id
     * @return Returns File Processing DTO
     * @throws PdfReaderException Throws PdfReaderException
     *
     * <AUTHOR> Nagare
     * @since 11/08/2023
     * @description This method returns the file processed files details based on the transaction Id.
     */
    private FileProcessingDetailsDto getProcessedFileDetailsFromTxnId(String pan, String fileType, String txnId,
                                                                      int pageNo, int limit, String searchKey,
                                                                      String searchValue,String sortBy,
                                                                      short sortingOrder) throws PdfReaderException{
        LOG.info("START >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> TXN_ID >> "+txnId);

        FileProcessingDetailsDto fileProcessingDetailsDto = null;
        List<FileUploadDetailsVO> fileUploadDetailsVOList;

        List<Document> documentList;

        //Get SB date and SB No or BOE date and BOE No from the file transaction Id.
        if(PdfFileType.BILL_OF_ENTRY.name().equals(fileType)){
            documentList = service.getBoeDateAndNoFromSubTxnId(pan, txnId, pageNo, limit, searchKey,
                    searchValue, sortBy, sortingOrder);
        }else if(PdfFileType.SHIPPING_BILL.name().equals(fileType)){
            documentList = service.getSbDateAndNoFromSubTxnId(pan, txnId, pageNo, limit, searchKey,
                    searchValue, sortBy, sortingOrder);
        }else {
            LOG.error("ERROR >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                    " >> TXN_ID >> "+txnId+" >> Invalid File Type.");
            throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_FILE_TYPE);
        }

        if(null != documentList && !documentList.isEmpty()){
            fileProcessingDetailsDto = new FileProcessingDetailsDto();

            //Get all the subTransaction Ids from the mongo document and get the file upload details.
            List<String> subTxnIdList = documentList.stream().map(document ->
                    document.getString(BoeDetailsFields.SUB_TXN_ID.getValue())).collect(Collectors.toList());

            fileUploadDetailsVOList = service.getFileUploadDetailsRepo().findAllByPanAndSubTxnIdInOrderByIdDesc(pan, subTxnIdList);

            if(!fileUploadDetailsVOList.isEmpty()){
                fileProcessingDetailsDto.setFileDetailsDtoList(prepareProcessedFileDetails(fileUploadDetailsVOList,
                        documentList, pan, fileType, null));

                if(PdfFileType.BILL_OF_ENTRY.name().equals(fileType)){
                    fileProcessingDetailsDto.setTotalRecords(service.getBoeCountFromTxnId(pan, txnId, searchKey, searchValue));
                }else {
                    fileProcessingDetailsDto.setTotalRecords(service.getSbCountFromTxnId(pan, txnId, searchKey, searchValue));
                }

            }else {
                LOG.error("ERROR >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                        " >> TXN_ID >> "+txnId+" >> File upload details not found.");
            }
        }else {
            LOG.error("ERROR >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                    " >> TXN_ID >> "+txnId+" >> No data found in mongoDb for the file type.");
        }

        LOG.info("END >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> TXN_ID >> "+txnId);

        return fileProcessingDetailsDto;
    }

    /**
     * @param pan PAN
     * @param fileType File Type
     * @param startPeriod Start Period
     * @param endPeriod End Period
     * @return Returns File Processing DTO
     * @throws PdfReaderException Throws PdfReaderException
     *
     * <AUTHOR> Nagare
     * @since 11/08/2023
     */
    private FileProcessingDetailsDto getProcessedFileDetailsFromPeriod(String pan, String fileType, String startPeriod,
                                                                       String endPeriod, int pageNo, int limit,
                                                                       String searchKey, String searchValue,String sortBy,
                                                                       short sortingOrder)
            throws PdfReaderException{
        LOG.info("START >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod);

        FileProcessingDetailsDto fileProcessingDetailsDto = null;
        List<FileUploadDetailsVO> fileUploadDetailsVOList;
        List<Document> documentList;
        Date startPeriodDate = DateFormatUtil.convertMMYYYYToDate(startPeriod);
        Date endPeriodDate = DateFormatUtil.getLastDaydate(DateFormatUtil.convertMMYYYYToDate(endPeriod));

        //Get SB date and SB No or BOE date and BOE No from the file transaction Id.
        if(PdfFileType.BILL_OF_ENTRY.name().equals(fileType)){
            documentList = service.getBoeDateAndNoFromPeriod(pan, startPeriodDate, endPeriodDate, pageNo, limit,
                    searchKey, searchValue, sortBy, sortingOrder);
        }else if(PdfFileType.SHIPPING_BILL.name().equals(fileType)){
            documentList = service.getSbDateAndNoFromPeriod(pan, startPeriodDate, endPeriodDate, pageNo, limit,
                    searchKey, searchValue, sortBy, sortingOrder);
        }else {
            LOG.error("ERROR >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                    " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> Invalid File Type.");
            throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_FILE_TYPE);
        }

        //Prepare the File Processing details DTO for each file upload details.
        if(null != documentList && !documentList.isEmpty()){
            fileProcessingDetailsDto = new FileProcessingDetailsDto();

            //Get all the subTransaction Ids from the mongo document and get the file upload details.
            List<String> subTxnIdList = documentList.stream().map(document ->
                    document.getString(BoeDetailsFields.SUB_TXN_ID.getValue())).collect(Collectors.toList());

            fileUploadDetailsVOList = service.getFileUploadDetailsRepo().findAllByPanAndSubTxnIdInOrderByIdDesc(pan, subTxnIdList);

            if(!fileUploadDetailsVOList.isEmpty()){
                fileProcessingDetailsDto.setFileDetailsDtoList(prepareProcessedFileDetails(fileUploadDetailsVOList,
                        documentList, pan, fileType, null));

                if(PdfFileType.BILL_OF_ENTRY.name().equals(fileType)){
                    fileProcessingDetailsDto.setTotalRecords(service.getBoeCountFromPeriod(pan, startPeriodDate, endPeriodDate,
                    searchKey, searchValue));
                }else {
                    fileProcessingDetailsDto.setTotalRecords(service.getSbCountFromPeriod(pan, startPeriodDate, endPeriodDate,
                    searchKey, searchValue));
                }

            }else {
                LOG.error("ERROR >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                        " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> File upload details not found.");
            }
        }else {
            LOG.error("ERROR >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                    " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> No data found in mongoDb for the file type.");
        }

        LOG.info("END >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod);

        return fileProcessingDetailsDto;
    }

    private List<FileDetailsDto> prepareProcessedFileDetails(List<FileUploadDetailsVO> fileUploadDetailsList,
                                                             List<Document> mongoDocumentList, String pan, String fileType,
                                                             String txnId){
        List<FileDetailsDto> fileDetailsDtoList = new ArrayList<>();
        LOG.info("START >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> TXN_ID >> "+txnId);

        for (Document document : mongoDocumentList) {
            FileDetailsDto fileDetailsDto = new FileDetailsDto();

            if(PdfFileType.BILL_OF_ENTRY.name().equals(fileType)){
                //Get the BOE date and BOE no from the document list.
                fileUploadDetailsList.stream().filter(fileUploadDtls -> ((Document) document.get("_id"))
                    .getString(SBInvoiceDetailsFields.SUB_TXN_ID.getValue())
                    .equals(fileUploadDtls.getSubTxnId()))
                    .findFirst().ifPresent(fileUploadDtls -> {

                        fileDetailsDto.setFileId(fileUploadDtls.getSubTxnId());
                        fileDetailsDto.setFileName(fileUploadDtls.getFileDisplayName());
                        fileDetailsDto.setLastUpdatedBy(fileUploadDtls.getUserName());
                        fileDetailsDto.setUploadDate(DateFormatUtil.convertDateToStringAndFormat(fileUploadDtls.getCreatedAt(),
                                DateFormatUtil.ddMMYYYY_Hyphen_with_time));

                        if(null != fileUploadDtls.getUpdatedAt()) {
                            fileDetailsDto.setLastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat(fileUploadDtls.getUpdatedAt(),
                                    DateFormatUtil.ddMMYYYY_Hyphen_with_time));
                        }else {
                            fileDetailsDto.setLastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat(fileUploadDtls.getCreatedAt(),
                                    DateFormatUtil.ddMMYYYY_Hyphen_with_time));
                        }

                        if(document.containsKey(BoeDetailsFields.BOE_NO.getValue())) {
                            fileDetailsDto.setBoeNo(document.getString(BoeDetailsFields.BOE_NO.getValue()));
                        }

                        if(document.containsKey(BoeDetailsFields.BE_TYPE.getValue())) {
                            fileDetailsDto.setBeType(BoeType.valueOf(document.getString(BoeDetailsFields.BE_TYPE.getValue())).getDescription());
                        }

                        if(document.containsKey(BoeDetailsFields.BOE_DATE.getValue())) {
                            fileDetailsDto.setBoeDate(DateFormatUtil.convertDateToStringAndFormat(
                                    document.getDate(BoeDetailsFields.BOE_DATE.getValue()),
                                    DateFormatUtil.ddMMYYYY_Hyphen_with_time));
                        }
                    }
                );
            }else {
                //Get the SB date and SB no from the document list.
                fileUploadDetailsList.stream().filter(fileUploadDtls -> ((Document) document.get("_id"))
                    .getString(SBInvoiceDetailsFields.SUB_TXN_ID.getValue())
                    .equals(fileUploadDtls.getSubTxnId()))
                    .findFirst().ifPresent(fileUploadDtls -> {

                        fileDetailsDto.setFileId(fileUploadDtls.getSubTxnId());
                        fileDetailsDto.setFileName(fileUploadDtls.getFileDisplayName());
                        fileDetailsDto.setLastUpdatedBy(fileUploadDtls.getUserName());

                        fileDetailsDto.setUploadDate(DateFormatUtil.convertDateToStringAndFormat(fileUploadDtls.getCreatedAt(),
                                DateFormatUtil.ddMMYYYY_Hyphen_with_time));

                        if(null != fileUploadDtls.getUpdatedAt()) {
                            fileDetailsDto.setLastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat(fileUploadDtls.getUpdatedAt(),
                                    DateFormatUtil.ddMMYYYY_Hyphen_with_time));
                        }else {
                            fileDetailsDto.setLastUpdatedAt(DateFormatUtil.convertDateToStringAndFormat(fileUploadDtls.getCreatedAt(),
                                    DateFormatUtil.ddMMYYYY_Hyphen_with_time));
                        }

                        if(document.containsKey(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue())) {
                            fileDetailsDto.setSbNo(document.getString(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue()));
                        }

                        if(document.containsKey(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue())) {
                            fileDetailsDto.setSbDate(DateFormatUtil.convertDateToStringAndFormat(
                                    document.getDate(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue()),
                                    DateFormatUtil.ddMMYYYY_Hyphen_with_time));
                        }
                    }
                );
            }

            fileDetailsDtoList.add(fileDetailsDto);
        }

        LOG.info("END >> PdfParsingHandler >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> TXN_ID >> "+txnId);
        return fileDetailsDtoList;
    }

    /**
     * @param pan PAN
     * @param iecCode IEC Code
     * @param startPeriod Start Period
     * @param endPeriod End Period
     * @param serviceName Product Details Request
     * @param pageNo Page Number
     * @param limit Limit
     * @return Returns Shipping Bill details.
     * @throws PdfReaderException Throws PdfReaderException.
     *
     * <AUTHOR> Narayane
     * @since 22-07-2025
     */
    @Override
    public Map<String, Object> getShippingBillDetails(String pan, String iecCode, String startPeriod, String endPeriod, String serviceName, int pageNo, int limit) throws PdfReaderException {

        LOG.info("START >> PdfParsingHandler >> getShippingBillDetails >> PAN >> " + pan + " >> IEC_CODE >> " + iecCode +
                " >> START_PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> PRODUCT_DTL_REQ >> " + serviceName +
                " >> PAGE_NO >> " + pageNo + " >> LIMIT >> " + limit);

        Date startDate = null;
        Date endDate = null;

        if(StringUtils.isNotBlank(startPeriod) && StringUtils.isNotBlank(endPeriod)) {
            startDate = DateFormatUtil.formatDateToDate(startPeriod, DateFormatUtil.yyyyMMdd_Hyphen);
            endDate =  DateFormatUtil.formatDateToDate(endPeriod, DateFormatUtil.yyyyMMdd_Hyphen);
        }


        int shippingBillRecordCount = mongoDbService.getShippingBillRecordCount(iecCode, startDate, endDate, serviceName);

        List<Document> shippingBillInvoicesList = mongoDbService.getShippingBillInvoicesList(pan, iecCode, startDate, endDate, serviceName ,
                pageNo, limit);

        PaginatedResponseDto<Document> paginatedResponseDto = new PaginatedResponseDto<Document>();

        paginatedResponseDto.setRecords(shippingBillInvoicesList);
        paginatedResponseDto.setTotalRecords(shippingBillRecordCount);


        LOG.info("END >> PdfParsingHandler >> getShippingBillDetails >> PAN >> " + pan + " >> IEC_CODE >> " + iecCode +
                " >> START_PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> PRODUCT_DTL_REQ >> " + serviceName +
                " >> PAGE_NO >> " + pageNo + " >> LIMIT >> " + limit);

        return ResponseHelper.success(paginatedResponseDto);
    }

}
