package com.perennialsys.pdfreader.handler;

import com.perennialsys.pdfreader.dto.PdfFileTxnDetailsDto;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IPdfParsingHandler {


    List<PdfFileTxnDetailsDto> getFileProcessingDetails(String pan, String fileType);

    Map<String, Object> uploadPdfInvoices(String pan, List<MultipartFile> multipartFileList, PdfFileType pdfFileType)
            throws PdfReaderException, IOException;

    ResponseEntity<?> convertPdfToHtml(MultipartFile multipartFile) throws IOException, PdfReaderException;

    Map<String, Object> getFileUploadHistory(String pan, String fileType, Integer pageNo, Integer limit,
                                                      String searchKey, String searchValue,String sortBy,
                                                      short sortingOrder) throws PdfReaderException;
    Map<String, Object> getFileUploadDetails(String pan, String txnId, boolean returnFailedFiles, Integer pageNo, Integer limit,
                                                      String searchKey, String searchValue,String sortBy,
                                                      short sortingOrder) throws PdfReaderException;
    ResponseEntity<?> getFiles(String pan, String txnId, String fileTxnId, String fileType, boolean shouldReturnFile) throws PdfReaderException;
    Map<String, Object> deleteFile(String pan, String fileTxnId, String txnId, String fileType) throws PdfReaderException;
    Map<String, Object> getProcessedFilesDetails(String pan, String fileType, String startPeriod, String endPeriod,
                                                 String fileTxnId, int pageNo, int limit, String searchKey,
                                                 String searchValue,String sortBy, short sortingOrder
    ) throws PdfReaderException;

    Map<String, Object> getShippingBillDetails(String pan, String iecCode, String startPeriod, String endPeriod,
            String productDtlReq, int pageNo, int limit) throws PdfReaderException;
}
