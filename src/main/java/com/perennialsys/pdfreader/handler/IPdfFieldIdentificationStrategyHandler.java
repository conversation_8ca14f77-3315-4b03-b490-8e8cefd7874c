package com.perennialsys.pdfreader.handler;

import com.perennialsys.pdfreader.dto.FieldIdentificationStrategyDto;
import com.perennialsys.pdfreader.exception.PdfReaderException;

import java.util.Map;

public interface IPdfFieldIdentificationStrategyHandler {

    Map<String, Object> setFieldIdentificationStrategy(String pan, FieldIdentificationStrategyDto fieldIdentificationStrategyDto) throws PdfReaderException;

    Map<String, Object> getFieldIdentificationStrategies(String pan, String fileType) throws PdfReaderException;
    Map<String, Object> deleteFieldIdentificationStrategies(String pan, String fileType, String targetField) throws PdfReaderException;

}
