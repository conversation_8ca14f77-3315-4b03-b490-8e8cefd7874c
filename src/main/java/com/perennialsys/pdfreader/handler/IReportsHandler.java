package com.perennialsys.pdfreader.handler;

import com.perennialsys.pdfreader.dto.ExportRequestDetailsDto;
import com.perennialsys.pdfreader.exception.PdfReaderException;

import java.io.File;
import java.util.List;
import java.util.Map;

public interface IReportsHandler {

    File exportReportForFileTxn(String pan, String txnId, String startPeriod, String endPeriod, boolean returnFileStream, List<String> fileIdList, String description, String exportFileType, String reportType) throws PdfReaderException;

    Map<String, Object> generateReportForFileTxnOrPeriod(String pan, String reportType, String startPeriod, String endPeriod,
                                                         ExportRequestDetailsDto requestDetails, String exportFileType
    ) throws PdfReaderException;

    Map<String, Object> generateReportForTxnId(String pan, String txnID, String reportType,
                                               ExportRequestDetailsDto requestDetails, String exportFileType
    ) throws PdfReaderException;

    File exportReportForTxnId(String pan, String txnID, String reportType, boolean returnFileStream,
                              ExportRequestDetailsDto requestDetails, String exportFileType) throws PdfReaderException;

    File exportReport(String pan, String reportGenerationId, String reportType, String startPeriod, String endPeriod,
                      boolean returnFileStream, ExportRequestDetailsDto requestDetails, String exportFileType) throws PdfReaderException;
    Map<String, Object> getExportReportHistory(String pan, String reportType, int pageNo, int limit, String searchKey,
                                               String searchValue,String sortBy, short sortingOrder) throws PdfReaderException;
    Map<String, Object> editExportRemark(String pan, String reportGenId,  ExportRequestDetailsDto requestDetails) throws PdfReaderException;

}
