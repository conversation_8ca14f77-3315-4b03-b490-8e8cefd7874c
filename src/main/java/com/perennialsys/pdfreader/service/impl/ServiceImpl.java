package com.perennialsys.pdfreader.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Projections;
import com.mongodb.client.result.DeleteResult;
import com.perennialsys.pdfreader.repository.SubscriptionServiceRequestAuditLogRepo;
import com.perennialsys.pdfreader.subscriptionService.dto.UserDetailsDto;
import com.perennialsys.pdfreader.bean.TenantStore;
import com.perennialsys.pdfreader.constants.DbQueries;
import com.perennialsys.pdfreader.constants.MysqlDbQueryFields;
import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import com.perennialsys.pdfreader.constants.QueryOperators;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.db.dao.IDao;
import com.perennialsys.pdfreader.db.mongo.MongoDao;
import com.perennialsys.pdfreader.db.mongo.MongoDbCursor;
import com.perennialsys.pdfreader.dto.GenerateReportDetailsDto;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.BoeType;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.enums.SearchAndSorKeyEnum;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.repository.DataNormalizerRepo;
import com.perennialsys.pdfreader.repository.ExportReportTemplateMappingRepo;
import com.perennialsys.pdfreader.repository.FieldIdentificationStrategyRepo;
import com.perennialsys.pdfreader.repository.FileUploadDetailsRepo;
import com.perennialsys.pdfreader.repository.PdfReaderExportReportsManagerRepo;
import com.perennialsys.pdfreader.repository.PdfReaderPdfFieldStrategyMappingRepo;
import com.perennialsys.pdfreader.repository.PdfReaderPdfSectionDetailsRepo;
import com.perennialsys.pdfreader.repository.SftpDetailsRepo;
import com.perennialsys.pdfreader.repository.SftpFilePathMappingRepo;
import com.perennialsys.pdfreader.repository.SftpOperationAuditLogRepo;
import com.perennialsys.pdfreader.repository.TransactionManagerRepo;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.KeyValue;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.DataNormalizerVO;
import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import com.perennialsys.pdfreader.vo.PdfSectionDetailsVO;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeAdditionalDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.SbDetailsDocument;
import flexjson.JSONException;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class ServiceImpl implements IService {

    private final Logger LOG = Logger.getLogger(ServiceImpl.class);

    private final TransactionManagerRepo transactionManagerRepo;

    private final PdfReaderExportReportsManagerRepo reportsManagerRepo;

    private final FileUploadDetailsRepo fileUploadDetailsRepo;

    private final DataNormalizerRepo dataNormalizerRepo;

    private final PdfReaderPdfFieldStrategyMappingRepo pdfReaderPdfFieldStrategyMappingRepo;

    private final PdfReaderPdfSectionDetailsRepo pdfReaderPdfSectionDetailsRepo;
    private final ExportReportTemplateMappingRepo exportReportTemplateMappingRepo;
    private final SftpDetailsRepo sftpDetailsRepo;
    private final SftpOperationAuditLogRepo sftpOperationAuditLogRepo;
    private final SftpFilePathMappingRepo sftpFilePathMappingRepo;
    private final FieldIdentificationStrategyRepo fieldIdentificationStrategyRepo;
    private final SubscriptionServiceRequestAuditLogRepo subscriptionServiceRequestAuditLogRepo;

    private final MongoDao mongoDao;
    private final IDao dao;
    private final TenantStore tenantStore;

    @Autowired
    public ServiceImpl(TransactionManagerRepo transactionManagerRepo, PdfReaderExportReportsManagerRepo reportsManagerRepo,
                       FileUploadDetailsRepo fileUploadDetailsRepo, DataNormalizerRepo dataNormalizerRepo,
                       PdfReaderPdfFieldStrategyMappingRepo pdfReaderPdfFieldStrategyMappingRepo,
                       PdfReaderPdfSectionDetailsRepo pdfReaderPdfSectionDetailsRepo,
                       ExportReportTemplateMappingRepo exportReportTemplateMappingRepo,
                       SftpDetailsRepo sftpDetailsRepo, SftpOperationAuditLogRepo sftpOperationAuditLogRepo,
                       SftpFilePathMappingRepo sftpFilePathMappingRepo,
                       FieldIdentificationStrategyRepo fieldIdentificationStrategyRepo,
                       SubscriptionServiceRequestAuditLogRepo subscriptionServiceRequestAuditLogRepo,
                       MongoDao mongoDao, IDao dao, TenantStore tenantStore) {

        this.transactionManagerRepo = transactionManagerRepo;
        this.reportsManagerRepo = reportsManagerRepo;
        this.fileUploadDetailsRepo = fileUploadDetailsRepo;
        this.dataNormalizerRepo = dataNormalizerRepo;
        this.pdfReaderPdfFieldStrategyMappingRepo = pdfReaderPdfFieldStrategyMappingRepo;
        this.pdfReaderPdfSectionDetailsRepo = pdfReaderPdfSectionDetailsRepo;
        this.exportReportTemplateMappingRepo = exportReportTemplateMappingRepo;
        this.sftpDetailsRepo = sftpDetailsRepo;
        this.sftpOperationAuditLogRepo = sftpOperationAuditLogRepo;
        this.sftpFilePathMappingRepo = sftpFilePathMappingRepo;
        this.fieldIdentificationStrategyRepo = fieldIdentificationStrategyRepo;
        this.subscriptionServiceRequestAuditLogRepo = subscriptionServiceRequestAuditLogRepo;
        this.mongoDao = mongoDao;
        this.dao = dao;
        this.tenantStore = tenantStore;
    }

    @Override
    public List<Object[]> executeSql(String queryString, List<KeyValue> conditions, int skip, int limit){
        return dao.executeSql(queryString, conditions, skip, limit);
    }

    @Override
    public TransactionManagerRepo getTransactionManagerRepo() {
        return this.transactionManagerRepo;
    }

    @Override
    public FileUploadDetailsRepo getFileUploadDetailsRepo() {
        return fileUploadDetailsRepo;
    }

    @Override
    public ExportReportTemplateMappingRepo getExportReportTemplateMappingRepo() {
        return exportReportTemplateMappingRepo;
    }

    @Override
    public SftpDetailsRepo getSftpDetailsRepo() {
        return sftpDetailsRepo;
    }

    @Override
    public SftpOperationAuditLogRepo getSftpOperationAuditLogRepo() {
        return sftpOperationAuditLogRepo;
    }

    @Override
    public SftpFilePathMappingRepo getSftpFilePathMappingRepo() {
        return sftpFilePathMappingRepo;
    }

    @Override
    public FieldIdentificationStrategyRepo getFieldIdentificationStrategyRepo() {
        return fieldIdentificationStrategyRepo;
    }

    @Override
    public PdfReaderPdfFieldStrategyMappingRepo getPdfReaderPdfFieldStrategyMappingRepo() {
        return pdfReaderPdfFieldStrategyMappingRepo;
    }

    @Override
    public SubscriptionServiceRequestAuditLogRepo getSubscriptionServiceRequestAuditLogRepo() {
        return subscriptionServiceRequestAuditLogRepo;
    }

    @Override
    public PdfReaderExportReportsManagerRepo getReportsManagerRepo() {
        return reportsManagerRepo;
    }

    @Override
    public void updateTransactionManger(TransactionManagerVO transactionManagerVO, String status) {
        if (transactionManagerVO != null) {
            transactionManagerVO.setStatus(status);
            transactionManagerVO.setUpdatedAt(new Date());

            transactionManagerRepo.save(transactionManagerVO);
        }
    }

    @Override
    public ExportReportsManagerVO getExportReportManager(String txnId, String pan, String reportType, String status) {
        return reportsManagerRepo.findByTxnIdAndPanAndReportTypeAndStatus(txnId, pan, reportType, status);
    }

    @Override
    public ExportReportsManagerVO saveOrUpdateEximExportReportManager(ExportReportsManagerVO exportReportsManagerVO) {
        return reportsManagerRepo.save(exportReportsManagerVO);
    }

    @Override
    public List<DataNormalizerVO> getDataNormalizerList() {
        LOG.info("START >> ServiceImpl >> getDataNormalizerList");
        List<DataNormalizerVO> normalizerVOS = new ArrayList<>();
        try {
            normalizerVOS = dataNormalizerRepo.findAll();
        } catch (Exception e) {
            LOG.error("Error in ServiceImpl >> getDataNormalizerList >> e >> \n" + e);
        }
        LOG.info("END >> ServiceImpl >> getDataNormalizerList ");
        return normalizerVOS;
    }

    @Override
    public ExportReportsManagerVO checkAndAddEntryIntoExportReportManager(String pan, String txnId, String reportType,
                                                                          String fileNameWithExtension, String status,
                                                                          Date startPeriod, Date endPeriod,
                                                                          String subTxnId, long filesCount,
                                                                          String description) throws PdfReaderException {

        ExportReportsManagerVO exportReportsManagerVO;
        if(StringUtils.isBlank(subTxnId)){
            exportReportsManagerVO = getExportReportManager(txnId, pan, reportType, status);
        }else {
            exportReportsManagerVO = reportsManagerRepo.findByTxnIdAndSubTxnIdAndPanAndReportTypeAndStatus(txnId,
                subTxnId, pan, reportType, status);
        }

        if (exportReportsManagerVO == null) {
            exportReportsManagerVO = new ExportReportsManagerVO();
            exportReportsManagerVO.setCreatedAt(new Date());
            addUserDetails(exportReportsManagerVO);
        }else {
            exportReportsManagerVO.setUpdatedAt(new Date());
        }

        exportReportsManagerVO.setPan(pan);
        exportReportsManagerVO.setTxnId(txnId);
        exportReportsManagerVO.setSubTxnId(subTxnId);
        exportReportsManagerVO.setReportType(reportType);
        exportReportsManagerVO.setStatus(status);
        exportReportsManagerVO.setReportName(fileNameWithExtension);

        GenerateReportDetailsDto generateReportDtlsDto = null;
        boolean createNewOtherDetails = false;
        if(StringUtils.isNotBlank(exportReportsManagerVO.getOtherDetails())){
             try{
                generateReportDtlsDto =  new ObjectMapper()
                    .readValue(exportReportsManagerVO.getOtherDetails(),
                            GenerateReportDetailsDto.class);
            }catch (JSONException e){
                LOG.error("ERROR >> ServiceImpl >> checkAndAddEntryIntoExportReportManager >> PAN >> " + pan + " >> FILE_TXN_ID >> " +
                        reportType + " >> OTHER_DETAILS >> "+exportReportsManagerVO.getOtherDetails()+
                        " >> Error while getting other details from export report manager.");
            } catch (IOException e) {
                throw new PdfReaderException(e);
            }
        }else {
            generateReportDtlsDto = new GenerateReportDetailsDto();
            createNewOtherDetails = true;
        }

        if(null != generateReportDtlsDto && createNewOtherDetails) {
            if (startPeriod != null && endPeriod != null) {

                generateReportDtlsDto.setStartPeriod(DateFormatUtil.formatDateToString(startPeriod,
                        DateFormatUtil.ddMMYYYY_Hyphen));
                generateReportDtlsDto.setEndPeriod(DateFormatUtil.formatDateToString(endPeriod,
                        DateFormatUtil.ddMMYYYY_Hyphen));
            }

            if (filesCount > 0) {
                generateReportDtlsDto.setFileCount(filesCount);
            }

            if (StringUtils.isNotBlank(description)) {
                generateReportDtlsDto.setDescription(description);
            }

            if (StringUtils.isNotBlank(generateReportDtlsDto.toString())) {
                exportReportsManagerVO.setOtherDetails(generateReportDtlsDto.toString());
                exportReportsManagerVO.setRemark(generateReportDtlsDto.getDescription());
            }
        }

        return saveOrUpdateEximExportReportManager(exportReportsManagerVO);
    }

    @Override
    public ExportReportsManagerVO checkAndAddEntryIntoExportReportManager(String pan, String txnId, String reportType,
                                                                          String fileNameWithExtension, String status,
                                                                          String subTxnId,
                                                                          GenerateReportDetailsDto generateReportDtlsDto
    ) throws PdfReaderException {

        ExportReportsManagerVO exportReportsManagerVO;
        String fileGenerationId = UUID.randomUUID().toString();
        if(StringUtils.isBlank(subTxnId)){
            exportReportsManagerVO = getExportReportManager(txnId, pan, reportType, status);
        }else {
            exportReportsManagerVO = reportsManagerRepo.findByTxnIdAndSubTxnIdAndPanAndReportTypeAndStatus(txnId,
                subTxnId, pan, reportType, status);
        }

        if (exportReportsManagerVO == null) {
            exportReportsManagerVO = new ExportReportsManagerVO();
            exportReportsManagerVO.setPan(pan);
            exportReportsManagerVO.setTxnId(txnId);
            exportReportsManagerVO.setSubTxnId(subTxnId);
            exportReportsManagerVO.setReportType(reportType);
            exportReportsManagerVO.setCreatedAt(new Date());
            addUserDetails(exportReportsManagerVO);
        }else {
            exportReportsManagerVO.setUpdatedAt(new Date());
        }

        exportReportsManagerVO.setStatus(status);
        exportReportsManagerVO.setReportName(fileNameWithExtension);
        exportReportsManagerVO.setFileId(fileGenerationId);

        if(null != generateReportDtlsDto) {
            exportReportsManagerVO.setOtherDetails(generateReportDtlsDto.toString());
            exportReportsManagerVO.setRemark(generateReportDtlsDto.getDescription());
        }

        return saveOrUpdateEximExportReportManager(exportReportsManagerVO);
    }

    @Override
    public List<PdfFieldStrategyMappingVO> getPdfFieldStrategyMapping(String sectionKey, String fileType) {
        return pdfReaderPdfFieldStrategyMappingRepo.findAllBySectionDetails_SectionKeyAndFileType(sectionKey, fileType);
    }

    @Override
    public PdfSectionDetailsVO getPdfSectionDetails(String fileType, String sectionKey) {
        return pdfReaderPdfSectionDetailsRepo.findAllByFileTypeAndSectionKey(fileType, sectionKey);
    }

    @Override
    public FileUploadDetailsVO saveOrUpdateFileUploadDetails(FileUploadDetailsVO fileUploadDetailsVO) {
        return fileUploadDetailsRepo.save(fileUploadDetailsVO);
    }

    @Override
    public MongoDbCursor<BoeDetailsDocument> getBoeDetailsRecords(String pan, List<String> subTxnIdList) {
        LOG.info("START >> ServiceImpl >> getBoeDetailsRecords >> PAN " + pan + " >> SUB_TXN_ID >> " + subTxnIdList);

        Bson filter = Filters.and(Filters.in(BoeDetailsFields.SUB_TXN_ID.getValue(), subTxnIdList),
                Filters.eq(BoeDetailsFields.PAN.getValue(), pan));

        LOG.info("END >> ServiceImpl >> getBoeDetailsRecords >> PAN " + pan + " >> SUB_TXN_ID >> " + subTxnIdList);
        return new MongoDbCursor<>(filter, mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT,
            BoeDetailsDocument.class));
    }

    @Override
    public MongoDbCursor<Document> getBoeDetailsDocuments(String pan, List<String> subTxnIdList) {
        LOG.info("START >> ServiceImpl >> getBoeDetailsRecords >> PAN " + pan + " >> SUB_TXN_ID >> " + subTxnIdList);

        Bson filter = Filters.and(Filters.in(BoeDetailsFields.SUB_TXN_ID.getValue(), subTxnIdList),
                Filters.eq(BoeDetailsFields.PAN.getValue(), pan));

        LOG.info("END >> ServiceImpl >> getBoeDetailsRecords >> PAN " + pan + " >> SUB_TXN_ID >> " + subTxnIdList);
        return new MongoDbCursor<>(filter, mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT,
            Document.class));
    }

    @Override
    public MongoDbCursor<BoeDetailsDocument> getBoeDetailsRecords(String pan, Date startDate, Date endDate) {
        LOG.info("START >> ServiceImpl >> getBoeDetailsRecords >> PAN " + pan);

        Bson filter = Filters.and(Filters.eq(BoeDetailsFields.PAN.getValue(), pan),
                Filters.gte(BoeDetailsFields.BOE_DATE.getValue(), startDate),
                Filters.lte(BoeDetailsFields.BOE_DATE.getValue(), endDate));

        LOG.info("END >> ServiceImpl >> getBoeDetailsRecords >> PAN " + pan);
        return new MongoDbCursor<>(filter, mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT,
            BoeDetailsDocument.class));
    }

    @Override
    public MongoDbCursor<Document> getBoeDetailsDocuments(String pan, Date startDate, Date endDate) {
        LOG.info("START >> ServiceImpl >> getBoeDetailsRecords >> PAN " + pan);

        Bson filter = Filters.and(Filters.eq(BoeDetailsFields.PAN.getValue(), pan),
                Filters.gte(BoeDetailsFields.BOE_DATE.getValue(), startDate),
                Filters.lte(BoeDetailsFields.BOE_DATE.getValue(), endDate));

        LOG.info("END >> ServiceImpl >> getBoeDetailsRecords >> PAN " + pan);
        return new MongoDbCursor<>(filter, mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT,
            Document.class));
    }

    @Override
    public List<String> getBoeSubTxnIds(String pan, Date startDate, Date endDate) {
        LOG.info("START >> ServiceImpl >> getBoeSubTxnIds >> PAN " + pan + " >> START_PERIOD >> "+ startDate +
                " >> END_PERIOD >> " + endDate);

        List<String> subTxnIdList = null;

        Bson filter = Filters.and(Filters.eq(BoeDetailsFields.PAN.getValue(), pan),
                Filters.gte(BoeDetailsFields.BOE_DATE.getValue(), startDate),
                Filters.lte(BoeDetailsFields.BOE_DATE.getValue(), endDate));

        Bson projection = Projections.include(BoeDetailsFields.SUB_TXN_ID.getValue());

        List<Document> sbDocList = mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT, Document.class)
                .find(filter).projection(projection).into(new ArrayList<>());

        if(!sbDocList.isEmpty()) {
            subTxnIdList = sbDocList.stream()
                    .map(sbDoc -> sbDoc.getString(BoeDetailsFields.SUB_TXN_ID.getValue()))
                    .distinct().collect(Collectors.toList());
        }

        LOG.info("END >> ServiceImpl >> getBoeSubTxnIds >> PAN " + pan + " >> START_PERIOD >> "+ startDate +
                " >> END_PERIOD >> " + endDate);
        return subTxnIdList;
    }

    @Override
    public List<SbDetailsDocument> getShippingBillDetailsRecords(String pan, List<String> subTxnIdList) {
        LOG.info("START >> ServiceImpl >> getShippingBillDetailsRecords >> PAN " + pan + " >> SUB_TXN_ID >> " +
            subTxnIdList);
        //2. Get the error records from the Import statement database collection.
        MongoCollection<SbDetailsDocument> sbDetailsCollection =
                mongoDao.getMongoCollection(NoSqlDBTables.SB_DETAILS_STATEMENT, SbDetailsDocument.class);
        Bson filter = Filters.and(Filters.in(SBInvoiceDetailsFields.SUB_TXN_ID.getValue(), subTxnIdList),
                Filters.eq(SBInvoiceDetailsFields.PAN.getValue(), pan));

        LOG.info("END >> ServiceImpl >> getShippingBillDetailsRecords >> PAN " + pan + " >> SUB_TXN_ID >> " +
            subTxnIdList);
        return sbDetailsCollection.find(filter).into(new ArrayList<>());
    }

    @Override
    public List<SbDetailsDocument> getShippingBillDetailsRecords(String pan, Date startDate, Date endDate) {
        LOG.info("START >> ServiceImpl >> getShippingBillDetailsRecords >> PAN " + pan);
        //2. Get the error records from the Shipping Bill statement database collection.
        MongoCollection<SbDetailsDocument> sbDetailsCollection =
                mongoDao.getMongoCollection(NoSqlDBTables.SB_DETAILS_STATEMENT, SbDetailsDocument.class);
        Bson filter = Filters.and(Filters.eq(SBInvoiceDetailsFields.PAN.getValue(), pan),
                Filters.gte(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), startDate),
                Filters.lte(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), endDate));

        LOG.info("END >> ServiceImpl >> getShippingBillDetailsRecords >> PAN " + pan);
        return sbDetailsCollection.find(filter).into(new ArrayList<>());
    }

    @Override
    public List<String> getShippingBillSubTxnIds(String pan, Date startDate, Date endDate) {
        LOG.info("START >> ServiceImpl >> getShippingBillSubTxnIds >> PAN " + pan);

        List<String> subTxnIdList = null;

        Bson filter = Filters.and(Filters.eq(SBInvoiceDetailsFields.PAN.getValue(), pan),
                Filters.gte(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), startDate),
                Filters.lte(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), endDate));

        Bson projection = Projections.include(SBInvoiceDetailsFields.SUB_TXN_ID.getValue());

        List<Document> sbDocList = mongoDao.getMongoCollection(NoSqlDBTables.SB_DETAILS_STATEMENT, Document.class)
                .find(filter).projection(projection).into(new ArrayList<>());

        if(!sbDocList.isEmpty()) {
            subTxnIdList = sbDocList.stream()
                .map(sbDoc -> sbDoc.getString(SBInvoiceDetailsFields.SUB_TXN_ID.getValue()))
                .distinct().collect(Collectors.toList());
        }

        LOG.info("END >> ServiceImpl >> getShippingBillSubTxnIds >> PAN " + pan);
        return subTxnIdList;
    }

    @Override
    public TransactionManagerVO getFirstByPanAndTypeOrderByUpdatedAtAsc(String pan, String type) {
        return transactionManagerRepo.getFirstByPanAndTypeOrderByUpdatedAtAsc(pan, type);
    }

    @Override
    public FileUploadDetailsVO saveFileUploadDetailsVo(FileUploadDetailsVO fileUploadDetailsVO) {
        return fileUploadDetailsRepo.save(fileUploadDetailsVO);
    }

    @Override
    public List<TransactionManagerVO> getTransactionsByStatusAndType(String status, List<String> typeList) {
        return transactionManagerRepo.findAllByStatusAndTypeIn(status, typeList);
    }

    @Override
    public int getTransactionCountByStatus(String status, List<String> typeList) {
        return transactionManagerRepo.getTxnCountByStatusAndType(status, typeList);
    }

    @Override
    public long deleteMongoRecordsBasedOnSubTxnId(String collectionName, String subTxnId, String pan){
        Bson filter = Filters.and(Filters.eq(BoeDetailsFields.PAN.getValue(), pan),
                Filters.eq(BoeDetailsFields.SUB_TXN_ID.getValue(), subTxnId));
        DeleteResult result = mongoDao.getMongoCollection(collectionName).deleteMany(filter);

        return result.getDeletedCount();
    }

    @Override
    public List<Document> getBoeDateAndNoFromSubTxnId(String pan, String txnId, int pageNo, int limit,
                                                    String searchKey, String searchValue, String sortBy,
                                                    short sortingOrder) throws PdfReaderException {
        MongoCollection<Document> mongoCollection = mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT,
                Document.class);

        BasicDBList andList = new BasicDBList();
        andList.add(new BasicDBObject(BoeDetailsFields.PAN.getValue(), pan));
        andList.add(new BasicDBObject(BoeDetailsFields.TXN_ID.getValue(), txnId));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if(searchAndSorKeyEnum.isDateType()){
                Date searchDate = DateFormatUtil.formatToDate(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), null == searchDate ? searchValue : searchDate));
            }else if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.BE_TYPE)){
                String boeType = BoeType.getByDescription(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), StringUtils.isNotBlank(boeType) ? boeType : searchValue));
            }else {
                if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.STATUS)){
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), new BasicDBObject(QueryOperators.$in, statusList)));
                }else {
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), searchValue));
                }
            }
        }

        if(StringUtils.isNotBlank(sortBy)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(sortBy);
            sortBy = searchAndSorKeyEnum.getDbKey();
        }else {
            sortBy = "_id";
            sortingOrder = -1;
        }

        Bson sort = Aggregates.sort(new BasicDBObject(sortBy, sortingOrder));

        Document groupById = new Document(BoeDetailsFields.SUB_TXN_ID.getValue(),
                "$" + BoeDetailsFields.SUB_TXN_ID.getValue());

        Bson aggregateQuery = Aggregates.group(groupById,
                Accumulators.first(BoeDetailsFields.BOE_NO.getValue(),
                        "$" + BoeDetailsFields.BOE_NO.getValue()),
                Accumulators.first(BoeDetailsFields.BOE_DATE.getValue(),
                        "$" + BoeDetailsFields.BOE_DATE.getValue()),
                Accumulators.first(BoeDetailsFields.BE_TYPE.getValue(),
                        "$" + BoeDetailsFields.BE_TYPE.getValue()),
                Accumulators.first(BoeDetailsFields.SUB_TXN_ID.getValue(),
                        "$" + BoeDetailsFields.SUB_TXN_ID.getValue())
                );
        Bson matchQuery = Aggregates.match(new BasicDBObject(QueryOperators.$and , andList));

        return  mongoCollection.aggregate(Arrays.asList(matchQuery, aggregateQuery, sort, Aggregates.skip(pageNo * limit),
                Aggregates.limit(limit))).into(new ArrayList<>());
    }

    @Override
    public List<Document> getSbDateAndNoFromSubTxnId(String pan, String txnId, int pageNo, int limit,
                                                    String searchKey, String searchValue, String sortBy,
                                                    short sortingOrder) throws PdfReaderException {
        MongoCollection<Document> mongoCollection = mongoDao.getMongoCollection(NoSqlDBTables.SB_DETAILS_STATEMENT,
                Document.class);

        BasicDBList andList = new BasicDBList();
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.PAN.getValue(), pan));
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.TXN_ID.getValue(), txnId));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if(searchAndSorKeyEnum.isDateType()){
                Date searchDate = DateFormatUtil.formatToDate(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), null == searchDate ? searchValue : searchDate));
            }else {
                if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.STATUS)){
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), new BasicDBObject(QueryOperators.$in, statusList)));
                }else {
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), searchValue));
                }
            }
        }

        if(StringUtils.isNotBlank(sortBy)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(sortBy);
            sortBy = searchAndSorKeyEnum.getDbKey();
        }else {
            sortBy = "_id";
            sortingOrder = -1;
        }

        Bson sort = Aggregates.sort(new BasicDBObject(sortBy, sortingOrder));

        Document groupById = new Document(SBInvoiceDetailsFields.SUB_TXN_ID.getValue(),
                "$" + SBInvoiceDetailsFields.SUB_TXN_ID.getValue());

        Bson aggregateQuery = Aggregates.group(groupById,
                Accumulators.first(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue(),
                        "$" + SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue()),
                Accumulators.first(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(),
                        "$" + SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue()),
                Accumulators.first(SBInvoiceDetailsFields.SUB_TXN_ID.getValue(),
                        "$" + SBInvoiceDetailsFields.SUB_TXN_ID.getValue())
        );

        Bson matchQuery = Aggregates.match(new BasicDBObject(QueryOperators.$and , andList));

        return  mongoCollection.aggregate(Arrays.asList(matchQuery, aggregateQuery, sort, Aggregates.skip(pageNo * limit),
                Aggregates.limit(limit))).into(new ArrayList<>());
    }

    @Override
    public long getBoeCountFromTxnId(String pan, String txnId, String searchKey, String searchValue) throws PdfReaderException {
        MongoCollection<Document> mongoCollection = mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT,
                Document.class);

        long count = 0;

        BasicDBList andList = new BasicDBList();
        andList.add(new BasicDBObject(BoeDetailsFields.PAN.getValue(), pan));
        andList.add(new BasicDBObject(BoeDetailsFields.TXN_ID.getValue(), txnId));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if(searchAndSorKeyEnum.isDateType()){
                Date searchDate = DateFormatUtil.formatToDate(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), null == searchDate ? searchValue : searchDate));
            }else if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.BE_TYPE)){
                String boeType = BoeType.getByDescription(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), StringUtils.isNotBlank(boeType) ? boeType : searchValue));
            }else {
                if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.STATUS)){
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), new BasicDBObject(QueryOperators.$in, statusList)));
                }else {
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), searchValue));
                }
            }
        }

        Document groupById = new Document(BoeDetailsFields.SUB_TXN_ID.getValue(),
                "$" + BoeDetailsFields.SUB_TXN_ID.getValue());

        Bson aggregateQuery = Aggregates.group(groupById, Accumulators.sum("count",1));

        Bson matchQuery = Aggregates.match(new BasicDBObject(QueryOperators.$and , andList));

        List<Document> docList =  mongoCollection.aggregate(Arrays.asList(matchQuery, aggregateQuery))
                .into(new ArrayList<>());

        if(!docList.isEmpty() && null != docList.get(0)){
            count = docList.size();
        }

        return count;
    }

    @Override
    public long getSbCountFromTxnId(String pan, String txnId, String searchKey, String searchValue) throws PdfReaderException {
        MongoCollection<Document> mongoCollection = mongoDao.getMongoCollection(NoSqlDBTables.SB_DETAILS_STATEMENT,
                Document.class);
        long count = 0;
        BasicDBList andList = new BasicDBList();
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.PAN.getValue(), pan));
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.TXN_ID.getValue(), txnId));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if(searchAndSorKeyEnum.isDateType()){
                Date searchDate = DateFormatUtil.formatToDate(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), null == searchDate ? searchValue : searchDate));
            }else {
                if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.STATUS)){
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), new BasicDBObject(QueryOperators.$in, statusList)));
                }else {
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), searchValue));
                }
            }
        }

        Document groupById = new Document(SBInvoiceDetailsFields.SUB_TXN_ID.getValue(),
                "$" + SBInvoiceDetailsFields.SUB_TXN_ID.getValue());

        Bson aggregateQuery = Aggregates.group(groupById, Accumulators.sum("count",1));

        Bson matchQuery = Aggregates.match(new BasicDBObject(QueryOperators.$and , andList));

        List<Document> docList =  mongoCollection.aggregate(Arrays.asList(matchQuery, aggregateQuery))
                .into(new ArrayList<>());

        if(!docList.isEmpty() && null != docList.get(0)){
            count = docList.size();
        }

        return count;
    }

    @Override
    public List<Document> getBoeDateAndNoFromPeriod(String pan, Date startDate, Date endDate, int pageNo, int limit,
                                                    String searchKey, String searchValue, String sortBy,
                                                    short sortingOrder) throws PdfReaderException {
        MongoCollection<Document> mongoCollection = mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT,
                Document.class);

        BasicDBList andList = new BasicDBList();
        andList.add(new BasicDBObject(BoeDetailsFields.PAN.getValue(), pan));
        andList.add(new BasicDBObject(BoeDetailsFields.BOE_DATE.getValue(), new BasicDBObject(QueryOperators.$gte, startDate)));
        andList.add(new BasicDBObject(BoeDetailsFields.BOE_DATE.getValue(), new BasicDBObject(QueryOperators.$lte, endDate)));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if(searchAndSorKeyEnum.isDateType()){
                Date searchDate = DateFormatUtil.formatToDate(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), null == searchDate ? searchValue : searchDate));
            }else if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.BE_TYPE)){
                String boeType = BoeType.getByDescription(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), StringUtils.isNotBlank(boeType) ? boeType : searchValue));
            }else {
                if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.STATUS)){
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), new BasicDBObject(QueryOperators.$in, statusList)));
                }else {
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), searchValue));
                }
            }
        }

        if(StringUtils.isNotBlank(sortBy)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(sortBy);
            sortBy = searchAndSorKeyEnum.getDbKey();
        }else {
            sortBy = "_id";
            sortingOrder = -1;
        }

        Bson sort = Aggregates.sort(new BasicDBObject(sortBy, sortingOrder));

        Document groupById = new Document(BoeDetailsFields.SUB_TXN_ID.getValue(),
                "$" + BoeDetailsFields.SUB_TXN_ID.getValue());

        Bson aggregateQuery = Aggregates.group(groupById,
                Accumulators.first(BoeDetailsFields.BOE_NO.getValue(),
                        "$" + BoeDetailsFields.BOE_NO.getValue()),
                Accumulators.first(BoeDetailsFields.BOE_DATE.getValue(),
                        "$" + BoeDetailsFields.BOE_DATE.getValue()),
                Accumulators.first(BoeDetailsFields.BE_TYPE.getValue(),
                        "$" + BoeDetailsFields.BE_TYPE.getValue()),
                Accumulators.first(BoeDetailsFields.SUB_TXN_ID.getValue(),
                        "$" + BoeDetailsFields.SUB_TXN_ID.getValue())
                );

        Bson matchQuery = Aggregates.match(new BasicDBObject(QueryOperators.$and , andList));

        return  mongoCollection.aggregate(Arrays.asList(matchQuery, aggregateQuery, sort, Aggregates.skip(pageNo * limit),
                Aggregates.limit(limit))).into(new ArrayList<>());
    }

    @Override
    public long getBoeCountFromPeriod(String pan, Date startDate, Date endDate,
                                      String searchKey, String searchValue) throws PdfReaderException {
        MongoCollection<Document> mongoCollection = mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT,
                Document.class);

        long count = 0;

        BasicDBList andList = new BasicDBList();
        andList.add(new BasicDBObject(BoeDetailsFields.PAN.getValue(), pan));
        andList.add(new BasicDBObject(BoeDetailsFields.BOE_DATE.getValue(), new BasicDBObject(QueryOperators.$gte, startDate)));
        andList.add(new BasicDBObject(BoeDetailsFields.BOE_DATE.getValue(), new BasicDBObject(QueryOperators.$lte, endDate)));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if(searchAndSorKeyEnum.isDateType()){
                Date searchDate = DateFormatUtil.formatToDate(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), null == searchDate ? searchValue : searchDate));
            }else if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.BE_TYPE)){
                String boeType = BoeType.getByDescription(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), StringUtils.isNotBlank(boeType) ? boeType : searchValue));
            }else {
                if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.STATUS)){
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), new BasicDBObject(QueryOperators.$in, statusList)));
                }else {
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), searchValue));
                }
            }
        }

        Document groupById = new Document(BoeDetailsFields.PAN.getValue(), "$" + BoeDetailsFields.PAN.getValue())
                .append(BoeDetailsFields.SUB_TXN_ID.getValue(),"$" + BoeDetailsFields.SUB_TXN_ID.getValue());

        Bson aggregateQuery = Aggregates.group(groupById, Accumulators.sum("count",1));

        Bson matchQuery = Aggregates.match(new BasicDBObject(QueryOperators.$and , andList));

        List<Document> docList =  mongoCollection.aggregate(Arrays.asList(matchQuery, aggregateQuery))
            .into(new ArrayList<>());

        if(!docList.isEmpty() && null != docList.get(0)){
            count = docList.size();
        }

        return count;
    }

    @Override
    public List<Document> getSbDateAndNoFromPeriod(String pan, Date startDate, Date endDate, int pageNo, int limit,
                                                   String searchKey, String searchValue,String sortBy,
                                                   short sortingOrder) throws PdfReaderException {
        MongoCollection<Document> mongoCollection = mongoDao.getMongoCollection(NoSqlDBTables.SB_DETAILS_STATEMENT,
                Document.class);

        BasicDBList andList = new BasicDBList();
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.PAN.getValue(), pan));
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), new BasicDBObject(QueryOperators.$gte, startDate)));
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), new BasicDBObject(QueryOperators.$lte, endDate)));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if(searchAndSorKeyEnum.isDateType()){
                Date searchDate = DateFormatUtil.formatToDate(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), null == searchDate ? searchValue : searchDate));
            }else {
                if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.STATUS)){
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), new BasicDBObject(QueryOperators.$in, statusList)));
                }else {
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), searchValue));
                }
            }
        }

        if(StringUtils.isNotBlank(sortBy)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(sortBy);
            sortBy = searchAndSorKeyEnum.getDbKey();
        }else {
            sortBy = "_id";
            sortingOrder = -1;
        }

        Bson sort = Aggregates.sort(new BasicDBObject(sortBy, sortingOrder));

        Document groupById = new Document(SBInvoiceDetailsFields.SUB_TXN_ID.getValue(),
                "$" + SBInvoiceDetailsFields.SUB_TXN_ID.getValue());

        Bson aggregateQuery = Aggregates.group(groupById,
                Accumulators.first(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue(),
                        "$" + SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue()),
                Accumulators.first(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(),
                        "$" + SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue()),
                Accumulators.first(SBInvoiceDetailsFields.SUB_TXN_ID.getValue(),
                        "$" + SBInvoiceDetailsFields.SUB_TXN_ID.getValue())
        );

        Bson matchQuery = Aggregates.match(new BasicDBObject(QueryOperators.$and , andList));

        return  mongoCollection.aggregate(Arrays.asList(matchQuery, aggregateQuery, sort, Aggregates.skip(pageNo * limit),
                Aggregates.limit(limit))).into(new ArrayList<>());
    }

    @Override
    public long getSbCountFromPeriod(String pan, Date startDate, Date endDate,
                                     String searchKey, String searchValue) throws PdfReaderException {
        MongoCollection<Document> mongoCollection = mongoDao.getMongoCollection(NoSqlDBTables.SB_DETAILS_STATEMENT,
                Document.class);

        long count = 0;

        BasicDBList andList = new BasicDBList();
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.PAN.getValue(), pan));
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), new BasicDBObject(QueryOperators.$gte, startDate)));
        andList.add(new BasicDBObject(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), new BasicDBObject(QueryOperators.$lte, endDate)));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchAndSorKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if(searchAndSorKeyEnum.isDateType()){
                Date searchDate = DateFormatUtil.formatToDate(searchValue);
                andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), null == searchDate ? searchValue : searchDate));
            }else {
                if(searchAndSorKeyEnum.equals(SearchAndSorKeyEnum.STATUS)){
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), new BasicDBObject(QueryOperators.$in, statusList)));
                }else {
                    andList.add(new BasicDBObject(searchAndSorKeyEnum.getDbKey(), searchValue));
                }
            }
        }

        Document groupById = new Document(SBInvoiceDetailsFields.PAN.getValue(), "$" + SBInvoiceDetailsFields.PAN
                    .getValue())
                .append(SBInvoiceDetailsFields.SUB_TXN_ID.getValue(), "$" + SBInvoiceDetailsFields.SUB_TXN_ID
                    .getValue());

        Bson aggregateQuery = Aggregates.group(groupById, Accumulators.sum("count",1));

        Bson matchQuery = Aggregates.match(new BasicDBObject(QueryOperators.$and , andList));
        List<Document> docList =  mongoCollection.aggregate(Arrays.asList(matchQuery, aggregateQuery))
            .into(new ArrayList<>());

        if(!docList.isEmpty() && null != docList.get(0)){
            count = docList.size();
        }

        return count;
    }

    @Override
    public MongoDbCursor<BoeAdditionalDetailsDocument> getBoeAdditionalDetailsRecords(String pan,
                                                                                      List<String> subTxnIdList) {
        LOG.info("START >> ServiceImpl >> getBoeAdditionalDetailsRecords >> PAN " + pan + " >> SUB_TXN_ID >> " +
            subTxnIdList);

        Bson filter = Filters.and(Filters.in(BoeDetailsFields.SUB_TXN_ID.getValue(), subTxnIdList),
                Filters.eq(BoeDetailsFields.PAN.getValue(), pan));

        LOG.info("END >> ServiceImpl >> getBoeAdditionalDetailsRecords >> PAN " + pan + " >> SUB_TXN_ID >> " +
            subTxnIdList);

        return new MongoDbCursor<>(filter, mongoDao.getMongoCollection(NoSqlDBTables.BOE_ADDITIONAL_DETAILS_STATEMENT,
                BoeAdditionalDetailsDocument.class));
    }

    @Override
    public List<ExportReportsManagerVO> getExportReportManagerVoList(String pan, List<String> reportTypeList, int skip,
                                                                     int limit, String searchKey, String searchValue,
                                                                     String sortBy, short sortingOrder) throws PdfReaderException {
        LOG.info("START >> ServiceImpl >> getExportReportManagerVoList >> PAN " + pan + " >> SEARCH_KEY >> " +
                searchKey + " >> SEARCH_VALUE >> "+searchValue+" >> SORT_BY >> "+sortBy+" >> SORT_ORDER >> "+sortingOrder);
        String query = DbQueries.GET_EXPORT_REPORT_HQL;
        List<KeyValue> conditions = new ArrayList<>();
        conditions.add(new KeyValue(MysqlDbQueryFields.PAN, pan));
        conditions.add(new KeyValue(MysqlDbQueryFields.REPORT_TYPE, reportTypeList));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            switch (searchKeyEnum){
                case EXPORT_DATE_TIME: {
                    Date searchStartDate = DateFormatUtil.formatToDate(searchValue);
                    Date searchEndDate = DateFormatUtil.getDayEndTime(searchStartDate);

                    query = DbQueries.GET_EXPORT_REPORT_FROM_CREATED_AT_BETWEEN_HQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.START_DATE, searchStartDate));
                    conditions.add(new KeyValue(MysqlDbQueryFields.END_DATE, searchEndDate));
                    break;
                }
                case EXPORTED_BY: {
                    query = DbQueries.GET_EXPORT_REPORT_FROM_EXPORT_USER_NAME_LIKE_HQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.EXPORTED_BY_USER_NAME, "%"+searchValue+"%"));
                    break;
                }
                case DESCRIPTION: {
                    query = DbQueries.GET_EXPORT_REPORT_FROM_REMARK_LIKE_HQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.REMARK, "%"+searchValue+"%"));
                    break;
                }
                default: {
                    LOG.error("ERROR >> ServiceImpl >> getExportReportManagerVoList >> PAN " + pan +
                        " >> SEARCH_KEY >> " + searchKey + " >> SEARCH_VALUE >> " + searchValue + " >> SORT_BY >> " +
                        sortBy + " >> SORT_ORDER >> " + sortingOrder + " >> Invalid Search key.");
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
                }
            }
        }

        if(StringUtils.isBlank(sortBy)){
           sortBy = "id";
           sortingOrder = -1;
        }else {
            sortBy = SearchAndSorKeyEnum.getEnumFromDisplayKey(sortBy).getDbKey();
        }

        query = query + " order by "+sortBy+" "+(sortingOrder > 0 ? "asc":"desc");

        List<ExportReportsManagerVO> exportReportsManagerVOList = dao.executeHql(ExportReportsManagerVO.class, query,
            conditions, skip * limit, limit);

        LOG.info("END >> ServiceImpl >> getExportReportManagerVoList >> PAN " + pan + " >> SEARCH_KEY >> " +
                searchKey + " >> SEARCH_VALUE >> "+searchValue+" >> SORT_BY >> "+sortBy+" >> SORT_ORDER >> "+sortingOrder);

        return exportReportsManagerVOList;
    }

    @Override
    public long getExportReportManagerVoCount(String pan, List<String> reportTypeList, String searchKey, String searchValue) throws PdfReaderException {
        LOG.info("START >> ServiceImpl >> getExportReportManagerVoList >> PAN " + pan + " >> SEARCH_KEY >> " +
                searchKey + " >> SEARCH_VALUE >> "+searchValue);
        String query = DbQueries.GET_EXPORT_REPORT_COUNT_HQL;
        List<KeyValue> conditions = new ArrayList<>();
        conditions.add(new KeyValue(MysqlDbQueryFields.PAN, pan));
        conditions.add(new KeyValue(MysqlDbQueryFields.REPORT_TYPE, reportTypeList));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            switch (searchKeyEnum){
                case EXPORT_DATE_TIME: {
                    Date searchStartDate = DateFormatUtil.formatToDate(searchValue);
                    Date searchEndDate = DateFormatUtil.getDayEndTime(searchStartDate);

                    query = DbQueries.GET_EXPORT_REPORT_COUNT_FROM_CREATED_AT_BETWEEN_HQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.START_DATE, searchStartDate));
                    conditions.add(new KeyValue(MysqlDbQueryFields.END_DATE, searchEndDate));
                    break;
                }
                case EXPORTED_BY: {
                    query = DbQueries.GET_EXPORT_REPORT_COUNT_FROM_EXPORT_USER_NAME_LIKE_HQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.EXPORTED_BY_USER_NAME, "%"+searchValue+"%"));
                    break;
                }
                case DESCRIPTION: {
                    query = DbQueries.GET_EXPORT_REPORT_COUNT_FROM_REMARK_LIKE_HQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.REMARK, "%"+searchValue+"%"));
                    break;
                }
                default: {
                    LOG.error("ERROR >> ServiceImpl >> getExportReportManagerVoList >> PAN " + pan +
                            " >> SEARCH_KEY >> " + searchKey + " >> SEARCH_VALUE >> " + searchValue + " >> Invalid Search key.");
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
                }
            }
        }

        long count = dao.executeCountHql(query, conditions);

        LOG.info("END >> ServiceImpl >> getExportReportManagerVoList >> PAN " + pan + " >> SEARCH_KEY >> " +
                searchKey + " >> SEARCH_VALUE >> "+searchValue);

        return count;
    }

    @Override
    public List<Object[]> getFileUploadDeatails(String pan, String fileType, int skip, int limit,
                                                String searchKey, String searchValue, String sortBy,
                                                short sortingOrder) throws PdfReaderException {
        LOG.info("START >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan + " >> FILE_TYPE >> "+fileType+
                " >> SEARCH_KEY >> " +searchKey + " >> SEARCH_VALUE >> "+searchValue);
        List<Object []> result;
        String query = DbQueries.GET_FILE_UPLOAD_DETAILS_SQL;
        List<KeyValue> conditions = new ArrayList<>();
        conditions.add(new KeyValue(MysqlDbQueryFields.PAN, pan));
        conditions.add(new KeyValue(MysqlDbQueryFields.FILE_TYPE, fileType));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            switch (searchKeyEnum){
                case LAST_UPDATED_AT: {
                    Date searchStartDate = DateFormatUtil.formatToDate(searchValue);
                    Date searchEndDate = DateFormatUtil.getDayEndTime(searchStartDate);

                    query = DbQueries.GET_FILE_UPLOAD_DETAILS_FROM_UPDATED_AT_SQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.START_DATE, searchStartDate));
                    conditions.add(new KeyValue(MysqlDbQueryFields.END_DATE, searchEndDate));
                    break;
                }
                case STATUS: {
                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);

                    if(!statusList.isEmpty() && statusList.contains(TransactionStatus.DELETED.name())){
                        query = DbQueries.GET_FILE_UPLOAD_DETAILS_FOR_DELETED_STATUS_SQL;
                    }else {
                        query = DbQueries.GET_FILE_UPLOAD_DETAILS_FROM_STATUS_SQL;
                        conditions.add(new KeyValue(MysqlDbQueryFields.STATUS_LIST, statusList.isEmpty() ? Collections.singletonList(searchValue) : statusList));
                        conditions.add(new KeyValue(MysqlDbQueryFields.IS_DELETED, false));
                    }
                    break;
                }
                case UPLOADED_BY: {
                    query = DbQueries.GET_FILE_UPLOAD_DETAILS_FROM_UPDATED_BY_SQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.UPDATED_BY_USER_NAME, "%"+searchValue+"%"));
                    break;
                }
                default: {
                    LOG.error("ERROR >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan +
                            " >> FILE_TYPE >> "+fileType+" >> SEARCH_KEY >> " + searchKey + " >> SEARCH_VALUE >> " +
                            searchValue + " >> Invalid Search key.");
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
                }
            }
        }

        if(StringUtils.isBlank(sortBy)){
            sortBy = "tm.id";
            sortingOrder = -1;
        }else {
            sortBy = "tm."+SearchAndSorKeyEnum.getEnumFromDisplayKey(sortBy).getDbKey();
        }

        query = query + " order by "+sortBy+" "+(sortingOrder > 0 ? "asc":"desc");

        result = dao.executeSql(query, conditions, skip, limit);

        LOG.info("END >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan + " >> FILE_TYPE >> "+fileType+" >> SEARCH_KEY >> " +
                searchKey + " >> SEARCH_VALUE >> "+searchValue);

        return result;
    }

    @Override
    public long getFileUploadDeatailsCount(String pan, String fileType, String searchKey, String searchValue) throws PdfReaderException {
        LOG.info("START >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan + " >> FILE_TYPE >> "+fileType+
                " >> SEARCH_KEY >> " +searchKey + " >> SEARCH_VALUE >> "+searchValue);
        String query = DbQueries.GET_FILE_UPLOAD_DETAILS_COUNT_SQL;
        List<KeyValue> conditions = new ArrayList<>();
        conditions.add(new KeyValue(MysqlDbQueryFields.PAN, pan));
        conditions.add(new KeyValue(MysqlDbQueryFields.FILE_TYPE, fileType));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            switch (searchKeyEnum){
                case LAST_UPDATED_AT: {
                    Date searchStartDate = DateFormatUtil.formatToDate(searchValue);
                    Date searchEndDate = DateFormatUtil.getDayEndTime(searchStartDate);

                    query = DbQueries.GET_FILE_UPLOAD_DETAILS_COUNT_FROM_UPDATED_AT_SQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.START_DATE, searchStartDate));
                    conditions.add(new KeyValue(MysqlDbQueryFields.END_DATE, searchEndDate));
                    break;
                }
                case STATUS: {

                    List<String> statusList = TransactionStatus.getByDisplayName(searchValue);

                    if(!statusList.isEmpty() && statusList.contains(TransactionStatus.DELETED.name())){
                        query = DbQueries.GET_FILE_UPLOAD_DETAILS_COUNT_FOR_DELETED_STATUS_SQL;
                    }else {
                        query = DbQueries.GET_FILE_UPLOAD_DETAILS_COUNT_FROM_STATUS_SQL;
                        conditions.add(new KeyValue(MysqlDbQueryFields.STATUS_LIST, statusList.isEmpty() ? Collections.singletonList(searchValue) : statusList));
                        conditions.add(new KeyValue(MysqlDbQueryFields.IS_DELETED, false));
                    }
                    break;
                }
                case UPLOADED_BY: {
                    query = DbQueries.GET_FILE_UPLOAD_DETAILS_COUNT_FROM_UPDATED_BY_SQL;

                    conditions.add(new KeyValue(MysqlDbQueryFields.UPDATED_BY_USER_NAME, "%"+searchValue+"%"));
                    break;
                }
                default: {
                    LOG.error("ERROR >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan +
                            " >> FILE_TYPE >> "+fileType+" >> SEARCH_KEY >> " + searchKey + " >> SEARCH_VALUE >> " +
                            searchValue + " >> Invalid Search key.");
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
                }
            }
        }

        long count = dao.executeCountSql(query, conditions);

        LOG.info("END >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan + " >> FILE_TYPE >> "+fileType+" >> SEARCH_KEY >> " +
                searchKey + " >> SEARCH_VALUE >> "+searchValue);

        return count;
    }

    @Override
    public List<FileUploadDetailsVO> getFileUploadDeatails(String pan, String txnId, List<String> statusList, int skip, int limit,
                                                String searchKey, String searchValue, String sortBy,
                                                short sortingOrder) throws PdfReaderException {
        LOG.info("START >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan + " >> STATUS_LIST >> "+statusList+
                " >> SEARCH_KEY >> " +searchKey + " >> SEARCH_VALUE >> "+searchValue);
        List<FileUploadDetailsVO> result;
        String query = DbQueries.GET_FILE_UPLOAD_DTLS_FROM_TXN_PAN_STATUS_HQL;
        List<KeyValue> conditions = new ArrayList<>();
        conditions.add(new KeyValue(MysqlDbQueryFields.PAN, pan));
        conditions.add(new KeyValue(MysqlDbQueryFields.STATUS_LIST, statusList));
        conditions.add(new KeyValue(MysqlDbQueryFields.TXN_ID, txnId));
        conditions.add(new KeyValue(MysqlDbQueryFields.IS_DELETED, false));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if (searchKeyEnum == SearchAndSorKeyEnum.FILE_NAME) {
                query = DbQueries.GET_FILE_UPLOAD_DTLS_FROM_TXN_PAN_STATUS_FILE_NAME_HQL;

                conditions.add(new KeyValue(MysqlDbQueryFields.FILE_DISPLAY_NAME, "%"+searchValue+"%"));
            } else {
                LOG.error("ERROR >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan +
                        " >> STATUS_LIST >> " + statusList + " >> SEARCH_KEY >> " + searchKey + " >> SEARCH_VALUE >> " +
                        searchValue + " >> Invalid Search key.");
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
            }
        }

        if(StringUtils.isBlank(sortBy)){
            sortBy = "id";
            sortingOrder = -1;
        }else {
            sortBy = SearchAndSorKeyEnum.getEnumFromDisplayKey(sortBy).getDbKey();
        }

        query = query + " order by "+sortBy+" "+(sortingOrder > 0 ? "asc":"desc");

        result = dao.executeHql(FileUploadDetailsVO.class, query, conditions, skip * limit, limit);

        LOG.info("END >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan + " >> STATUS_LIST >> "+statusList+" >> SEARCH_KEY >> " +
                searchKey + " >> SEARCH_VALUE >> "+searchValue);

        return result;
    }

    @Override
    public long getFileUploadDeatailsCount(String pan, String txnId, List<String> statusList, String searchKey,
                                           String searchValue) throws PdfReaderException {
        LOG.info("START >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan + " >> STATUS_LIST >> "+statusList+
                " >> SEARCH_KEY >> " +searchKey + " >> SEARCH_VALUE >> "+searchValue);

        String query = DbQueries.GET_FILE_UPLOAD_DTLS_COUNT_FROM_TXN_PAN_STATUS_HQL;
        List<KeyValue> conditions = new ArrayList<>();
        conditions.add(new KeyValue(MysqlDbQueryFields.PAN, pan));
        conditions.add(new KeyValue(MysqlDbQueryFields.STATUS_LIST, statusList));
        conditions.add(new KeyValue(MysqlDbQueryFields.TXN_ID, txnId));
        conditions.add(new KeyValue(MysqlDbQueryFields.IS_DELETED, false));

        if(StringUtils.isNotBlank(searchKey) && StringUtils.isNotBlank(searchValue)){
            SearchAndSorKeyEnum searchKeyEnum = SearchAndSorKeyEnum.getEnumFromDisplayKey(searchKey);
            if (searchKeyEnum == SearchAndSorKeyEnum.FILE_NAME) {
                query = DbQueries.GET_FILE_UPLOAD_DTLS_COUNT_FROM_TXN_PAN_STATUS_FILE_NAME_HQL;

                conditions.add(new KeyValue(MysqlDbQueryFields.FILE_DISPLAY_NAME, "%"+searchValue+"%"));
            } else {
                LOG.error("ERROR >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan +
                        " >> STATUS_LIST >> " + statusList + " >> SEARCH_KEY >> " + searchKey + " >> SEARCH_VALUE >> " +
                        searchValue + " >> Invalid Search key.");
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
            }
        }

        long count = dao.executeCountSql(query, conditions);

        LOG.info("END >> ServiceImpl >> getFileUploadDeatails >> PAN " + pan + " >> STATUS_LIST >> "+statusList+" >> SEARCH_KEY >> " +
                searchKey + " >> SEARCH_VALUE >> "+searchValue);

        return count;
    }

    @Override
    public void addUserDetails(ExportReportsManagerVO exportReportVo) throws PdfReaderException {
        String userDtlsString = tenantStore.getDtls("user-details");
        LOG.info("START >> ServiceImpl >> addUserDetails >> PAN >> "+exportReportVo.getPan());
        if(StringUtils.isNotBlank(userDtlsString)){
            try {
                UserDetailsDto userDetailsDto = new ObjectMapper().readValue(userDtlsString, UserDetailsDto.class);
                exportReportVo.setUserId(userDetailsDto.getUserId());
                exportReportVo.setUserName(userDetailsDto.getFirstName()+" "+userDetailsDto.getLastName());
            }catch (IOException e){
                LOG.error("ERROR >> ServiceImpl >> addUserDetails >> PAN >> "+exportReportVo.getPan()+" >> Error while getting user details");
                throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.ERROR_WHILE_GETTING_USER_DETAILS);
            }
        }else {
            LOG.error("ERROR >> ServiceImpl >> addUserDetails >> PAN >> "+exportReportVo.getPan()+" >> User details not found");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.USER_DETAILS_NOT_FOUND);
        }
        LOG.info("END >> ServiceImpl >> addUserDetails >> PAN >> "+exportReportVo.getPan());
    }

    @Override
    public void addUserDetails(TransactionManagerVO txnVo) throws PdfReaderException {
        String userDtlsString = tenantStore.getDtls("user-details");
        LOG.info("START >> ServiceImpl >> addUserDetails >> PAN >> "+txnVo.getPan());
        if(StringUtils.isNotBlank(userDtlsString)){
            try {
                UserDetailsDto userDetailsDto = new ObjectMapper().readValue(userDtlsString, UserDetailsDto.class);
                txnVo.setUserId(userDetailsDto.getUserId());
                txnVo.setUserName(userDetailsDto.getFirstName()+" "+userDetailsDto.getLastName());
            }catch (IOException e){
                LOG.error("ERROR >> ServiceImpl >> addUserDetails >> PAN >> "+txnVo.getPan()+" >> Error while getting user details");
                throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.ERROR_WHILE_GETTING_USER_DETAILS);
            }
        }else {
            LOG.error("ERROR >> ServiceImpl >> addUserDetails >> PAN >> "+txnVo.getPan()+" >> User details not found");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.USER_DETAILS_NOT_FOUND);
        }
        LOG.info("END >> ServiceImpl >> addUserDetails >> PAN >> "+txnVo.getPan());
    }

    @Override
    public void addUserDetails(FileUploadDetailsVO fileUploadDetailsVO) throws PdfReaderException {
        String userDtlsString = tenantStore.getDtls("user-details");
        LOG.info("START >> ServiceImpl >> addUserDetails >> PAN >> "+fileUploadDetailsVO.getPan());
        if(StringUtils.isNotBlank(userDtlsString)){
            try {
                UserDetailsDto userDetailsDto = new ObjectMapper().readValue(userDtlsString, UserDetailsDto.class);
                fileUploadDetailsVO.setUserId(userDetailsDto.getUserId());
                fileUploadDetailsVO.setUserName(userDetailsDto.getFirstName()+" "+userDetailsDto.getLastName());
            }catch (IOException e){
                LOG.error("ERROR >> ServiceImpl >> addUserDetails >> PAN >> "+fileUploadDetailsVO.getPan()+" >> Error while getting user details");
                throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.ERROR_WHILE_GETTING_USER_DETAILS);
            }
        }else {
            LOG.error("ERROR >> ServiceImpl >> addUserDetails >> PAN >> "+fileUploadDetailsVO.getPan()+" >> User details not found");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.USER_DETAILS_NOT_FOUND);
        }
        LOG.info("END >> ServiceImpl >> addUserDetails >> PAN >> "+fileUploadDetailsVO.getPan());
    }
}
