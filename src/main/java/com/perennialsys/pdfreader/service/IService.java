package com.perennialsys.pdfreader.service;

import com.perennialsys.pdfreader.db.mongo.MongoDbCursor;
import com.perennialsys.pdfreader.dto.GenerateReportDetailsDto;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.repository.ExportReportTemplateMappingRepo;
import com.perennialsys.pdfreader.repository.FieldIdentificationStrategyRepo;
import com.perennialsys.pdfreader.repository.FileUploadDetailsRepo;
import com.perennialsys.pdfreader.repository.PdfReaderExportReportsManagerRepo;
import com.perennialsys.pdfreader.repository.PdfReaderPdfFieldStrategyMappingRepo;
import com.perennialsys.pdfreader.repository.SftpDetailsRepo;
import com.perennialsys.pdfreader.repository.SftpFilePathMappingRepo;
import com.perennialsys.pdfreader.repository.SftpOperationAuditLogRepo;
import com.perennialsys.pdfreader.repository.SubscriptionServiceRequestAuditLogRepo;
import com.perennialsys.pdfreader.repository.TransactionManagerRepo;
import com.perennialsys.pdfreader.util.KeyValue;
import com.perennialsys.pdfreader.vo.DataNormalizerVO;
import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import com.perennialsys.pdfreader.vo.PdfSectionDetailsVO;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeAdditionalDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.SbDetailsDocument;
import org.bson.Document;

import java.util.Date;
import java.util.List;

public interface IService {
    //MySQL related methods
    List<Object[]> executeSql(String queryString, List<KeyValue> conditions, int skip, int limit);

    TransactionManagerRepo getTransactionManagerRepo();

    FileUploadDetailsRepo getFileUploadDetailsRepo();

    ExportReportTemplateMappingRepo getExportReportTemplateMappingRepo();
    SftpDetailsRepo getSftpDetailsRepo();
    SftpOperationAuditLogRepo getSftpOperationAuditLogRepo();
    SftpFilePathMappingRepo getSftpFilePathMappingRepo();
    FieldIdentificationStrategyRepo getFieldIdentificationStrategyRepo();

    PdfReaderExportReportsManagerRepo getReportsManagerRepo();

    PdfReaderPdfFieldStrategyMappingRepo getPdfReaderPdfFieldStrategyMappingRepo();
    SubscriptionServiceRequestAuditLogRepo getSubscriptionServiceRequestAuditLogRepo();
    void updateTransactionManger(TransactionManagerVO transactionManagerVO, String status);

    ExportReportsManagerVO getExportReportManager(String txnId, String pan, String reportType, String status);

    ExportReportsManagerVO saveOrUpdateEximExportReportManager(ExportReportsManagerVO exportReportsManagerVO);

    List<DataNormalizerVO> getDataNormalizerList();

    ExportReportsManagerVO checkAndAddEntryIntoExportReportManager(String pan, String txnId, String reportType, String fileNameWithExtension, String status, Date startPeriod, Date endPeriod, String subTxnId, long filesCount, String description) throws PdfReaderException;

    ExportReportsManagerVO checkAndAddEntryIntoExportReportManager(String pan, String txnId, String reportType,
                                                                   String fileNameWithExtension, String status,
                                                                   String subTxnId,
                                                                   GenerateReportDetailsDto generateReportDtlsDto) throws PdfReaderException;

    List<PdfFieldStrategyMappingVO> getPdfFieldStrategyMapping(String sectionKey, String fileType);

    PdfSectionDetailsVO getPdfSectionDetails(String fileType, String sectionKey);

    FileUploadDetailsVO saveOrUpdateFileUploadDetails(FileUploadDetailsVO fileUploadDetailsVO);

    List<String> getShippingBillSubTxnIds(String pan, Date startDate, Date endDate);

    TransactionManagerVO getFirstByPanAndTypeOrderByUpdatedAtAsc(String pan, String type);

    FileUploadDetailsVO saveFileUploadDetailsVo(FileUploadDetailsVO fileUploadDetailsVO);

    List<TransactionManagerVO> getTransactionsByStatusAndType(String status, List<String> typeList);

    int getTransactionCountByStatus(String status, List<String> typeList);

    //Mongo DB related methods
    MongoDbCursor<BoeDetailsDocument> getBoeDetailsRecords(String pan, List<String> subTxnIdList);
    MongoDbCursor<Document> getBoeDetailsDocuments(String pan, List<String> subTxnIdList);

    List<String> getBoeSubTxnIds(String pan, Date startDate, Date endDate);

    List<SbDetailsDocument> getShippingBillDetailsRecords(String pan, List<String> subTxnIdList);
    long deleteMongoRecordsBasedOnSubTxnId(String collectionName, String subTxnId, String pan);
    List<SbDetailsDocument> getShippingBillDetailsRecords(String pan, Date startDate, Date endDate);
    MongoDbCursor<BoeDetailsDocument> getBoeDetailsRecords(String pan, Date startDate, Date endDate);
    MongoDbCursor<Document> getBoeDetailsDocuments(String pan, Date startDate, Date endDate);
    List<Document> getBoeDateAndNoFromSubTxnId(String pan, String txnId, int pageNo, int limit,
                                                    String searchKey, String searchValue, String sortBy,
                                                    short sortingOrder) throws PdfReaderException;

    long getBoeCountFromTxnId(String pan, String txnId, String searchKey, String searchValue) throws PdfReaderException;
    List<Document> getSbDateAndNoFromSubTxnId(String pan, String txnId, int pageNo, int limit,
                                                    String searchKey, String searchValue, String sortBy,
                                                    short sortingOrder) throws PdfReaderException;

    long getSbCountFromTxnId(String pan, String txnId, String searchKey, String searchValue) throws PdfReaderException;
    List<Document> getBoeDateAndNoFromPeriod(String pan, Date startDate, Date endDate, int pageNo, int limit,
                                             String searchKey, String searchValue,String sortBy,
                                             short sortingOrder) throws PdfReaderException;
    long getBoeCountFromPeriod(String pan, Date startDate, Date endDate,
                               String searchKey, String searchValue) throws PdfReaderException;
    List<Document> getSbDateAndNoFromPeriod(String pan, Date startDate, Date endDate, int pageNo, int limit,
                                            String searchKey, String searchValue,String sortBy,
                                            short sortingOrder) throws PdfReaderException;
    long getSbCountFromPeriod(String pan, Date startDate, Date endDate,
                              String searchKey, String searchValue) throws PdfReaderException;
    MongoDbCursor<BoeAdditionalDetailsDocument> getBoeAdditionalDetailsRecords(String pan, List<String> subTxnIdList);
    List<ExportReportsManagerVO> getExportReportManagerVoList(String pan, List<String> reportTypeList, int skip, int limit, String searchKey,
                                                              String searchValue,String sortBy, short sortingOrder) throws PdfReaderException;
    long getExportReportManagerVoCount(String pan, List<String> reportTypeList, String searchKey, String searchValue) throws PdfReaderException;

    List<Object[]> getFileUploadDeatails(String pan, String fileType, int skip, int limit,
                                         String searchKey, String searchValue, String sortBy,
                                         short sortingOrder) throws PdfReaderException;

    long getFileUploadDeatailsCount(String pan, String fileType, String searchKey, String searchValue) throws PdfReaderException;

    List<FileUploadDetailsVO> getFileUploadDeatails(String pan, String txnId, List<String> statusList, int skip, int limit,
                                                    String searchKey, String searchValue, String sortBy,
                                                    short sortingOrder) throws PdfReaderException;

    long getFileUploadDeatailsCount(String pan, String txnId, List<String> statusList, String searchKey, String searchValue) throws PdfReaderException;

    void addUserDetails(ExportReportsManagerVO exportReportVo) throws PdfReaderException;
    void addUserDetails(TransactionManagerVO txnVo) throws PdfReaderException;

    void addUserDetails(FileUploadDetailsVO fileUploadDetailsVO) throws PdfReaderException;
}
