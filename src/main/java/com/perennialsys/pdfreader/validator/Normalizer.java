package com.perennialsys.pdfreader.validator;

import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.DataNormalizerVO;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public class Normalizer {
    private static final List<String> GSP_GSTN_HSN_TYPE;
    private static final String HSN_GOODS_INITIAL = "G";
    private static final String HSN_SERVICES_INITIAL = "S";
    private static final List<String> GSP_GSTN_YES_LIST;
    private static final List<String> GSP_GSTN_NO_LIST;
    private static final List<String> GSP_GSTN_VALID_NOTE_RESONS;
    public final static List<String> NULL_VAL_ARR = new ArrayList<>(Arrays.asList("", "NA", "NULL", "null"));

    static {

        GSP_GSTN_YES_LIST = new ArrayList<>();
        GSP_GSTN_YES_LIST.add("Y");
        GSP_GSTN_YES_LIST.add("YES");
        GSP_GSTN_YES_LIST.add("TRUE");
        GSP_GSTN_NO_LIST = new ArrayList<>();
        GSP_GSTN_NO_LIST.add("N");
        GSP_GSTN_NO_LIST.add("NO");
        GSP_GSTN_NO_LIST.add("NONE");
        GSP_GSTN_NO_LIST.add("FALSE");

        GSP_GSTN_HSN_TYPE = new ArrayList<>();
        GSP_GSTN_HSN_TYPE.add("G");
        GSP_GSTN_HSN_TYPE.add("GOOD");
        GSP_GSTN_HSN_TYPE.add("GOODS");
        GSP_GSTN_HSN_TYPE.add("S");
        GSP_GSTN_HSN_TYPE.add("SERVICE");
        GSP_GSTN_HSN_TYPE.add("SERVICES");

        GSP_GSTN_VALID_NOTE_RESONS = new ArrayList<String>();
        GSP_GSTN_VALID_NOTE_RESONS.add("01-Sales Return");
        GSP_GSTN_VALID_NOTE_RESONS.add("02-Post Sale Discount");
        GSP_GSTN_VALID_NOTE_RESONS.add("03-Deficiency in services");
        GSP_GSTN_VALID_NOTE_RESONS.add("04-Correction in Invoice");
        GSP_GSTN_VALID_NOTE_RESONS.add("05-Change in POS");
        GSP_GSTN_VALID_NOTE_RESONS.add("06-Finalization of Provisional assessment");
        GSP_GSTN_VALID_NOTE_RESONS.add("07-Others");

    }

    public static boolean isValueExist(Object value) {
        return StringUtils.isNotBlank(String.valueOf(value));
    }

    public static Object normalizeValue(DataNormalizerVO normalizer, String mappingKey, Object value) throws PdfReaderException {

        if (normalizer == null) {
            return value;
        }

        Object normalizedValue = value;
        switch (normalizer.getNormalization()) {
            case "NORM_NULL_VAL": {
                /** normalization */
                normalizedValue = (value == null || NULL_VAL_ARR.contains(value)) ? null
                        : String.valueOf(value).trim();
                break;
            }
            case "NORM_TO_TEXT_VAL": {
                /** normalization */
                normalizedValue = normalizeFromDecimalNumberToString(value);
                break;
            }
            case "NORM_TO_ESCAPE_TEXT_VAL": {
                /** normalization */
                normalizedValue = normalizeFromDecimalNumberToString(value);
                normalizedValue = normalizedValue == null ? null : ((String) value).replaceAll("'", "''");
                break;
            }
            case "NORM_ESCAPE_CHARS": {
                /** normalization */
                if (normalizedValue instanceof String) {
                    normalizedValue = ((String) value).replaceAll("'", "''");
                }
                break;
            }
            case "NORM_DATE_FORMAT": {
                /** normalization */
                normalizedValue = DateFormatUtil.formatToGstnDateString(value);
                break;
            }
            case "NORM_RATE": {
                /** normalization */
                normalizedValue = normalizeRateValue(mappingKey, value);
                break;
            }
            case "NORM_GOODS_OR_SERVICE": {
                /** normalization */
                normalizedValue = normalizeGoodsOrServices(value);
                break;
            }
            case "NORM_YES_NO_VAL": {
                normalizedValue = normalizeToBooleanCharValue(value);
                break;
            }
            case "NORM_TO_LOWERCASE": {
                /** normalization */
                normalizedValue = normalizeToLowercase(value);
                break;
            }
            case "NORM_NOTE_REASON": {
                /** normalization */
                normalizedValue = normalizeNoteReason(value);
                normalizedValue = normalizedValue == null ? null : ((String) value).replaceAll("'", "''");
                break;
            }
            case "NORM_TO_DOUBLE": {
                normalizedValue = normalizeCommaSepStringToDoubleValue(mappingKey, value);
                break;
            }
            case "NORM_TO_INT": {
                normalizedValue = normalizeCommaSepStringToIntValue(mappingKey, value);
                break;
            }
            case "NORM_TO_DATE": {
                normalizedValue = DateFormatUtil.formatToDate(value);
                break;
            }
        }
        return normalizedValue;
    }

    public static String normalizeFromDecimalNumberToString(Object value) {
        if (!isValueExist(value)) {
            return null;
        }
        try {
            //value.toString().startsWith("0") this condition added to handle the issue [GSP-4573]
            if (value.toString().contains("E") || value.toString().contains("e") || value.toString().startsWith("0")) {
                return value.toString();
            } else {
                Double double1 = Double.parseDouble(value.toString());
                return String.format("%.0f", double1).trim();
            }
        } catch (NumberFormatException e) {
            return value.toString();
        }
    }

    public static String normalizeGoodsOrServices(Object value) {
        if (value == null) {
            return null;
        }
        String hsnType = String.valueOf(value).toUpperCase().trim();
        return GSP_GSTN_HSN_TYPE.contains(hsnType)
                ? (hsnType.startsWith(HSN_GOODS_INITIAL) ? HSN_GOODS_INITIAL : HSN_SERVICES_INITIAL)
                : (String) value;
    }

    public static Object normalizeRateValue(String mappingKey, Object cellValue) throws PdfReaderException {
        if (!isValueExist(cellValue)) {
            return null;
        }
        try {
            cellValue = Double.parseDouble(String.valueOf(cellValue).replace(",", ""));
            if (mappingKey.equals("csrt")) {
                DecimalFormat f = new DecimalFormat("##.00");
                cellValue = f.format(cellValue);
            }
        } catch (Exception e) {
            throw new PdfReaderException("Error while parsing double value", e);
        }
        return cellValue;
    }

    public static Object normalizeCommaSepStringToDoubleValue(String mappingKey, Object cellValue) throws PdfReaderException {
        if (!isValueExist(cellValue)) {
            return null;
        }
        try {
            cellValue = Double.parseDouble(cellValue.toString().replaceAll(",", ""));
            if (mappingKey.equals("csrt")) {
                DecimalFormat f = new DecimalFormat("##.00");
                cellValue = f.format(cellValue);
            }
        } catch (Exception e) {
            throw new PdfReaderException("Error while parsing double value", e);
        }
        return cellValue;
    }

    public static Object normalizeCommaSepStringToIntValue(String mappingKey, Object cellValue) throws PdfReaderException {
        if (!isValueExist(cellValue)) {
            return null;
        }
        try {
            cellValue = Integer.parseInt(cellValue.toString().replaceAll(",", ""));
        } catch (Exception e) {
            throw new PdfReaderException("Error while parsing double value", e);
        }
        return cellValue;
    }

    public static Object normalizeToBooleanCharValue(Object cellValue) {
        if (!isValueExist(cellValue)) {
            return "N";
        }
        cellValue = String.valueOf(cellValue).toUpperCase();
        return GSP_GSTN_YES_LIST.contains(cellValue) ? "Y"
                : (GSP_GSTN_NO_LIST.contains(cellValue) ? "N" : (String) cellValue);
    }

    public static Object normalizeToLowercase(Object cellValue) {
        if (!isValueExist(cellValue)) {
            return null;
        }
        return String.valueOf(cellValue).toLowerCase();
    }

    public static String normalizeNoteReason(Object cellValue) {
        if (!isValueExist(cellValue)) {
            return null;
        }
        String reason = String.valueOf(cellValue).trim();
        for (String noteReason : GSP_GSTN_VALID_NOTE_RESONS) {
            if (reason.equalsIgnoreCase(noteReason.substring(3))) {
                reason = noteReason;
            }
        }
        return reason;
    }

    public static Object normalizeAndGetValue(String mappingKey, Object value, List<DataNormalizerVO> dataNormalizarVoList) throws PdfReaderException {
        Optional<DataNormalizerVO> normalizerOptional = dataNormalizarVoList.stream()
                .filter(dataNormalizerVO -> dataNormalizerVO.getMappingKey().equals(mappingKey))
                .findFirst();

        if (normalizerOptional.isPresent()) {
            value = Normalizer.normalizeValue(normalizerOptional.get(), mappingKey, value);
        }

        return value;
    }
}
