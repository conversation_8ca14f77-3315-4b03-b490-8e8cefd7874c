/**
 * <AUTHOR>
 */
package com.perennialsys.pdfreader.validator;


import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Projections;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.FileExtension;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.GeneralUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.SbDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.SbDetailsProduct;
import lombok.extern.slf4j.Slf4j;
import org.bson.conversions.Bson;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Filter;

/**
 * <AUTHOR> Salvi
 */
@Slf4j
public class ValidationProcessor {

    public static boolean isValueExist(Object value) {
        return StringUtils.isNotBlank(String.valueOf(value));
    }

    /**
     * @param value               - cell value.
     * @param boeDetailsCollection - import statement mongo collection
     * @return - returns if the BOE no is unique or not.
     * <AUTHOR> Nagare
     * @since 15/11/2022
     */
    public static boolean checkUniqueBoeNo(Object value, String txnId, String pan, MongoCollection<BoeDetailsDocument> boeDetailsCollection) {
        boolean isValid = false;

        if (isValueExist(value)) {
            Bson filter = Filters.and(Filters.eq(BoeDetailsFields.BOE_NO.getValue(), value),
                    Filters.eq(BoeDetailsFields.PAN.getValue(), pan), Filters.ne(BoeDetailsFields.TXN_ID.getValue(), txnId)
            );
            isValid = boeDetailsCollection.countDocuments(filter) <= 0;
        }

        return isValid;
    }

    /**
     * @param sbNo               - shipping bill no
     * @param sbDate shipping bill date
     * @param  pan PAN
     * @param sbDetailsCollection - Shipping bill statement mongo collection
     * @return - returns if the SB no is unique or not.
     * <AUTHOR> Nagare
     * @since 15/11/2022
     */
    public static boolean checkUniqueSbNo(Object sbNo, Object sbDate, String pan, MongoCollection<SbDetailsDocument> sbDetailsCollection) {
        boolean isValid = true;

        Bson filter = Filters.and(Filters.eq(SBInvoiceDetailsFields.PAN.getValue(), pan),
                Filters.eq(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue(), sbNo));

        Bson projection = Projections.include(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue(),
                SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue());

        List<SbDetailsDocument> sbDetailsDocuments = sbDetailsCollection.find(filter).projection(projection).into(new ArrayList<>());

        if (!sbDetailsDocuments.isEmpty()) {
            //If for any of the shipping bill entry if we are getting the duplicate
            // shipping bill no we will mark the row as invalid.

            String sdFy = DateFormatUtil.getFinancialYearFromDate(DateFormatUtil.formatToDate(sbDate));

            if (sbDetailsDocuments.stream()
                    .filter(sbDoc -> Objects.nonNull(sbDoc.getShippingBillNo()))
                    .anyMatch(sbDoc -> !validateDuplicateExportStatementExporter(sbDoc.getShippingBillDate(), sdFy))) {
                isValid = false;
            }
        }

        return isValid;
    }

    private static boolean validateDuplicateExportStatementExporter(Date sbDbDate, String fy){

        boolean isValid = true;

        String sbDbFy = DateFormatUtil.getFinancialYearFromDate(sbDbDate);

        if(sbDbFy.equals(fy)){
            isValid = false;
        }

        return isValid;
    }

    public static boolean validateValidPdfFiles(List<MultipartFile> upoadedFileList) throws PdfReaderException {
        log.info("START >> ValidationProcessor >> METHOD >> validateValidPdfFiles");
        boolean result = false;
        if(null != upoadedFileList && !upoadedFileList.isEmpty()){
            for (MultipartFile file : upoadedFileList) {
                if(null != file){
                    result = validateFilePdfExtension(file.getOriginalFilename(), FileExtension.getValidPdfExtensions());
                }else {
                    result = false;
                    break;
                }
            }
        }

        if(!result){
            log.error("ERROR >> ValidationProcessor >> METHOD >> validateValidPdfFiles >> Invalid PDF files are present");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_PDF_FILES);
        }

        log.info("END >> ValidationProcessor >> METHOD >> validateValidPdfFiles");
        return result;
    }

    private static boolean validateFilePdfExtension(String fileNameWithExtension, List<String> expectedExtensionList){
        log.info("START >> ValidationProcessor >> METHOD >> validateFilePdfExtension");

        String subStringAfterDot = fileNameWithExtension;

        expectedExtensionList = null == expectedExtensionList || expectedExtensionList.isEmpty()
                ? FileExtension.getValidPdfExtensions() : expectedExtensionList;

        int dotIndex;
        do{
            dotIndex = subStringAfterDot.indexOf(".");
            subStringAfterDot = subStringAfterDot.substring(dotIndex);
            //Check if the there is number just after dot
            String firstCharAfterDot = subStringAfterDot.substring(1, 2);
            boolean isNumberAfterDot = true;
            try{
                Integer.valueOf(firstCharAfterDot);
            } catch (NumberFormatException e){
                //There is no number just after the dot
                isNumberAfterDot = false;
            }

            //If there is no number just after the dot then check if it is valid extension or not.
            if(!isNumberAfterDot){
                log.info("START >> ValidationProcessor >> METHOD >> validateFilePdfExtension >> File extension is valid.");
                return dotIndex > 0 && expectedExtensionList.contains(subStringAfterDot.substring(1));
            }
            subStringAfterDot = subStringAfterDot.substring(1);
        }while (StringUtils.isNotBlank(subStringAfterDot) || dotIndex != subStringAfterDot.lastIndexOf("."));

        log.info("END >> ValidationProcessor >> METHOD >> validateFilePdfExtension >> File Extension is invalid.");
        return false;
    }
}
