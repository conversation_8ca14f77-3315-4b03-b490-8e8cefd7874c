package com.perennialsys.pdfreader.listeners;

import com.mongodb.client.model.Filters;
import com.perennialsys.pdfreader.db.mongo.MongoDao;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 04-29-2024
 * @Description This class will be used to run code just after the application is started and all the beans are initialized.
 */
@Component
@Slf4j
public class StartupApplicationListener implements ApplicationListener<ContextRefreshedEvent> {

    private final IService service;
    private final MongoDao mongoDao;

    public StartupApplicationListener(IService service, MongoDao mongoDao){
        this.service = service;
        this.mongoDao = mongoDao;
    }

    @Override
    public void onApplicationEvent(@NonNull ContextRefreshedEvent contextRefreshedEvent) {
        log.info("START >> StartupApplicationListener >> METHOD >> onApplicationEvent");

        resetInProgressFiles();

        log.info("END >> StartupApplicationListener >> METHOD >> onApplicationEvent");
    }

    private void resetInProgressFiles(){
        log.info("START >> StartupApplicationListener >> METHOD >> resetInProgressFiles");
        int inProgressEntryCount = 0;
        List<FileUploadDetailsVO> ipFileUpldDtlsList = service.getFileUploadDetailsRepo()
                .findAllByStatusInOrderByIdAsc(Collections.singletonList(TransactionStatus.PDF_PARSING_IN_PROGRESS.name()));

        if(null != ipFileUpldDtlsList && !ipFileUpldDtlsList.isEmpty()) {
            //Remove the data from mongoDb based on the file type
            log.info("INTERMEDIATE >> StartupApplicationListener >> METHOD >> resetInProgressFiles >> Removing MongoDb data for BOE");
            removeMondoDbData(PdfFileType.BILL_OF_ENTRY.getMongoCollection(),
                    ipFileUpldDtlsList.stream()
                        .filter(fileUpldVo -> fileUpldVo.getFileType().equals(PdfFileType.BILL_OF_ENTRY.name()))
                        .map(FileUploadDetailsVO::getSubTxnId)
                        .distinct()
                        .collect(Collectors.toList()));

            log.info("INTERMEDIATE >> StartupApplicationListener >> METHOD >> resetInProgressFiles >> Removing MongoDb data for SB");
            removeMondoDbData(PdfFileType.SHIPPING_BILL.getMongoCollection(),
                    ipFileUpldDtlsList.stream()
                        .filter(fileUpldVo -> fileUpldVo.getFileType().equals(PdfFileType.SHIPPING_BILL.name()))
                        .map(FileUploadDetailsVO::getSubTxnId)
                        .distinct()
                        .collect(Collectors.toList()));

            log.info("INTERMEDIATE >> StartupApplicationListener >> METHOD >> resetInProgressFiles >> Updating file upload details status to uploaded");
            //Update the status as uploaded for all the files
            service.getFileUploadDetailsRepo().updateStatusFromSubTxnId(TransactionStatus.UPLOADED.name(),
                    ipFileUpldDtlsList.stream()
                        .map(FileUploadDetailsVO::getSubTxnId)
                        .distinct()
                        .collect(Collectors.toList()));

            inProgressEntryCount = ipFileUpldDtlsList.size();
        }

        log.info("END >> StartupApplicationListener >> METHOD >> resetInProgressFiles >> UPDATED_ENTRIES_COUNT >> {}", inProgressEntryCount);
    }

    private void removeMondoDbData(String collectionName, List<String> subTxnIdList){
        log.info("START >> StartupApplicationListener >> METHOD >> removeMondoDbData >> COLLECTION_NAME >> {}", collectionName);

        if(null != subTxnIdList && !subTxnIdList.isEmpty()) {
            mongoDao.getMongoCollection(collectionName).deleteMany(Filters.in(BoeDetailsFields.SUB_TXN_ID.getValue(), subTxnIdList));
        }

        log.info("END >> StartupApplicationListener >> METHOD >> removeMondoDbData >> COLLECTION_NAME >> {}", collectionName);
    }
}
