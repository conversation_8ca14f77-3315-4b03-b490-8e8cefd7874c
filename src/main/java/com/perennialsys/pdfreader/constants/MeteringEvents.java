package com.perennialsys.pdfreader.constants;

import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @since 01-09-2024
 */
@AllArgsConstructor
@Getter
public enum MeteringEvents {

    SB_FILE_UPLD("File Upload through API/portal Event for Shipping Bill file",
            Arrays.asList(MeteringEntities.FILE_COUNT, MeteringEntities.PAGE_COUNT)),
    SB_FILE_DLT("File delete Event for Shipping Bill file uploaded through API/portal",
            Collections.singletonList(MeteringEntities.FILE_COUNT)),

    SB_SFTP_FILE_UPLD("File Upload through SFTP Event for Shipping Bill file",
            Arrays.asList(MeteringEntities.FILE_COUNT, MeteringEntities.PAGE_COUNT)),
    SB_SFTP_FILE_DLT("File delete Event for Shipping Bill file uploaded through SFTP",
            Collections.singletonList(MeteringEntities.FILE_COUNT)),

    BOE_FILE_UPLD("File Upload through API/portal Event for Bill Of Entry file",
            Arrays.asList(MeteringEntities.FILE_COUNT, MeteringEntities.PAGE_COUNT)),
    BOE_FILE_DLT("File delete Event for Bill Of Entry file uploaded through API/portal",
            Collections.singletonList(MeteringEntities.FILE_COUNT)),

    BOE_SFTP_FILE_UPLD("File Upload through SFTP Event for Bill Of Entry file",
            Arrays.asList(MeteringEntities.FILE_COUNT, MeteringEntities.PAGE_COUNT)),
    BOE_SFTP_FILE_DLT("File delete Event for Bill Of Entry file uploaded through SFTP",
            Collections.singletonList(MeteringEntities.FILE_COUNT));

    private final String description;
    private final List<MeteringEntities> allowedMeteringEntityList;

    /**
     * <AUTHOR> Nagare
     * @since 01-08-2024
     * @Description This method validated if the metering entity key is allowed for the metering event
     * @param meteringEventString Metering event Name
     * @param meteringEntityString Metering entity key
     * @return Returns if the metering entity key is allowed for the metering event or not.
     * @throws PdfReaderException throws PdfReaderException
     */
    public static boolean validateEventEntityMapping(String meteringEventString, String meteringEntityString
    ) throws PdfReaderException {
        try{
            MeteringEvents meteringEvent = valueOf(meteringEventString);

            return meteringEvent.getAllowedMeteringEntityList().stream()
                    .map(MeteringEntities::getMeteringEntityKey)
                    .anyMatch(meteringEntityKey -> meteringEntityKey.equals(meteringEntityString));

        }catch (IllegalArgumentException e){
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_METERING_EVENT);
        }
    }
}
