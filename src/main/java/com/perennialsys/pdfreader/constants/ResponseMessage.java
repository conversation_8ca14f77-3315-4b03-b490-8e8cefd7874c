package com.perennialsys.pdfreader.constants;

@SuppressWarnings({"squid:S1214"})
public interface ResponseMessage {
    String INVALID_DATA = "INVALID DATA";
    String INVALID_FILE_TYPE = "Invalid File type.";
    String FILE_UPLOADED_SUCCESS = "File Uploaded Successfully";
    String INVALID_DATA_MISSING_FILE = "File Does not exist.";
    String INVALID_FILE_FORMAT_ENCRYPTED_PDF = "Encrypted PDF cannot be processed.";
    String INVALID_DATE_FORMAT = "Invalid date format";
    String INVALID_REPORT_TYPE = "Invalid Report type. Please pass the valid report type value.";
    String INVALID_EXPORT_FORMAT = "Invalid Export format.";
    String INVALID_EXPORT_FORMAT_FOR_REPORT = "Export format is not available for the provided report type.";
    String MISSING_EXPORT__FORMAT_OR_REPORT_TYPE = "Export format or Report type cannot be blank.";
    String INVALID_ACTION = "Invalid or missing Action, Please pass the valid action.";
    String UNABLE_TO_UPLOAD_FILE = "Unable to upload, please contact to support team.";
    String UNABLE_TO_DOWNLOAD_FILE = "Unable to download, please contact to support team.";
    String UNABLE_TO_DELETE_FILE = "Unable to delete, please contact to support team.";
    String UNABLE_TO_READ_FILE_DATA = "Can not read file data.";
    String UNABLE_TO_PROCESS_REQUEST = "Unable to process request. Please contact support for quick resolution.";
    String ERROR_WHILE_PARSING_PDF_FILE = "Error while parsing the PDF file.";
    String INVALID_PDF_FILE = "Invalid PDF File.";
    String REACHED_END_OF_PDF_FILE = "We have reached end of PDF file.";
    String FILE_NOT_FOUND = "File Not Found, Please contact support for quick resolution.";
    String ERROR_WHILE_EXPORTING_REPORT = "Error While Exporting Report.";
    String NO_ENUM_FOUND = "No Enum found for key >> ";
    String BOE_NO_MUST_BE_UNIQUE = "Bill of Entry No. Must be unique.";
    String BLANK_PAN = "PAN value is blank. Please pass the valid PAN value";
    String INVALID_PAN = "Blank or invalid PAN. Please provide valid PAN";
    String MISSING_MANDATORY_HEADERS = "Missing Mandatory headers";
    String MISSING_SESSION_TOKEN = "Missing Session Token";
    String DATA_NOT_FOND = "Data not found";
    String SFTP_CONNECTION_FAILED = "Error while connecting to SFTP";
    String SFTP_DETAILS_NOT_FOUND = "SFTP details not found";
    String SFTP_PATH_DETAILS_NOT_FOUNE = "SFTP input/output file path not found";
    String SFTP_ERROR_WHILE_READING_DIRECTORY = "Error while SFTP directory";
    String SFTP_ERROR_WHILE_DOWNLOADING_FILE = "Error while downloading file from SFTP";
    String SFTP_ERROR_WHILE_MOVING_FILE = "Error while moving file in SFTP";
    String SFTP_ERROR_WHILE_UPLOADING_FILE = "Error while uploading file in SFTP";
    String ERROR_WHILE_UPLOADING_FILE = "Error while uploading file";
    String INVALID_FIELD_IDENTIFICATION_STRATEG = "Invalid Field Identification Strategy";
    String ERROR_WHILE_IDENTIFYING_FIELD_VALUE = "Error while identifying the field value.";
    String INVALID_TERMINATION_LENGTH = "Value termination length not found in the field identification strategy.";
    String INVALID_TERMINATION_SPECIAL_CHAR = "Special character value not found in the field identification strategy.";

    String MISSING_SOURCE_FIELD = "Missing Source Field";
    String INVALID_SOURCE_FIELD = "Invalid Source Field";
    String MISSING_TARGET_FIELD = "Missing Target Field";
    String INVALID_TARGET_FIELD = "Invalid Target Field";
    String MISSING_STRATEGY = "Missing Strategy";
    String INVALID_STRATEGY = "Invalid Strategy";
    String MISSING_TERMINATION_POLICY = "Missing Termination Policy";
    String INVALID_TERMINATION_POLICY = "Invalid Termination Policy";
    String FIELD_IDENTIFICATION_STRATEGY_ADDED_SUCCESSFULLY = "Field Identification Strategy Set Successfully.";
    String FIELD_IDENTIFICATION_STRATEGY_DELETED_SUCCESSFULLY = "Field Identification Strategy Deleted Successfully.";
    String INVALID_TERMINATION_POLICY_VALUE = "Invalid Termination Policy value";

    String SUBSCRIPTION_DTLS_NOT_FOUND = "Subscription Details not found.";
    String SUBSCRIPTION_NOT_ACTIVE = "No Active Subscription found. Please contact support for quick resolution.";
    String MISSING_EMAIL = "Please pass valid email.";
    String TXN_NOT_FOUND = "Transaction Details not found, Please provide correct transaction ID.";
    String INVALID_TXN_TYPE = "Invalid transaction found, please provide correct transaction ID.";
    String FILE_UPLD_DTLS_NOT_FOUND = "File upload details not found, please contact support for quick resolution.";
    String ERROR_WHILE_CREATING_ZIP_FILE = "Error while creating zip file, please contact support for quick resolution.";
    String FILE_DELETE_SUCCESS = "File Deleted successfully.";
    String INVALID_FILE = "Invalid file found, please provide correct file ID.";
    String INVALID_PDF_FILES = "One or more files are not valid PDF files. Kindly upload valid PDF files.";
    String REPORT_NOT_GENERATED = "Report not generated, please try after some time or generate new report.";
    String REPORT_GENERATION_STARTED_SUCCESSFULLY = "Report generation started successfully, please check in some time to download the report.";
    String NO_FILES_FOUND_FOR_TXN = "No files found for the provided transaction, please provide valid transaction.";

    String MISSING_REMARK = "Missing remark, please pass valid remark.";
    String FILE_EXPORT_DETAILS_NOT_FOUND = "File Export details not found.";
    String UNABLE_TO_GET_OTHER_DETAILS = "Error while getting other details from export report.";
    String REMARK_UPDATED_SUCCESSFULLY = "Remark updated successfully.";
    String INVALID_SEARCH_KEY = "Invalid search key.";
    String INVALID_SORTING_ORDER = "Invalid searching order.";
    String USER_DETAILS_NOT_FOUND = "User details not found.";
    String ERROR_WHILE_GETTING_USER_DETAILS = "Error while getting user details.";
    String INVALID_METERING_EVENT = "Invalid metering event.";
}
