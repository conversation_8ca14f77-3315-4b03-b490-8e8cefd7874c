package com.perennialsys.pdfreader.constants;

public interface QueryOperators {
    String $and = "$and";
    String $or = "$or";

    String $size = "$size";
    String $gt = "$gt";
    String $gte = "$gte";
    String $lt = "$lt";
    String $lte = "$lte";

    String $set = "$set";
    String $unset = "$unset";
    String $setOnInsert = "$setOnInsert";

    String $pull = "$pull";
    String $pullAll = "$pullAll";

    String $pop = "$pop";

    String $push = "$push";
    String $pushAll = "$pushAll";
    String $each = "$each";
    String $addToSet = "$addToSet";

    String $mul = "$mul";
    String $inc = "$inc";

    String $rename = "$rename";

    String $exists = "$exists";

    String $currentDate = "$currentDate";
    String $type = "$type";

    String $expr = "$expr";
    String TYPE_timestamp = "timestamp";
    String $project = "$project";
    String $match = "$match";
    String $count = "$count";
    String $regex = "$regex";
    String $ne = "$ne";
    String $elemMatch = "$elemMatch";
    String $nin = "$nin";
    String $in = "$in";

    String $group = "$group";
    String $sum = "$sum";
}
