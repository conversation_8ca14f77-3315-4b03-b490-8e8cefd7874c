package com.perennialsys.pdfreader.constants;

import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;

public interface DbQueries {

    String GET_FILE_UPLOAD_DETAILS_SQL = "select distinct (tm.TXN_ID), fud.FILE_TYPE, tm.STATUS, tm.UPDATED_AT, " +
            "tm.CREATED_AT, tm.OTHER_DETAILS, tm.ID , tm.USER_NAME, tm.USER_ID from "+DBTables.TRANSACTION_MANAGER_TBL+" tm right join "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" fud " +
            "on fud.TXN_ID = tm.TXN_ID where fud.FILE_TYPE = :fileType and tm.PAN = :pan";

    String GET_FILE_UPLOAD_DETAILS_FROM_STATUS_SQL = "select tm.TXN_ID, tm.FILE_TYPE, tm.STATUS," +
            " tm.UPDATED_AT, tm.CREATED_AT, tm.OTHER_DETAILS, tm.ID , tm.USER_NAME, tm.USER_ID from " +
            "(select tm.TXN_ID, count(fud.ID) - sum(if(fud.STATUS in (:statusList), 1, 0)) " +
            "as DIFFERENCE, tm.STATUS, tm.UPDATED_AT, tm.CREATED_AT, tm.OTHER_DETAILS, tm.ID, tm.USER_NAME, tm.USER_ID, " +
            " fud.FILE_TYPE from "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" fud join "+DBTables.TRANSACTION_MANAGER_TBL+" tm on fud.TXN_ID = tm.TXN_ID where tm.PAN = " +
            ":pan and fud.FILE_TYPE = :fileType and fud.IS_DELETED = :isDeleted group by tm.TXN_ID) as tm where tm.DIFFERENCE = 0";

    String GET_FILE_UPLOAD_DETAILS_FOR_DELETED_STATUS_SQL = "select tm.TXN_ID, tm.FILE_TYPE, tm.STATUS," +
            " tm.UPDATED_AT, tm.CREATED_AT, tm.OTHER_DETAILS, tm.ID , tm.USER_NAME, tm.USER_ID from " +
            "(select tm.TXN_ID, count(fud.ID) - sum(if(fud.IS_DELETED = true, 1, 0)) " +
            "as DIFFERENCE, tm.STATUS, tm.UPDATED_AT, tm.CREATED_AT, tm.OTHER_DETAILS, tm.ID, tm.USER_NAME, tm.USER_ID, " +
            " fud.FILE_TYPE from "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" fud join "+DBTables.TRANSACTION_MANAGER_TBL+" tm on fud.TXN_ID = tm.TXN_ID where tm.PAN = " +
            ":pan and fud.FILE_TYPE = :fileType group by tm.TXN_ID) as tm where tm.DIFFERENCE = 0";

    String GET_FILE_UPLOAD_DETAILS_FROM_UPDATED_AT_SQL = "select distinct (tm.TXN_ID), fud.FILE_TYPE, tm.STATUS, tm.UPDATED_AT, " +
            "tm.CREATED_AT, tm.OTHER_DETAILS, tm.ID , tm.USER_NAME, tm.USER_ID from "+DBTables.TRANSACTION_MANAGER_TBL+" tm right join " +
            DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" fud on fud.TXN_ID = tm.TXN_ID where fud.FILE_TYPE = :fileType and tm.PAN = :pan" +
            " and date(tm.UPDATED_AT) >= :startDate and date(tm.UPDATED_AT) <= :endDate";

    String GET_FILE_UPLOAD_DETAILS_FROM_UPDATED_BY_SQL = "select distinct (tm.TXN_ID), fud.FILE_TYPE, tm.STATUS, tm.UPDATED_AT, " +
            "tm.CREATED_AT, tm.OTHER_DETAILS, tm.ID , tm.USER_NAME, tm.USER_ID from "+DBTables.TRANSACTION_MANAGER_TBL+" tm right join "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" fud on " +
            "fud.TXN_ID = tm.TXN_ID where fud.FILE_TYPE = :fileType and tm.PAN = :pan and tm.USER_NAME like :updatedByUserName";
    String GET_FILE_UPLOAD_DETAILS_COUNT_SQL ="select count(distinct (TXN_ID)) from file_upload_details" +
            " where PAN = :pan and FILE_TYPE = :fileType";
    String GET_FILE_UPLOAD_DETAILS_COUNT_FROM_STATUS_SQL = "select count(filtered_fileDtls.TXN_ID) from " +
            "(select TXN_ID, count(ID) - sum(if(STATUS in (:statusList) , 1, 0)) as DIFFERENCE from file_upload_details where " +
            "PAN = :pan and FILE_TYPE = :fileType and IS_DELETED = :isDeleted group by TXN_ID) as filtered_fileDtls where filtered_fileDtls.DIFFERENCE = 0";
    String GET_FILE_UPLOAD_DETAILS_COUNT_FOR_DELETED_STATUS_SQL = "select count(deleted_fileDtls.TXN_ID) from " +
            "(select TXN_ID, count(ID) - sum(if(IS_DELETED = true, 1, 0)) as DIFFERENCE from file_upload_details where " +
            "PAN = :pan and FILE_TYPE = :fileType group by TXN_ID) as deleted_fileDtls where deleted_fileDtls.DIFFERENCE = 0";
    String GET_FILE_UPLOAD_DETAILS_COUNT_FROM_UPDATED_AT_SQL = "select count(distinct (TXN_ID)) from "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+
            " where PAN = :pan and FILE_TYPE = :fileType  and date(UPDATED_AT) >= :startDate and date(UPDATED_AT) <= :endDate";
    String GET_FILE_UPLOAD_DETAILS_COUNT_FROM_UPDATED_BY_SQL = "select count(distinct (TXN_ID)) from "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+
            " where PAN = :pan and FILE_TYPE = :fileType  and USER_NAME like :updatedByUserName";

    String GET_FILE_UPLOAD_DTLS_FROM_TXN_PAN_STATUS_HQL = "from "+FileUploadDetailsVO.class.getName()+" where pan = :pan and" +
            " txnId = :txnId and status in (:statusList) and isDeleted = :isDeleted";

    String GET_FILE_UPLOAD_DTLS_FROM_TXN_PAN_STATUS_FILE_NAME_HQL = "from "+FileUploadDetailsVO.class.getName()+" where pan = :pan and" +
            " txnId = :txnId and status in (:statusList) and isDeleted = :isDeleted and fileDisplayName like :fileDisplayName";

    String GET_FILE_UPLOAD_DTLS_COUNT_FROM_TXN_PAN_STATUS_HQL = "select count(id) from "+FileUploadDetailsVO.class.getName()+" where pan = :pan and" +
            " txnId = :txnId and status in (:statusList) and isDeleted = :isDeleted";

    String GET_FILE_UPLOAD_DTLS_COUNT_FROM_TXN_PAN_STATUS_FILE_NAME_HQL = "select count(id) from "+FileUploadDetailsVO.class.getName()+" where pan = :pan and" +
            " txnId = :txnId and status in (:statusList) and isDeleted = :isDeleted and fileDisplayName like :fileDisplayName";
    String GET_EXPORT_REPORT_HQL = "from "+ ExportReportsManagerVO.class.getName()+ " where pan =:pan and reportType in (:reportType)";
    String GET_EXPORT_REPORT_COUNT_HQL = "select count(id) from "+ ExportReportsManagerVO.class.getName()+ " where pan =:pan and reportType in (:reportType)";
    String GET_EXPORT_REPORT_FROM_CREATED_AT_BETWEEN_HQL = "from "+ ExportReportsManagerVO.class.getName()+
            " where pan =:pan and reportType in :reportType and date(createdAt) >= :startDate and date(createdAt) <= :endDate";
    String GET_EXPORT_REPORT_COUNT_FROM_CREATED_AT_BETWEEN_HQL = "select count(id) from "+ ExportReportsManagerVO.class.getName()+
            " where pan =:pan and reportType in :reportType and date(createdAt) >= :startDate and date(createdAt) <= :endDate";
    String GET_EXPORT_REPORT_FROM_EXPORT_USER_NAME_LIKE_HQL = "from "+ ExportReportsManagerVO.class.getName()+
            " where pan =:pan and reportType in :reportType and userName like :exportedByUserName";
    String GET_EXPORT_REPORT_COUNT_FROM_EXPORT_USER_NAME_LIKE_HQL = "select count(id) from "+ ExportReportsManagerVO.class.getName()+
            " where pan =:pan and reportType in :reportType and userName like :exportedByUserName";
    String GET_EXPORT_REPORT_FROM_REMARK_LIKE_HQL = "from "+ ExportReportsManagerVO.class.getName()+
            " where pan =:pan and reportType in :reportType and remark like :remark";
    String GET_EXPORT_REPORT_COUNT_FROM_REMARK_LIKE_HQL = "select count(id) from "+ ExportReportsManagerVO.class.getName()+
            " where pan =:pan and reportType in :reportType and remark like :remark";

    String GET_FILE_PARSING_STATUS_DIFF_FROM_STATUS_SQL = "select " +
            "count(ID) - sum(if(STATUS in ('PDF_PARSING_COMPLETE'), 1, 0)) as DIFFERENCE, ID from "
            +DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" where PAN = :pan and FILE_TYPE = :fileType and TXN_ID = :txnId " +
            "and IS_DELETED = :isDeleted";

    String GET_FILE_PARSING_DELETED_DIFF_FROM_STATUS_SQL = "select " +
            "count(ID) - sum(if(IS_DELETED, 1, 0)) as DIFFERENCE, ID from "
            +DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" where PAN = :pan and FILE_TYPE = :fileType and TXN_ID = :txnId";
}
