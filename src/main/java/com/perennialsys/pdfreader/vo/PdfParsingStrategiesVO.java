package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = DBTables.PDF_PARSING_STRATEGIES_TBL)
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PdfParsingStrategiesVO {

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    @Column(name = "STRATEGY")
    private String strategy;
    @Column(name = "IS_FIELD_SPECIFIC")
    private boolean isFieldSpecific;
}
