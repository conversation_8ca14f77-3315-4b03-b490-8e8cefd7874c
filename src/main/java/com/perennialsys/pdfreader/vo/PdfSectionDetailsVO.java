package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = DBTables.PDF_SECTION_DETAILS_TBL)
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PdfSectionDetailsVO implements Serializable {
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    @Column(name = "FILE_TYPE")
    private String fileType;
    @Column(name = "SECTION_KEY")
    private String sectionKey;
    @Column(name = "SECTION_DISPLAY_NAME")
    private String sectionDisplayName;
    @Column(name = "BUFFER_VALUE")
    private double bufferValue;
    @Column(name = "CREATED_AT")
    private Date createdAt;
    @Column(name = "UPDATED_AT")
    private Date updatedAt;
}
