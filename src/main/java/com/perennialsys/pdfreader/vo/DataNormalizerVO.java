package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;


@Entity
@Table(name = DBTables.DATA_NORMALIZER_TBL)
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class DataNormalizerVO implements IGenericVO, Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "MAPPING_KEY", unique = true)
    private String mappingKey;

    @Column(name = "NORMALIZATION")
    private String normalization;

    @Column(name = "RULE_TYPE")
    private String ruleType;

    @Column(name = "RULE", columnDefinition = "TEXT")
    private String rule;

    public DataNormalizerVO() {
        super();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMappingKey() {
        return mappingKey;
    }

    public void setMappingKey(String mappingKey) {
        this.mappingKey = mappingKey;
    }

    public String getNormalization() {
        return normalization;
    }

    public void setNormalization(String normalization) {
        this.normalization = normalization;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

}
