package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = DBTables.SFTP_OPERATION_AUDIT_LOG)
@Getter
@Setter
@ToString
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class SftpOperationAuditLogVO implements IGenericVO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private long id;

    @Column(name = "PAN")
    private String pan;

    @Column(name = "TXN_ID")
    private String txnId;

    @Column(name = "HOST_IP")
    private String hostIp;

    @Column(name = "OPERATION")
    private String operation;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "OTHER_DETAILS")
    private String otherDetails;

    @Column(name = "RETRY_COUNT")
    private int retryCount;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATED_AT")
    private Date updatedAt;

    @Override
    public boolean equals(Object o) {
        boolean result = false;
        if (this == o){
            result = true;
        }else if (o != null && Hibernate.getClass(this) == Hibernate.getClass(o)){
            SftpOperationAuditLogVO that = (SftpOperationAuditLogVO) o;
            result = getId() != 0 && Objects.equals(getId(), that.getId());
        }

        return result;
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
