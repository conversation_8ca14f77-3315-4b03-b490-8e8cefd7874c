package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@Table(name = DBTables.SUBSCRIPTION_SERVICE_REQUEST_AUDIT_LOG)
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@AllArgsConstructor
@Builder
public class SubscriptionServiceRequestAuditLogVo implements IGenericVO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "PAN")
    private String pan;

    @Column(name = "ACTION")
    private String action;

    @Column(name = "TXN_ID")
    private String txnId;

    @Column(name = "API_KEY")
    private String apiKey;

    @Column(name = "RESOURCE_PATH")
    private String resourcePath;

    @Column(name = "REQUEST_HEADERS")
    private String requestHeaders;

    @Column(name = "REQUEST_PARAMS")
    private String requestParams;

    @Column(name = "REQUEST_PAYLOAD")
    private String requestPayload;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "RESPONSE")
    private String response;

    @Column(name = "CREATED_AT")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "UPDATED_AT")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;
}
