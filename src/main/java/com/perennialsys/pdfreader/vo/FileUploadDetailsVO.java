package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = DBTables.TBL_FILE_UPLOAD_DETAILS_TBL)
@RequiredArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class FileUploadDetailsVO implements Serializable, Cloneable, IGenericVO {

    /**
     *
     */
    private static final long serialVersionUID = 6989579080988720202L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "PAN")
    private String pan;

    @Column(name = "TXN_ID")
    private String txnId;

    @Column(name = "USER_ID")
    private long userId;

    @Column(name = "USER_NAME")
    private String userName;

    @Column(name = "SOURCE")
    private String source;

    @Column(name = "FILE_TYPE")
    private String fileType;

    @Column(name = "FILE_NAME")
    private String fileName;

    @Column(name = "FILE_DISPLAY_NAME")
    private String fileDisplayName;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "FILE_LOC")
    private String fileLoc;

    @Column(name = "RETRY_COUNT")
    private int retryCount;

    @Column(name = "SUB_TXN_ID")
    private String subTxnId;

    @Column(name = "IS_DELETED")
    private boolean isDeleted;

    @Column(name = "CREATED_AT")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "UPDATED_AT")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    public FileUploadDetailsVO(String pan, String fileName, String fileDisplayName, String txnId, String fileType,
                               String status, String fileLoc, String source) {
        super();
        this.pan = pan;
        this.fileName = fileName;
        this.fileDisplayName = fileDisplayName;
        this.txnId = txnId;
        this.fileType = fileType;
        this.status = status;
        this.fileLoc = fileLoc;
        this.source = source;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
