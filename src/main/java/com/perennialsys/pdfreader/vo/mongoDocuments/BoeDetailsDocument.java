package com.perennialsys.pdfreader.vo.mongoDocuments;

import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;

@Document(value = NoSqlDBTables.BOE_DETAILS_STATEMENT)
@Data
@NoArgsConstructor
public class BoeDetailsDocument implements IGenericDocument {

    @Id
    private ObjectId id;

    @BsonProperty("txnId")
    private String txnId;

    @BsonProperty("subTxnId")
    private String subTxnId;

    @BsonProperty("pan")
    private String pan;

    @BsonProperty("itemCode")
    private String itemCode;

    @BsonProperty("itemDesc")
    private String itemDesc;

    @BsonProperty("boeNo")
    private String billNo;

    @BsonProperty("boeDate")
    private Date boeDate;

    @BsonProperty("customHouse")
    private String customHouse;

    @BsonProperty("hsn")
    private String hsn;

    @BsonProperty("importedQty")
    private Double importedQty;

    @BsonProperty("availableQty")
    private Double availableQty;

    @BsonProperty("utilizedQty")
    private double utilizedQty;

    @BsonProperty("uqc")
    private String uqc;

    @BsonProperty("assessableValueAsPerBoe")
    private Double assessableValueAsPerBoe;

    @BsonProperty("assess")
    private String assess;

    @BsonProperty("bcdRate")
    private Double bcdRate;

    @BsonProperty("customCessRate")
    private Double customCessRate;

    @BsonProperty("importedCountry")
    private String importedCountry;

    @BsonProperty("supplierName")
    private String supplierName;

    @BsonProperty("importersName")
    private String importersName;

    @BsonProperty("importersAddress")
    private String importersAddress;

    @BsonProperty("isFinalAssessment")
    private String isFinalAssessment;

    @BsonProperty("foreignMaterialsSupName")
    private String foreignMaterialsSupName;

    @BsonProperty("cust1")
    private String custom1;

    @BsonProperty("cust2")
    private String custom2;

    @BsonProperty("cust3")
    private String custom3;

    @BsonProperty("supplierAddress")
    private String supplierAddress;

    @BsonProperty("invNo")
    private String invNo;

    @BsonProperty("invDate")
    private Date invDate;

    @BsonProperty("igmNo")
    private String igmNo;

    @BsonProperty("hss")
    private String hss;

    @BsonProperty("provFinal")
    private String provFinal;

    @BsonProperty("portOfLoading")
    private String portOfLoading;

    @BsonProperty("igmDate")
    private Date igmDate;

    @BsonProperty("mode")
    private String mode;

    @BsonProperty("inwDate")
    private Date inwDate;

    @BsonProperty("gigmDt")
    private Date gigmDt;

    @BsonProperty("date")
    private Date date;

    @BsonProperty("manifiestDate")
    private Date manifiestDate;

    @BsonProperty("sec48")
    private String sec48;

    @BsonProperty("gigmNo")
    private String gigmNo;

    @BsonProperty("maqbNo")
    private String maqbNo;

    @BsonProperty("hawbNo")
    private String hawbNo;

    @BsonProperty("pkg")
    private String pkg;

    @BsonProperty("gw")
    private String gw;

    @BsonProperty("reImp")
    private String reImp;

    @BsonProperty("advBe")
    private String advBe;

    @BsonProperty("exam")
    private String exam;

    @BsonProperty("firstCheck")
    private String firstCheck;

    @BsonProperty("adCode")
    private String adCode;

    @BsonProperty("beType")
    private String beType;

    @BsonProperty("exchangeRate")
    private String exchangeRate;

    @BsonProperty("gstin")
    private String gstin;

    @BsonProperty("gstinType")
    private String gstinType;

    @BsonProperty("iec")
    private String iec;

    @BsonProperty("portCode")
    private String portCode;

    @BsonProperty("bcdAmount")
    private Double bcdAmount;

    @BsonProperty("unitPrice")
    private Double unitPrice;

    @BsonProperty("acdRate")
    private Double acdRate;

    @BsonProperty("acdAmount")
    private Double acdAmount;

    @BsonProperty("swsRate")
    private Double swsRate;

    @BsonProperty("swsAmount")
    private Double swsAmount;

    @BsonProperty("totalDuty")
    private Double totalDuty;

    @BsonProperty("sadRate")
    private Double sadRate;

    @BsonProperty("sadAmount")
    private Double sadAmount;

    @BsonProperty("igstRate")
    private Double igstRate;

    @BsonProperty("igstAmount")
    private Double igstAmount;

    @BsonProperty("cessAmount")
    private Double cessAmount;

    @BsonProperty("addRate")
    private Double addRate;

    @BsonProperty("addAmount")
    private Double addAmount;

    @BsonProperty("cvdRate")
    private Double cvdRate;

    @BsonProperty("cvdAmount")
    private Double cvdAmount;

    @BsonProperty("invValue")
    private Double invValue;

    @BsonProperty("invCurrency")
    private String invCurrency;

    @BsonProperty("term")
    private String term;

    @BsonProperty("freight")
    private String freight;

    @BsonProperty("itemValue")
    private Double itemValue;

    @BsonProperty("oocDate")
    private Date oocDate;

    @BsonProperty("oocNo")
    private String oocNo;

    @BsonProperty("licUqc")
    private String licUqc;

    @BsonProperty("licDebitDuty")
    private Double licDebitDuty;

    @BsonProperty("licSlNo")
    private String licSlNo;

    @BsonProperty("licNo")
    private String licNo;

    @BsonProperty("licDate")
    private Date licDate;

    @BsonProperty("licCode")
    private String licCode;

    @BsonProperty("licPort")
    private String licPort;

    @BsonProperty("licDebitValue")
    private Double licDebitValue;

    @BsonProperty("licQty")
    private Double licQty;

    @BsonProperty("svbRefNo")
    private String svbRefNo;

    @BsonProperty("svbRefDate")
    private Date svbRefDate;

    @BsonProperty("svbPartCode")
    private String svbPartCode;

    @BsonProperty("svbLab")
    private String svbLab;

    @BsonProperty("svbPf1")
    private String svbPf1;

    @BsonProperty("svbLoadDate")
    private Date svbLoadDate;

    @BsonProperty("svbPf2")
    private String svbPf2;

    @BsonProperty("prevBoeNo")
    private String prevBoeNo;

    @BsonProperty("prevBoeDate")
    private Date prevBoeDate;

    @BsonProperty("prevBoePartCode")
    private String prevBoePartCode;

    @BsonProperty("prevBoeUnitPrice")
    private Double prevBoeUnitPrice;

    @BsonProperty("prevBoeCurrencyCode")
    private String prevBoeCurrencyCode;

    @BsonProperty("reImpNoteNo")
    private String reImpNoteNo;

    @BsonProperty("reImpSlNo")
    private String reImpSlNo;

    @BsonProperty("reImpFrt")
    private String reImpFrt;

    @BsonProperty("reImpUnitIns")
    private String reImpUnitIns;

    @BsonProperty("reImpDuty")
    private Double reImpDuty;

    @BsonProperty("reImpSbNo")
    private String reImpSbNo;

    @BsonProperty("reImpSbDate")
    private Date reImpSbDate;

    @BsonProperty("reImpPortCd")
    private String reImpPortCd;

    @BsonProperty("reImpSinv")
    private String reImpSinv;

    @BsonProperty("reImpSitemN")
    private String reImpSitemN;

    @BsonProperty("itemManuType")
    private String itemManuType;

    @BsonProperty("itemManuManufacturerCode")
    private String itemManuManufacturerCode;

    @BsonProperty("itemManuSorceCy")
    private String itemManuSorceCy;

    @BsonProperty("itemManuTransCy")
    private String itemManuTransCy;

    @BsonProperty("itemManuAddress")
    private String itemManuAddress;

    @BsonProperty("accessoryItmDtls")
    private String accessoryItmDtls;

    @BsonProperty("beStatus")
    private String beStatus;

    @BsonProperty("defBe")
    private String defBe;

    @BsonProperty("kacha")
    private String kacha;

    @BsonProperty("countryOfConsignment")
    private String countryOfConsignment;

    @BsonProperty("portOfShipment")
    private String portOfShipment;

    @BsonProperty("cbName")
    private String cbName;

    @BsonProperty("aeo")
    private String aeo;

    @BsonProperty("ucr")
    private String ucr;

    @BsonProperty("bcd")
    private String bcd;

    @BsonProperty("acd")
    private String acd;

    @BsonProperty("sws")
    private String sws;

    @BsonProperty("nccd")
    private String nccd;

    @BsonProperty("add")
    private String add;

    @BsonProperty("cvd")
    private String cvd;

    @BsonProperty("igst")
    private String igst;

    @BsonProperty("cess")
    private String cess;

    @BsonProperty("totalAssValue")
    private String totalAssValue;

    @BsonProperty("sg")
    private String sg;

    @BsonProperty("aed")
    private String aed;

    @BsonProperty("gsia")
    private String gsia;

    @BsonProperty("tta")
    private String tta;

    @BsonProperty("health")
    private String health;

    @BsonProperty("dutySummaryTotalDuty")
    private String dutySummaryTotalDuty;

    @BsonProperty("int")
    private String dutySummaryInt;

    @BsonProperty("penalty")
    private String penalty;

    @BsonProperty("fine")
    private String fine;

    @BsonProperty("totalAmount")
    private String totalAmount;

    @BsonProperty("purOrdeNo")
    private String purOrdeNo;

    @BsonProperty("purOrderDate")
    private String purOrderDate;

    @BsonProperty("lcNo")
    private String lcNo;

    @BsonProperty("lcDate")
    private String lcDate;

    @BsonProperty("contracNo")
    private String contracNo;

    @BsonProperty("contractDate")
    private String contractDate;

    @BsonProperty("buyersName")
    private String buyersName;

    @BsonProperty("buyersAddress")
    private String buyersAddress;

    @BsonProperty("sellersName")
    private String sellersName;

    @BsonProperty("sellersAddress")
    private String sellersAddress;

    @BsonProperty("thirdPartyName")
    private String thirdPartyName;

    @BsonProperty("thirdPartyAddress")
    private String thirdPartyAddress;

    @BsonProperty("transactingPartiesAeo")
    private String transactingPartiesAeo;

    @BsonProperty("transactingPartiesAdCode")
    private String transactingPartiesAdCode;

    @BsonProperty("insurance")
    private String insurance;

    @BsonProperty("valuationHss")
    private String valuationHss;

    @BsonProperty("loading")
    private String loading;

    @BsonProperty("commn")
    private String commn;

    @BsonProperty("payTerms")
    private String payTerms;

    @BsonProperty("valTerms")
    private String valTerms;

    @BsonProperty("reltd")
    private String reltd;

    @BsonProperty("svbCh")
    private String svbCh;

    @BsonProperty("svbNo")
    private String svbNo;

    @BsonProperty("valuationDate")
    private String valuationDate;

    @BsonProperty("loa")
    private String loa;

    @BsonProperty("cb")
    private String cb;

    @BsonProperty("coc")
    private String coc;

    @BsonProperty("cop")
    private String cop;

    @BsonProperty("hindChg")
    private String hindChg;

    @BsonProperty("gs")
    private String gs;

    @BsonProperty("docCh")
    private String docCh;

    @BsonProperty("coo")
    private String coo;

    @BsonProperty("rLf")
    private String rLf;

    @BsonProperty("othCost")
    private String othCost;

    @BsonProperty("ldUld")
    private String ldUld;

    @BsonProperty("ws")
    private String ws;

    @BsonProperty("otc")
    private String otc;

    @BsonProperty("miscChg")
    private String miscChg;

    @BsonProperty("assValue")
    private String assValue;

    @BsonProperty("fs")
    private String fs;

    @BsonProperty("pq")
    private String pq;

    @BsonProperty("dc")
    private String dc;

    @BsonProperty("wc")
    private String wc;

    @BsonProperty("aq")
    private String aq;

    @BsonProperty("upi")
    private String upi;

    @BsonProperty("itemDtlsCoo")
    private String itemDtlsCoo;

    @BsonProperty("cQty")
    private String cQty;

    @BsonProperty("cUqc")
    private String cUqc;

    @BsonProperty("sQty")
    private String sQty;

    @BsonProperty("sUqc")
    private String sUqc;

    @BsonProperty("sch")
    private String sch;

    @BsonProperty("stndPr")
    private String stndPr;

    @BsonProperty("rsp")
    private String rsp;

    @BsonProperty("reimp")
    private String reimp;

    @BsonProperty("prov")
    private String prov;

    @BsonProperty("endUse")
    private String endUse;

    @BsonProperty("prodn")
    private String prodn;

    @BsonProperty("cntrl")
    private String cntrl;

    @BsonProperty("qualfr")
    private String qualfr;

    @BsonProperty("contnt")
    private String contnt;

    @BsonProperty("stmnt")
    private String stmnt;

    @BsonProperty("supDocs")
    private String supDocs;

    @BsonProperty("notnNo")
    private String notnNo;

    @BsonProperty("notnSrNo")
    private String notnSrNo;

    @BsonProperty("bcdDutyFg")
    private String bcdDutyFg;

    @BsonProperty("acdDutyNotnNo")
    private String acdDutyNotnNo;

    @BsonProperty("acdDutyNotnSrNo")
    private String acdDutyNotnSrNo;

    @BsonProperty("acdDutyFg")
    private String acdDutyFg;

    @BsonProperty("swsDutyNotnNo")
    private String swsDutyNotnNo;

    @BsonProperty("swsDutyNotnSrNo")
    private String swsDutyNotnSrNo;

    @BsonProperty("swsDutyFg")
    private String swsDutyFg;

    @BsonProperty("sadDutyNotnNo")
    private String sadDutyNotnNo;

    @BsonProperty("sadDutyNotnSrNo")
    private String sadDutyNotnSrNo;

    @BsonProperty("sadDutyFg")
    private String sadDutyFg;

    @BsonProperty("igstNotnNo")
    private String igstNotnNo;

    @BsonProperty("igstNotnSrNo")
    private String igstNotnSrNo;

    @BsonProperty("igstDutyFg")
    private String igstDutyFg;

    @BsonProperty("cessNotnNo")
    private String cessNotnNo;

    @BsonProperty("cessNotnSrNo")
    private String cessNotnSrNo;

    @BsonProperty("cessDutyFg")
    private String cessDutyFg;

    @BsonProperty("addDutyNotnNo")
    private String addDutyNotnNo;

    @BsonProperty("addDutyNotnSrNo")
    private String addDutyNotnSrNo;

    @BsonProperty("addDutyFg")
    private String addDutyFg;

    @BsonProperty("cvdDutyNotnNo")
    private String cvdDutyNotnNo;

    @BsonProperty("cvdDutyNotnSrNo")
    private String cvdDutyNotnSrNo;

    @BsonProperty("cvdDutyFg")
    private String cvdDutyFg;

    @BsonProperty("sgNotnNo")
    private String sgNotnNo;

    @BsonProperty("sgNotnSrNo")
    private String sgNotnSrNo;

    @BsonProperty("sgRate")
    private String sgRate;

    @BsonProperty("sgAmt")
    private String sgAmt;

    @BsonProperty("sgDutyFg")
    private String sgDutyFg;

    @BsonProperty("spExdNotn_no")
    private String spExdNotn_no;

    @BsonProperty("spExdNotnSrNo")
    private String spExdNotnSrNo;

    @BsonProperty("spExdRate")
    private String spExdRate;

    @BsonProperty("spExdAmt")
    private String spExdAmt;

    @BsonProperty("spExdDutyFg")
    private String spExdDutyFg;

    @BsonProperty("chCessNotnNo")
    private String chCessNotnNo;

    @BsonProperty("chCessNotnSrNo")
    private String chCessNotnSrNo;

    @BsonProperty("chCessRate")
    private String chCessRate;

    @BsonProperty("chCessAmt")
    private String chCessAmt;

    @BsonProperty("chCessDutyFg")
    private String chCessDutyFg;

    @BsonProperty("ttaNotnNo")
    private String ttaNotnNo;

    @BsonProperty("ttaNotnSrNo")
    private String ttaNotnSrNo;

    @BsonProperty("ttaRate")
    private String ttaRate;

    @BsonProperty("ttaAmt")
    private String ttaAmt;

    @BsonProperty("ttaDutyFg")
    private String ttaDutyFg;

    @BsonProperty("othDutyCessNotnNo")
    private String othDutyCessNotnNo;

    @BsonProperty("othDutyCessNotnSrNo")
    private String othDutyCessNotnSrNo;

    @BsonProperty("othDutyCessRate")
    private String othDutyCessRate;

    @BsonProperty("othDutyCessAmt")
    private String othDutyCessAmt;

    @BsonProperty("othDutyCessDutyFg")
    private String othDutyCessDutyFg;

    @BsonProperty("cvdEdcNotnNo")
    private String cvdEdcNotnNo;

    @BsonProperty("cvdEdcNotnSrNo")
    private String cvdEdcNotnSrNo;

    @BsonProperty("cvdEdcRate")
    private String cvdEdcRate;

    @BsonProperty("cvdEdcAmt")
    private String cvdEdcAmt;

    @BsonProperty("cvdEdcDutyFg")
    private String cvdEdcDutyFg;

    @BsonProperty("cvdHecNotnNo")
    private String cvdHecNotnNo;

    @BsonProperty("cvdHecNotnSrNo")
    private String cvdHecNotnSrNo;

    @BsonProperty("cvdHecRate")
    private String cvdHecRate;

    @BsonProperty("cvdHecAmt")
    private String cvdHecAmt;

    @BsonProperty("cvdHecDutyFg")
    private String cvdHecDutyFg;

    @BsonProperty("cusEdcNotnNo")
    private String cusEdcNotnNo;

    @BsonProperty("cusEdcNotnSrNo")
    private String cusEdcNotnSrNo;

    @BsonProperty("cusEdcRate")
    private String cusEdcRate;

    @BsonProperty("cusEdcAmt")
    private String cusEdcAmt;

    @BsonProperty("cusEdcDutyFg")
    private String cusEdcDutyFg;

    @BsonProperty("cusHecNotnNo")
    private String cusHecNotnNo;

    @BsonProperty("cusHecNotnSrNo")
    private String cusHecNotnSrNo;

    @BsonProperty("cusHecRate")
    private String cusHecRate;

    @BsonProperty("cusHecAmt")
    private String cusHecAmt;

    @BsonProperty("cusHecDutyFg")
    private String cusHecDutyFg;

    @BsonProperty("ncdNotnNo")
    private String ncdNotnNo;

    @BsonProperty("ncdNotnSrNo")
    private String ncdNotnSrNo;

    @BsonProperty("ncdRate")
    private String ncdRate;

    @BsonProperty("ncdAmt")
    private String ncdAmt;

    @BsonProperty("ncdDutyFg")
    private String ncdDutyFg;

    @BsonProperty("aggrNotnNo")
    private String aggrNotnNo;

    @BsonProperty("aggrNotnSrNo")
    private String aggrNotnSrNo;

    @BsonProperty("aggrRate")
    private String aggrRate;

    @BsonProperty("aggrAmt")
    private String aggrAmt;

    @BsonProperty("aggrDutyFg")
    private String aggrDutyFg;

    @BsonProperty("processingStatus")
    private String processingStatus;

    @BsonProperty("billOfEntrySubmissionDate")
    private String billOfEntrySubmissionDate;

    @BsonProperty("billOfEntrySubmissionTime")
    private String billOfEntrySubmissionTime;

    @BsonProperty("billOfEntryAssessmentDate")
    private String billOfEntryAssessmentDate;

    @BsonProperty("billOfEntryAssessmentTime")
    private String billOfEntryAssessmentTime;

    @BsonProperty("billOfEntryExaminationDate")
    private String billOfEntryExaminationDate;

    @BsonProperty("billOfEntryExaminationTime")
    private String billOfEntryExaminationTime;

    @BsonProperty("billOfEntryOocDate")
    private String billOfEntryOocDate;

    @BsonProperty("billOfEntryOocTime")
    private String billOfEntryOocTime;

    @BsonProperty("createdAt")
    private Date createdAt;

    @BsonProperty("updatedAt")
    private Date updatedAt;

    public String getReImp(){
        return this.reimp;
    }
    public void setReImp(String reimp){
        this.reimp = reimp;
    }
}
