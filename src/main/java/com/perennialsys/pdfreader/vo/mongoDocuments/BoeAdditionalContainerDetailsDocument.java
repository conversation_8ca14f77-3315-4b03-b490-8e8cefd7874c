package com.perennialsys.pdfreader.vo.mongoDocuments;

import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoeAdditionalContainerDetailsDocument implements IGenericDocument {

    @BsonProperty("invNo")
    private String invNo;

    @BsonProperty("itemNo")
    private String itemNo;

    @BsonProperty("additionalContainerNo")
    private String additionalContainerNo;

    @BsonProperty("additionalContainerTruckNo")
    private String additionalContainerTruckNo;

    @BsonProperty("additionalContainerSealNo")
    private String additionalContainerSealNo;

    @BsonProperty("additionalContainerFclLcL")
    private String additionalContainerFclLcL;
}
