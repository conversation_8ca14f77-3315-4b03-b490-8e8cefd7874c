package com.perennialsys.pdfreader.vo.mongoDocuments;

import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;
import java.util.List;

@Document(value = NoSqlDBTables.BOE_ADDITIONAL_DETAILS_STATEMENT)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoeAdditionalDetailsDocument implements IGenericDocument {

    @Id
    private ObjectId id;

    @BsonProperty("txnId")
    private String txnId;

    @BsonProperty("subTxnId")
    private String subTxnId;

    @BsonProperty("pan")
    private String pan;

    @BsonProperty("boeNo")
    private String boeNo;

    @BsonProperty("singleWindowDetails")
    private List<BoeAdditionalSingleWindowDetailsDocument> singleWindowDetailsList;

    @BsonProperty("singleWindowConstDetails")
    private List<BoeAdditionalSingleWindowConstituentsDetailsDocument> singleWindowConstDtlsList;

    @BsonProperty("singleWindowControlDetails")
    private List<BoeAdditionalSingleWindowControlDetailsDocument> singleWindowControlDtlsList;

    @BsonProperty("supportingDocsDetails")
    private List<BoeAdditionalSupportingDocDetailsDocument> supportingDocsDtlsList;

    @BsonProperty("additionalContainerDetails")
    private List<BoeAdditionalContainerDetailsDocument> additionalContainerDtlsList;

    @BsonProperty("boeBondDetails")
    private List<BoeBondDetailsDocument> boeBondDetails;

    @BsonProperty("boeWarehouseDetails")
    private List<BoeWarehouseDetailsDocument> boeWarehouseDtlsList;

    @BsonProperty("boePaymentDetails")
    private List<BoePaymentDetailsDocument> boePaymentDtlsList;

    @BsonProperty("createdAt")
    private Date createdAt;

    @BsonProperty("updatedAt")
    private Date updatedAt;
}
