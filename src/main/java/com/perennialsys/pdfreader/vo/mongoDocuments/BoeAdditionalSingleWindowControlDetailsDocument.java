package com.perennialsys.pdfreader.vo.mongoDocuments;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoeAdditionalSingleWindowControlDetailsDocument implements IGenericDocument {

    @BsonProperty("invSN")
    private String invSN;

    @BsonProperty("itemSN")
    private String itemSN;

    @BsonProperty("singleWindowControlType")
    private String singleWindowControlType;

    @BsonProperty("singleWindowControlLocation")
    private String singleWindowControlLocation;

    @BsonProperty("singleWindowControlStartDt")
    private String singleWindowControlStartDt;

    @BsonProperty("singleWindowControlEndDt")
    private String singleWindowControlEndDt;

    @BsonProperty("singleWindowControlResCd")
    private String singleWindowControlResCd;

    @BsonProperty("singleWindowControlResText")
    private String singleWindowControlResText;
}
