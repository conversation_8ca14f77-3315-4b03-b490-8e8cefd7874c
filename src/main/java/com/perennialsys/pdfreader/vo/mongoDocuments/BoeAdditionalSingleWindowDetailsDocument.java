package com.perennialsys.pdfreader.vo.mongoDocuments;

import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoeAdditionalSingleWindowDetailsDocument implements IGenericDocument {

    @BsonProperty("invSN")
    private String invSN;

    @BsonProperty("itemSN")
    private String itemSN;

    @BsonProperty("singleWindowType")
    private String singleWindowType;

    @BsonProperty("singleWindowQualifier")
    private String singleWindowQualifier;

    @BsonProperty("singleWindowInfoCode")
    private String singleWindowInfoCode;

    @BsonProperty("singleWindowInfoText")
    private String singleWindowInfoText;

    @BsonProperty("singleWindowInfoMsr")
    private String singleWindowInfoMsr;

    @BsonProperty("singleWindowUqc")
    private String singleWindowUqc;
}
