package com.perennialsys.pdfreader.vo.mongoDocuments;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoeBondDetailsDocument implements IGenericDocument {

    @BsonProperty("bondNo")
    private String bondNo;

    @BsonProperty("bondPort")
    private String bondPort;

    @BsonProperty("bondCode")
    private String bondCode;

    @BsonProperty("bondDebtAmt")
    private String bondDebtAmt;

    @BsonProperty("bondBgAmt")
    private String bondBgAmt;
}
