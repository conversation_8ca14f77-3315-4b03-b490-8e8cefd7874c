package com.perennialsys.pdfreader.vo.mongoDocuments;

import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoeAdditionalSupportingDocDetailsDocument implements IGenericDocument {

    @BsonProperty("supportingDocsIceGateId")
    private String supportingDocsIceGateId;

    @BsonProperty("supportingDocsType")
    private String supportingDocsType;

    @BsonProperty("supportingDocIrn")
    private String supportingDocIrn;

    @BsonProperty("supportingDocsCd")
    private String supportingDocsCd;

    @BsonProperty("supportingDocsIssuePlace")
    private String supportingDocsIssuePlace;

    @BsonProperty("supportingDocsIssueDt")
    private String supportingDocsIssueDt;

    @BsonProperty("supportingDocsExportDt")
    private String supportingDocsExportDt;
}
