package com.perennialsys.pdfreader.vo.mongoDocuments;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoeWarehouseDetailsDocument implements IGenericDocument {

    @BsonProperty("whWbeNo")
    private String whWbeNo;

    @BsonProperty("whDate")
    private Date whDate;

    @BsonProperty("whWbeSite")
    private String whWbeSite;

    @BsonProperty("whCode")
    private String whCode;
}
