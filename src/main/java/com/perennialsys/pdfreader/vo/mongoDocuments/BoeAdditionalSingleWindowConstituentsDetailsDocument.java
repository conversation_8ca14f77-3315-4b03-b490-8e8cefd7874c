package com.perennialsys.pdfreader.vo.mongoDocuments;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoeAdditionalSingleWindowConstituentsDetailsDocument implements IGenericDocument {

    @BsonProperty("invSN")
    private String invSN;

    @BsonProperty("itemSN")
    private String itemSN;

    @BsonProperty("singleWindowConstCsNo")
    private String singleWindowConstCsNo;

    @BsonProperty("singleWindowConstName")
    private String singleWindowConstName;

    @BsonProperty("singleWindowConstCode")
    private String singleWindowConstCode;

    @BsonProperty("singleWindowConstPercentage")
    private Double singleWindowConstPercentage;

    @BsonProperty("singleWindowConstYieldPct")
    private String singleWindowConstYieldPct;

    @BsonProperty("singleWindowConstIng")
    private String singleWindowConstIng;
}
