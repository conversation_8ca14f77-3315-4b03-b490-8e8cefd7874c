package com.perennialsys.pdfreader.vo.mongoDocuments;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoePaymentDetailsDocument implements IGenericDocument {

    @BsonProperty("paymentChallanNo")
    private String paymentChallanNo;

    @BsonProperty("paymentPaidNo")
    private Date paymentPaidNo;

    @BsonProperty("paymentAmount")
    private Double paymentAmount;
}
