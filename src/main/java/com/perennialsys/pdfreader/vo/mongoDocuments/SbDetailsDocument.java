package com.perennialsys.pdfreader.vo.mongoDocuments;

import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;
import java.util.List;

@Document(value = NoSqlDBTables.SB_DETAILS_STATEMENT)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SbDetailsDocument implements IGenericDocument {

    @Id
    private ObjectId id;
    @BsonProperty("txnId")
    private String txnId;
    @BsonProperty("subTxnId")
    private String subTxnId;
    @BsonProperty("pan")
    private String pan;
    @BsonProperty("invNo")
    private String invoiceNo;
    @BsonProperty("invDate")
    private Date invoiceDate;
    @BsonProperty("shippingBillNo")
    private String shippingBillNo;
    @BsonProperty("ShippingBillDate")
    private Date ShippingBillDate;
    @BsonProperty("applicantType")
    private String applicantType;
    @BsonProperty("iec")
    private String iec;
    @BsonProperty("products")
    private List<SbDetailsProduct> products;
    @BsonProperty("invValExcTax")
    private Double invValExcTax;
    @BsonProperty("invFobVal")
    private Double fobVal;
    @BsonProperty("leoDate")
    private Date leoDate;
    @BsonProperty("leoNo")
    private String leoNo;
    @BsonProperty("invVal")
    private Double invVal;
    @BsonProperty("invTerm")
    private String invTrem;
    @BsonProperty("freight")
    private String freight;
    @BsonProperty("insurance")
    private String insurance;
    @BsonProperty("commission")
    private String commission;
    @BsonProperty("discount")
    private String discount;
    @BsonProperty("otherDeductions")
    private String otherDeductions;
    @BsonProperty("sbInsurance")
    private String sbInsurance;
    @BsonProperty("sbCommission")
    private String sbCommission;
    @BsonProperty("sbDiscount")
    private String sbDiscount;
    @BsonProperty("sbOtherDeductions")
    private String sbOtherDeductions;
    @BsonProperty("exchangeRate")
    private String exchangeRate;
    @BsonProperty("customHouseName")
    private String customHouseName;
    @BsonProperty("portCode")
    private String portCode;
    @BsonProperty("gstin")
    private String gstin;
    @BsonProperty("gstinType")
    private String gstinType;
    @BsonProperty("mode")
    private String mode;
    @BsonProperty("exim")
    private String exim;
    @BsonProperty("meis")
    private String meis;
    @BsonProperty("dbk")
    private String dbk;
    @BsonProperty("deecDfia")
    private String deecDfia;
    @BsonProperty("dfrc")
    private String dfrc;
    @BsonProperty("reExp")
    private String reExp;
    @BsonProperty("lut")
    private String lut;
    @BsonProperty("stateOfOrigin")
    private String stateOfOrigin;
    @BsonProperty("countryOdFinalDestination")
    private String countryOdFinalDestination;
    @BsonProperty("portOfFinalDestination")
    private String portOfFinalDestination;
    @BsonProperty("countryOfDischarge")
    private String countryOfDischarge;
    @BsonProperty("exportorName")
    private String exportorName;
    @BsonProperty("exportorAddress")
    private String exportorAddress;
    @BsonProperty("consigneeName")
    private String consigneeName;
    @BsonProperty("consigneeAddress")
    private String consigneeAddress;
    @BsonProperty("thirdPartyName")
    private String thirdPartyName;
    @BsonProperty("thirdPartyAddress")
    private String thirdPartyAddress;
    @BsonProperty("adCode")
    private String adCode;
    @BsonProperty("rbiWaiverNo")
    private String rbiWaiverNo;
    @BsonProperty("rbiWaiverDate")
    private String rbiWaiverDate;
    @BsonProperty("cbName")
    private String cbName;
    @BsonProperty("aeo")
    private String aeo;
    @BsonProperty("ifscNo")
    private String ifscNo;
    @BsonProperty("dbkClaim")
    private String dbkClaim;
    @BsonProperty("igstAmt")
    private Double igstAmt;
    @BsonProperty("cessAmt")
    private Double cessAmt;
    @BsonProperty("invRodtepAmt")
    private Double rodtepAmt;
    @BsonProperty("brcRealisationDate")
    private Date brcRealisationDate;

    @BsonProperty("egmNo")
    private String egmNo;

    @BsonProperty("egmDt")
    private Date egmDt;

    @BsonProperty("invRosctlAmt")
    private Double rosctlAmt;

    @BsonProperty("rodtp")
    private String rodtp;

    @BsonProperty("invCurrency")
    private String invCurrency;

    @BsonProperty("processingStatus")
    private String processingStatus;

    @BsonProperty("createdAt")
    private Date createdAt;

    @BsonProperty("updatedAt")
    private Date updatedAt;

}
