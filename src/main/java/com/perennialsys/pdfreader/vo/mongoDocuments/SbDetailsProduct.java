package com.perennialsys.pdfreader.vo.mongoDocuments;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.codecs.pojo.annotations.BsonProperty;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SbDetailsProduct implements IGenericDocument {

    @BsonProperty("prodCode")
    private String productCode;
    @BsonProperty("prodDesc")
    private String productDescription;
    @BsonProperty("qtyAsPerShippingBill")
    private Double qtyAsPerShippingBill;
    @BsonProperty("uqc")
    private String uqc;
    @BsonProperty("availableStock")
    private Double availableStock;
    @BsonProperty("qtyConsumedInClaim")
    private Double qtyConsumedInClaim;
    @BsonProperty("cust1")
    private String cust1;
    @BsonProperty("cust2")
    private String cust2;
    @BsonProperty("cust3")
    private String cust3;
    @BsonProperty("rate")
    private Double rate;
    @BsonProperty("itemValFc")
    private Double itemValFc;
    @BsonProperty("itemFob")
    private Double itemFob;
    @BsonProperty("pmv")
    private Double pmv;
    @BsonProperty("igstStat")
    private String igstStat;
    @BsonProperty("itmIgstAmt")
    private Double itmIgstAmt;
    @BsonProperty("schCode")
    private String schCode;
    @BsonProperty("schemaDescription")
    private String schemaDescription;
    @BsonProperty("sqcMsr")
    private Double sqcMsr;
    @BsonProperty("ptAbroad")
    private String ptAbroad;
    @BsonProperty("compCess")
    private Double compCess;
    @BsonProperty("endUse")
    private String endUse;
    @BsonProperty("thirdPartyItem")
    private String thirdPartyItem;

    @BsonProperty("dbkSrNo")
    private String dbkSrNo;

    @BsonProperty("dbkValue")
    private Double dbkValue;

    @BsonProperty("dbkRate")
    private Double dbkRate;

    @BsonProperty("dbkAmt")
    private Double dbkAmt;

    @BsonProperty("rosctlAmt")
    private Double rosctlAmt;

    @BsonProperty("rodtepAmt")
    private Double rodtepAmt;

    @BsonProperty("itemHsnCode")
    private String itemHsnCode;

    @BsonProperty("assessableValue")
    private Double assessableValue;

    @BsonProperty("ftaBenefitAvailed")
    private String ftaBenefitAvailed;

    @BsonProperty("rewardBenefit")
    private String rewardBenefit;

    @BsonProperty("status")
    private String status;

    @BsonProperty("processingStatus")
    private String processingStatus;
}
