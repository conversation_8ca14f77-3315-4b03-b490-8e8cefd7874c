package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import com.perennialsys.pdfreader.dto.FieldIdentificationStrategyDto;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = DBTables.FIELD_IDENTIFICATION_STRATEGY)
@Getter
@Setter
@ToString
@RequiredArgsConstructor
public class FieldIdentificationStrategyVO implements IGenericVO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private long id;

    @Column(name = "PAN")
    private String pan;

    @Column(name = "FILE_TYPE")
    private String fileType;

    @Column(name = "TARGET_FIELD")
    private String targetField;

    @Column(name = "SOURCE_FIELD")
    private String sourceField;

    @Column(name = "STRATEGY")
    private String strategy;

    @Column(name = "TERMINATION")
    private String termination;

    @Column(name = "PREFIX_SUFFIX")
    private String prefixSuffix;

    @Column(name = "LENGTH")
    private int length;

    @Column(name = "SPECIAL_CHAR")
    private String specialChar;

    @Column(name = "IS_ACTIVE")
    private boolean isActive;

    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Column(name = "UPDATED_AT")
    private Date updatedAt;

    @Override
    public boolean equals(Object o) {
        boolean result = false;
        if (this == o) {
            result = true;
        } else if (o != null && Hibernate.getClass(this) == Hibernate.getClass(o)) {
            SftpOperationAuditLogVO that = (SftpOperationAuditLogVO) o;
            result = getId() != 0 && Objects.equals(getId(), that.getId());
        }

        return result;
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
