package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@Table(name = DBTables.REPORT_MANAGER_TBL)
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@AllArgsConstructor
public class ExportReportsManagerVO implements IGenericVO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "TXN_ID")
    private String txnId;

    @Column(name = "PAN")
    private String pan;

    @Column(name = "USER_ID")
    private long userId;

    @Column(name = "USER_NAME")
    private String userName;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "REPORT_NAME")
    private String reportName;

    @Column(name = "REPORT_TYPE")
    private String reportType;

    @Getter
    @Column(name = "REPORT_LOCATION")
    private String reportFilLoc;

    @Column(name = "FILE_ID")
    private String fileId;

    @Column(name = "SUB_TXN_ID")
    private String subTxnId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATED_AT")
    private Date updatedAt;

    @Column(name = "OTHER_DETAILS")
    private String otherDetails;

    @Column(name = "REMARK")
    private String remark;
}
