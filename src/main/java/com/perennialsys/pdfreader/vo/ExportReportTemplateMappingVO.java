package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = DBTables.EXPORT_TEMPLATE_MAPPING_TBL)
public class ExportReportTemplateMappingVO {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "REF_ID", nullable = false)
    private long refId;

    @Column(name = "MAPPING_ENTITY", length = 100, nullable = false)
    private String mappingEntity;

    @Column(name = "MAPPING_KEY", length = 30, nullable = false)
    private String mappingKey;

    @Column(name = "DISPLAY_NAME", length = 255, nullable = false)
    private String displayName;

    @Column(name = "MAPPING_SEQUENCE", nullable = false)
    private int sequenceNumber;

    @Column(name = "COLUMN_NAME", length = 255, nullable = false)
    private String columnName;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getRefId() {
        return refId;
    }

    public void setRefId(long refId) {
        this.refId = refId;
    }

    public String getMappingEntity() {
        return mappingEntity;
    }

    public void setMappingEntity(String mappingEntity) {
        this.mappingEntity = mappingEntity;
    }

    public String getMappingKey() {
        return mappingKey;
    }

    public void setMappingKey(String mappingKey) {
        this.mappingKey = mappingKey;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public int getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(int sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }
}
