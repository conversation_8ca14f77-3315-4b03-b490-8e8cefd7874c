package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = DBTables.PDF_FIELDS_STRATEGY_MAPPING_TBL)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PdfFieldStrategyMappingVO implements Serializable {
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    @Column(name = "FILE_TYPE")
    private String fileType;
    @Column(name = "FIELD_NAME")
    private String fieldName;
    @Column(name = "MAPPING_KEY")
    private String mappingKey;
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "SECTION_KEY", referencedColumnName = "SECTION_KEY")
    private PdfSectionDetailsVO sectionDetails;
    @Column(name = "OPTIONAL_FIELD")
    private boolean isOptionalField;
    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "STRATEGY_ID")
    private PdfParsingStrategiesVO strategy;
    @Column(name = "BUFFER_VALUE")
    private double bufferValue;
    @Column(name = "CREATED_AT")
    private Date createdAt;
    @Column(name = "UPDATED_AT")
    private Date updatedAt;
}
