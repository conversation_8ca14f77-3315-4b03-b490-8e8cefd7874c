package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = DBTables.SFTP_DETAILS)
@Getter
@Setter
@ToString
@RequiredArgsConstructor
public class SftpDetailsVO implements IGenericVO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private long id;

    @Column(name = "PAN")
    private String pan;

    @Column(name = "CLIENT_ID")
    private String clientId;

    @Column(name = "USR_ID")
    private String usrId;

    @Column(name = "HOST_IP")
    private String hostIp;

    @Column(name = "USER_NAME")
    private String userName;

    @Column(name = "PASSWORD")
    private String password;

    @Column(name = "IS_ACTIVE")
    private boolean isActive;

    @Column(name = "AUTH_TYPE")
    private String authType;

    @Column(name = "PPK_FILE_PATH")
    private String ppkFilePath;

    @Column(name = "PORT_NUMBER")
    private int portNumber;

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "sftpDetailsId")
    private List<SftpFilePathMappingVO> pathMappingList;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATED_AT")
    private Date updatedAt;

    @Override
    public boolean equals(Object o) {
        boolean result = false;
        if (this == o){
            result = true;
        }else if (o != null && Hibernate.getClass(this) == Hibernate.getClass(o)){
            SftpOperationAuditLogVO that = (SftpOperationAuditLogVO) o;
            result = getId() != 0 && Objects.equals(getId(), that.getId());
        }

        return result;
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
