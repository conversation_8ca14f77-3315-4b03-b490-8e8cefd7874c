package com.perennialsys.pdfreader.vo;

import com.perennialsys.pdfreader.constants.DBTables;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = DBTables.SFTP_FILE_PATH_MAPPING)
@Getter
@Setter
@ToString
@RequiredArgsConstructor
public class SftpFilePathMappingVO implements IGenericVO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private long id;

    @Column(name = "SFTP_ID")
    private long sftpDetailsId;

    @Column(name = "FILE_TYPE")
    private String fileType;

    @Column(name = "IN_FILE_PATH")
    private String inFilePath;

    @Column(name = "OUT_FILE_PATH")
    private String outFilePath;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATED_AT")
    private Date updatedAt;

    @Override
    public boolean equals(Object o) {
        boolean result = false;
        if (this == o){
            result = true;
        }else if (o != null && Hibernate.getClass(this) == Hibernate.getClass(o)){
            SftpOperationAuditLogVO that = (SftpOperationAuditLogVO) o;
            result = getId() != 0 && Objects.equals(getId(), that.getId());
        }

        return result;
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
