package com.perennialsys.pdfreader.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@AllArgsConstructor
@Data
@JsonPropertyOrder
public class ExportRequestDetailsDto {

    @JsonProperty("description")
    private String description;

    @JsonProperty("file-id-list")
    private List<String> fileIdList;

}