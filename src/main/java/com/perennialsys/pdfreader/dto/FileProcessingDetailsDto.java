package com.perennialsys.pdfreader.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FileProcessingDetailsDto {

    @JsonProperty("in-progress-files")
    private long inProgressFilesCount;

    @JsonProperty("file-details")
    private List<FileDetailsDto> fileDetailsDtoList;

    @JsonProperty("total-records")
    private long totalRecords;

}
