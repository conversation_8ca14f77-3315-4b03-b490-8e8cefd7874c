package com.perennialsys.pdfreader.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.enums.SBItemDetailsFields;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.helper.FieldIdentificationStrategies;
import com.perennialsys.pdfreader.helper.FieldIdentificationTerminationPolicies;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.FieldIdentificationStrategyVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class FieldIdentificationStrategyDto {
    @JsonProperty(value = "file-type")
    private String fileType;
    @JsonProperty(value = "target-field")
    private String targetField;
    @JsonProperty(value = "source-field")
    private String sourceField;
    @JsonProperty(value = "strategy")
    private String strategy;
    @JsonProperty(value = "termination")
    private String termination;
    @JsonProperty(value = "prefix-suffix")
    private String prefixSuffix;
    @JsonProperty(value = "length")
    private int length;
    @JsonProperty(value = "special-char")
    private String specialChar;
    @JsonProperty(value = "is-active")
    private boolean isActive;
    @JsonProperty(value = "created-date")
    private String createdDate;

    public FieldIdentificationStrategyDto(FieldIdentificationStrategyVO strategyVO){
            this.fileType = strategyVO.getFileType();
            this.targetField = strategyVO.getTargetField();
            this.sourceField = strategyVO.getSourceField();
            this.strategy = strategyVO.getStrategy();
            this.termination = strategyVO.getTermination();
            this.prefixSuffix = strategyVO.getPrefixSuffix();
            this.length = strategyVO.getLength();
            this.specialChar = strategyVO.getSpecialChar();
            this.isActive = strategyVO.isActive();
            this.createdDate = DateFormatUtil.convertDateToStringAndFormat(strategyVO.getCreatedAt(), DateFormatUtil.ddMMYYYY_Hyphen_with_time);
    }

    private static boolean validateSourceOrTargetFields(String fileType, String fieldName){
        boolean result = true;
        try {
            if (PdfFileType.BILL_OF_ENTRY.name().equals(fileType)) {
                BoeDetailsFields.getFieldEnum(fieldName);
            } else {
                SBItemDetailsFields.getFieldEnum(fieldName);
            }
        }catch (PdfReaderException e){
            result = false;
        }

        return result;
    }

    public static boolean validate(FieldIdentificationStrategyDto dto) throws PdfReaderException {

        if(!StringUtils.isBlank(dto.getFileType())){
            PdfFileType.validatePdfFileType(dto.getFileType());
        }else {
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_FILE_TYPE);
        }

        if(StringUtils.isBlank(dto.getTargetField())){
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_TARGET_FIELD);
        }else {
            boolean isValid = validateSourceOrTargetFields(dto.getFileType(), dto.getTargetField());
            if(!isValid){
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_TARGET_FIELD);
            }
        }

        if(StringUtils.isBlank(dto.getSourceField())){
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_SOURCE_FIELD);
        }else {
            boolean isValid = validateSourceOrTargetFields(dto.getFileType(), dto.getSourceField());
            if(!isValid){
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SOURCE_FIELD);
            }
        }

        FieldIdentificationStrategies.validateStrategy(dto.getStrategy());
        FieldIdentificationTerminationPolicies.validateTermination(dto.getTermination(), dto.getStrategy());

        validateTerminationValue(dto.getStrategy(), dto.getPrefixSuffix(), dto.getLength(), dto.getSpecialChar());

        return true;
    }

    private static void validateTerminationValue(String strategy, String prefixSuffix, int length, String specialChar) throws PdfReaderException{
        boolean valid = true;
        if(FieldIdentificationStrategies.WITH_PREFIX.name().equals(strategy)
                || FieldIdentificationStrategies.WITH_SUFFIX.name().equals(strategy)){

            valid = StringUtils.isNotBlank(prefixSuffix) || StringUtils.isNotBlank(specialChar) || length > 0;
        }

        if(!valid){
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_TERMINATION_POLICY_VALUE);
        }
    }

    public boolean validate() throws PdfReaderException {
        return validate(this);
    }
}
