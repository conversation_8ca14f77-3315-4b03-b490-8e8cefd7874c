package com.perennialsys.pdfreader.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class ExportHistoryDetailsDto {
    @JsonProperty("export-time")
    private String exportTime;
    @JsonProperty("export-period")
    private String exportPeriod;
    @JsonProperty("file-count")
    private long fileCount;
    @JsonProperty("exported-by")
    private String exportedBy;
    @JsonProperty("export-id")
    private String exportId;
    @JsonProperty("status")
    private String status;
    @JsonProperty("remark")
    private String remark;
}
