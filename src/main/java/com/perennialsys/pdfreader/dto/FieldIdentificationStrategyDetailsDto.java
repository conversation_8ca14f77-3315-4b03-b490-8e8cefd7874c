package com.perennialsys.pdfreader.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.perennialsys.pdfreader.vo.FieldIdentificationStrategyVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class FieldIdentificationStrategyDetailsDto {

    @JsonProperty(value = "file-type")
    private String fileType;

    @JsonProperty(value = "inactive-strategy-list")
    private List<FieldIdentificationStrategyDto> inactiveStrategyDtoList;

    @JsonProperty(value = "active-strategy-list")
    private List<FieldIdentificationStrategyDto> activeStrategyDtoList;

    public FieldIdentificationStrategyDetailsDto(String fileType, List<FieldIdentificationStrategyVO> strategyVOList){
        this.fileType = fileType;
        this.activeStrategyDtoList = new ArrayList<>();
        this.inactiveStrategyDtoList = new ArrayList<>();
        for(FieldIdentificationStrategyVO strategyVO : strategyVOList){
            if(strategyVO.isActive()) {
                this.activeStrategyDtoList.add(new FieldIdentificationStrategyDto(strategyVO));
            }else {
                this.inactiveStrategyDtoList.add(new FieldIdentificationStrategyDto(strategyVO));
            }
        }
    }
}
