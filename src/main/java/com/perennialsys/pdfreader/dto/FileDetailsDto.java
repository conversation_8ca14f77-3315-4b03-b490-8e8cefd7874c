package com.perennialsys.pdfreader.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileDetailsDto {
    @JsonProperty("file-id")
    private String fileId;

    @JsonProperty("file-type")
    private String fileType;

    @JsonProperty("file-name")
    private String fileName;

    @JsonProperty("processing-status")
    private String processingStatus;

    @JsonProperty("boe-no")
    private String boeNo;

    @JsonProperty("boe-date")
    private String boeDate;

    @JsonProperty("be-type")
    private String beType;

    @JsonProperty("sb-no")
    private String sbNo;

    @JsonProperty("sb-date")
    private String sbDate;

    @JsonProperty("upload-date")
    private String uploadDate;

    @JsonProperty("last-updated-date")
    private String lastUpdatedAt;

    @JsonProperty("last-updated-by")
    private String lastUpdatedBy;

    @JsonProperty("error-message")
    private String errorMessage;
}
