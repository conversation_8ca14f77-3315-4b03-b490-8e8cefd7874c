package com.perennialsys.pdfreader.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PdfFileTxnDetailsDto {
    //Transaction details
    @JsonProperty("file-type")
    private String fileType;
    @JsonProperty("file-name")
    private String fileName;
    @JsonProperty("processing-status")
    private String processingStatus;
    @JsonProperty("last-updated-date")
    private String lastUpdatedAt;
    @JsonProperty("last-updated-by")
    private String lastUpdatedBy;
    @JsonProperty("txn-id")
    private String txnId;
    @JsonProperty("txn-type")
    private String txnType;

    //File Processing details
    @JsonProperty("processing-details")
    private Map<String, Integer> fileProcessingDtls;

    //Uploaded Files Details
    @JsonProperty("file-details")
    private List<FileDetailsDto> fileDetailsDtoList;
    @JsonProperty("total-file-count")
    private int totalFileCount;

    @JsonProperty("error-message")
    private String errorMessage;
}
