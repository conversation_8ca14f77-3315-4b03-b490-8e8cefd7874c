package com.perennialsys.pdfreader.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.perennialsys.pdfreader.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.json.JSONObject;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonPropertyOrder
public class GenerateReportDetailsDto {

    @JsonProperty("description")
    private String description;

    @JsonProperty("file-count")
    private long fileCount;

    @JsonProperty("file-id-list")
    private List<String> fileIdList;

    @JsonProperty("start-period")
    private String startPeriod;

    @JsonProperty("end-period")
    private String endPeriod;

    @JsonProperty("error-details")
    private String errorDetails;

    @JsonProperty("completion-percentage")
    private double completionPercentage;

    @Override
    public String toString(){
        JSONObject jsonObject = new JSONObject();

        if(StringUtils.isNotBlank(this.description)){
            jsonObject.put("description", this.description);
        }

        if(this.fileCount > 0){
            jsonObject.put("file-count", this.fileCount);
        }

        if(null != this.fileIdList && !this.fileIdList.isEmpty()){
            jsonObject.put("file-id-list", this.fileIdList);
        }

        if(StringUtils.isNotBlank(this.startPeriod)){
            jsonObject.put("start-period", this.startPeriod);
        }

        if(StringUtils.isNotBlank(this.endPeriod)){
            jsonObject.put("end-period", this.endPeriod);
        }

        if(StringUtils.isNotBlank(this.errorDetails)){
            jsonObject.put("error-details", this.errorDetails);
        }

        jsonObject.put("completion-percentage", this.completionPercentage);

        return jsonObject.toString();
    }

}