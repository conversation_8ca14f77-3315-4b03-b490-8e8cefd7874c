package com.perennialsys.pdfreader.processor;

import com.perennialsys.pdfreader.bean.SftpConnectionBean;
import com.perennialsys.pdfreader.constants.AppConstants;
import com.perennialsys.pdfreader.constants.MeteringEntities;
import com.perennialsys.pdfreader.constants.MeteringEvents;
import com.perennialsys.pdfreader.enums.FileProcessingStatus;
import com.perennialsys.pdfreader.enums.FileSources;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.SftpOperations;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.enums.TransactionType;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.impl.TransactionHandler;
import com.perennialsys.pdfreader.helper.FileHelper;
import com.perennialsys.pdfreader.helper.SftpHandler;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.subscriptionService.handler.IMeteringHandler;
import com.perennialsys.pdfreader.util.AmazonS3StorageUtil;
import com.perennialsys.pdfreader.util.PdfReaderConfig;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import com.perennialsys.pdfreader.vo.SftpDetailsVO;
import com.perennialsys.pdfreader.vo.SftpFilePathMappingVO;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import org.apache.log4j.Logger;

import java.io.File;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Callable;

/**
 * <AUTHOR> Nagare
 * @since 20/06/2023
 *
 * This processor will be used to download and upload the files to SFTP
 */
public class ConcurrentSftpFileDownloader implements Callable<Boolean> {
    private static final Logger LOG = Logger.getLogger(ConcurrentSftpFileDownloader.class);

    private final SftpDetailsVO sftpDetails;

    private final SftpConnectionBean sftpConnectionBean;

    private final IService service;

    private final TransactionManagerVO downloadFileTxnVo;

    private final TransactionHandler transactionHandler;

    private final SftpFilePathMappingVO filePathMapping;

    protected final SftpHandler sftpHandler;
    private final IMeteringHandler meteringHandler;

    public ConcurrentSftpFileDownloader(SftpDetailsVO sftpDetails, SftpConnectionBean sftpConnectionBean,
                                        IService service, TransactionManagerVO downloadFileTxnVo,
                                        TransactionHandler transactionHandler, SftpFilePathMappingVO filePathMapping,
                                        SftpHandler sftpHandler, IMeteringHandler meteringHandler) {
        this.sftpDetails = sftpDetails;
        this.sftpConnectionBean = sftpConnectionBean;
        this.service = service;
        this.downloadFileTxnVo = downloadFileTxnVo;
        this.transactionHandler = transactionHandler;
        this.filePathMapping = filePathMapping;
        this.sftpHandler = sftpHandler;
        this.meteringHandler = meteringHandler;
    }

    @Override
    public Boolean call() throws PdfReaderException{
        return downloadInputFiles();
    }

    /**
     * <AUTHOR> Nagare
     * @since 20/06/2023
     *
     * This method will download all the files from the input folder and store on either S3 or on local storage based on the configuration
     */
    private boolean downloadInputFiles() throws PdfReaderException{
        LOG.info("START >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> downloadInputFiles >> PAN >> "
            +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp());

        try{
            List<String> files = sftpHandler.readDirectory(sftpConnectionBean.getSftpChannel(), filePathMapping.getInFilePath(), sftpDetails);
            if(!files.isEmpty()) {
                LOG.debug("INTERMEDIATE >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> downloadInputFiles >> PAN >> "
                    +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> NEW_FILES_COUNT >> "+files.size());
                for (String fileName : files) {
                    String sftpFilePath = filePathMapping.getInFilePath() + File.separator + fileName;
                    String panNumber = sftpDetails.getPan();
                    String destFolderPath = FileHelper.getLocalFolderPath(panNumber, SftpOperations.DOWNLOAD_FILE.getValue(),
                            filePathMapping.getFileType(), true);
                    String destFilePath = destFolderPath + File.separator + fileName;

                    FileHelper.checkAndCreateDirectory(destFolderPath);

                    LOG.debug("INTERMEDIATE >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> downloadInputFiles >> PAN >> "
                        +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" FILE_PATH >> "+destFilePath);

                    //Download file from SFTP to server
                    sftpHandler.downloadFile(sftpConnectionBean.getSftpChannel(), sftpFilePath, destFilePath, sftpDetails);

                    //Move the file to archive folder in SFTP
                    moveFileToArchiveFolder(sftpFilePath);

                    //Saving file to Amazon S3
                    checkAndSaveFileToAmazonS3(destFilePath, fileName);

                    //Create file upload details and transaction manager entry for the PDF parsing
                    createFileUploadDtlsAndTxnEntry(destFilePath, fileName);
                }
            }
        }catch (Exception e){
            LOG.error("ERROR >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> downloadInputFiles >> PAN >> "
                +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> ", e);
            throw new PdfReaderException(e);
        }finally {
            sftpHandler.closeSftpConnection(sftpConnectionBean);
        }

        LOG.info("END >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> downloadInputFiles >> PAN >> "
            +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp());

        return true;
    }

    private void moveFileToArchiveFolder(String sftpFilePath) throws PdfReaderException{
        LOG.debug("START >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> moveFileToArchiveFolder >> PAN >> "
            +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> FILE_PATH >> "+sftpFilePath);

        String [] strArr = filePathMapping.getInFilePath().split("/");
        String inFilePath = filePathMapping.getInFilePath().split("/")[strArr.length - 1];
        String archiveFolderPath = sftpFilePath.replace(inFilePath, "archive");
        sftpHandler.moveFile(sftpConnectionBean.getSftpChannel(), sftpFilePath, archiveFolderPath, sftpDetails);

        LOG.debug("END >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> moveFileToArchiveFolder >> PAN >> "
            +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> FILE_PATH >> "+sftpFilePath);
    }

    private void checkAndSaveFileToAmazonS3(String completeFilePath, String fileNameWithExtension) throws PdfReaderException{
        LOG.debug("START >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> checkAndSaveFileToAmazonS3 >> PAN >> "
                +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> FILE_PATH >> "+completeFilePath);

        //Upload the Downloaded file to AMAZON S3
        if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
            AmazonS3StorageUtil.uploadFileToAmazonS3Storage(FileHelper.getDynamicS3FilePath(sftpDetails.getPan(),
                filePathMapping.getFileType(), true), completeFilePath, fileNameWithExtension);
        }

        //Remove the local copy of downloaded from the server
        if (!AppConstants.LOCAL_STORAGE.equals(PdfReaderConfig.getInstance().getStorageAccountType())
                && PdfReaderConfig.getInstance().isRemoveInvalidDataFiles()) {
            FileHelper.removeLocalCopy(Collections.singletonList(completeFilePath));
        }

        LOG.debug("END >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> checkAndSaveFileToAmazonS3 >> PAN >> "
                +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> FILE_PATH >> "+completeFilePath);
    }

    private void createFileUploadDtlsAndTxnEntry(String completeFilePath, String fileNameWithExtension) throws PdfReaderException {
        LOG.debug("START >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> createFileUploadDtlsAndTxnEntry >> PAN >> "
                +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> FILE_PATH >> "+completeFilePath);
        //Create the File Upload Details entry
        FileUploadDetailsVO fileUploadDetails = new FileUploadDetailsVO(sftpDetails.getPan(), fileNameWithExtension, fileNameWithExtension,
            downloadFileTxnVo.getTxnId(), filePathMapping.getFileType(),
            FileProcessingStatus.UPLOADED.name(), completeFilePath, FileSources.SFTP.name());
        fileUploadDetails.setSubTxnId(UUID.randomUUID().toString());
        fileUploadDetails.setCreatedAt(new Date());
        fileUploadDetails.setUpdatedAt(new Date());

        service.addUserDetails(fileUploadDetails);

        fileUploadDetails = service.saveFileUploadDetailsVo(fileUploadDetails);

        //Create Transaction Manager Entry.
        transactionHandler.addEntryInTransactionManager(fileUploadDetails.getTxnId(), sftpDetails.getPan(),
            TransactionType.SFTP_PDF_FILE_PARSING.name(), fileUploadDetails.getSubTxnId(),
            TransactionStatus.UPLOADED.name());

        try {
            meteringHandler.addOrUpdateMeteringDetails(sftpDetails.getPan(),
                    PdfFileType.SHIPPING_BILL.name().equals(filePathMapping.getFileType()) ?
                            MeteringEvents.SB_SFTP_FILE_UPLD.name() : MeteringEvents.BOE_SFTP_FILE_UPLD.name(),
                    MeteringEntities.FILE_COUNT.getMeteringEntityKey(), 1, 0, 0, null);
        }catch (SubscriptionException e){
            LOG.error("ERROR >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> createFileUploadDtlsAndTxnEntry >> PAN >> "
                    +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> FILE_PATH >> "+completeFilePath +" >> " + e.getErrorMessge());
            throw new PdfReaderException(e.getErrorCode(), e.getErrorMessge());
        }

        LOG.debug("START >> CLASS >> ConcurrentSftpFileDownloader >> METHOD >> createFileUploadDtlsAndTxnEntry >> PAN >> "
                +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> FILE_PATH >> "+completeFilePath);
    }
}
