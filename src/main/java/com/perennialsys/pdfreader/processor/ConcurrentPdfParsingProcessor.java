package com.perennialsys.pdfreader.processor;

import com.perennialsys.pdfreader.constants.MeteringEntities;
import com.perennialsys.pdfreader.constants.MeteringEvents;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.db.mongo.MongoDao;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.enums.TransactionType;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.impl.TransactionHandler;
import com.perennialsys.pdfreader.pdfParser.BillOfEntryIPdfParser;
import com.perennialsys.pdfreader.pdfParser.ShippingBillIPdfParser;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.subscriptionService.handler.IMeteringHandler;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import org.apache.log4j.Logger;
import org.json.JSONObject;

import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;

public class ConcurrentPdfParsingProcessor implements Callable<Boolean> {

    private final List<FileUploadDetailsVO> fileUploadDetailsVoList;

    private final IService service;

    private final MongoDao mongoDao;

    private final TransactionHandler txnHandler;
    private final IMeteringHandler meteringHandler;

    public ConcurrentPdfParsingProcessor(List<FileUploadDetailsVO> fileUploadDetailsVoList, IService service,
                                         MongoDao mongoDao, TransactionHandler txnHandler, IMeteringHandler meteringHandler){
        this.fileUploadDetailsVoList = fileUploadDetailsVoList;
        this.service = service;
        this.mongoDao = mongoDao;
        this.txnHandler = txnHandler;
        this.meteringHandler = meteringHandler;
    }


    private static final Logger LOG = Logger.getLogger(ConcurrentPdfParsingProcessor.class);

    public Boolean call() throws PdfReaderException{
        LOG.info("START >> ConcurrentPdfParsingProcessor >> PAN >> " + fileUploadDetailsVoList.get(0).getPan()+ " >> FILE_COUNT >> "+fileUploadDetailsVoList.size());

        synchronized (ConcurrentPdfParsingProcessor.class) {
            for (FileUploadDetailsVO fileUploadDetailsVO : fileUploadDetailsVoList) {
                fileUploadDetailsVO.setStatus(TransactionStatus.PDF_PARSING_IN_PROGRESS.name());
                fileUploadDetailsVO.setUpdatedAt(new Date());

                txnHandler.checkAndUpdateTxnStatusForFileParsing(fileUploadDetailsVO.getTxnId(),
                        TransactionType.PDF_FILE_PARSING.name(), fileUploadDetailsVO.getPan(),
                        TransactionStatus.PDF_PARSING_IN_PROGRESS.name(), null);
            }

            service.getFileUploadDetailsRepo().saveAll(fileUploadDetailsVoList);
        }

        long totalSbSuccess = 0;
        long totalSbFailed = 0;
        long totalBoeSuccess = 0;
        long totalBoeFailed = 0;
        long totalSbPagesParsed = 0;
        long totalBoePagesParsed = 0;

        for (FileUploadDetailsVO fileUploadDetailsVo : fileUploadDetailsVoList) {
            LOG.info("INTERMEDIATE_START >> ConcurrentPdfParsingProcessor >> TXN_ID >> " + fileUploadDetailsVo.getTxnId() +
                    " >> SUB_TXN_ID >> "+fileUploadDetailsVo.getSubTxnId()+" >> PAN >> " + fileUploadDetailsVo.getPan());
            try {
                switch (PdfFileType.valueOf(fileUploadDetailsVo.getFileType())) {
                    case SHIPPING_BILL: {
                        totalSbPagesParsed += new ShippingBillIPdfParser(service, mongoDao).parsePdf(fileUploadDetailsVo, false);
                        break;
                    }
                    case BILL_OF_ENTRY: {
                        totalBoePagesParsed += new BillOfEntryIPdfParser(service, mongoDao).parsePdf(fileUploadDetailsVo, false);
                        break;
                    }
                    default: {
                        break;
                    }
                }

                fileUploadDetailsVo.setStatus(TransactionStatus.PDF_PARSING_COMPLETE.name());
                synchronized (service) {
                    service.saveOrUpdateFileUploadDetails(fileUploadDetailsVo);
                    txnHandler.checkAndUpdateTxnStatusForFileParsing(fileUploadDetailsVo.getTxnId(),
                            TransactionType.PDF_FILE_PARSING.name(), fileUploadDetailsVo.getPan(),
                            TransactionStatus.PDF_PARSING_COMPLETE.name(), null);

                    if(PdfFileType.SHIPPING_BILL.name().equals(fileUploadDetailsVo.getFileType())){
                        totalSbSuccess ++;
                    }else {
                        totalBoeSuccess ++;
                    }

                }
            } catch (PdfReaderException e) {
                LOG.error("ERROR >> ConcurrentPdfParsingProcessor >> TXN_ID >> " + fileUploadDetailsVo.getTxnId() +
                        " >> SUB_TXN_ID >> "+fileUploadDetailsVo.getSubTxnId()+" >> PAN >> " + fileUploadDetailsVo.getPan(), e);
                synchronized (ConcurrentPdfParsingProcessor.class) {
                    JSONObject errorJson = new JSONObject();
                    errorJson.put("error-message", e.getErrorMessge());
                    errorJson.put("error-display-message", e.getErrorMessge());

                    txnHandler.checkAndUpdateTxnStatusForFileParsing(fileUploadDetailsVo.getTxnId(),
                            TransactionType.PDF_FILE_PARSING.name(), fileUploadDetailsVo.getPan(),
                            TransactionStatus.PDF_PARSING_FAILED.name(), errorJson.toString());
                    fileUploadDetailsVo.setStatus(TransactionStatus.PDF_PARSING_FAILED.name());
                    service.saveOrUpdateFileUploadDetails(fileUploadDetailsVo);
                }

                if(PdfFileType.SHIPPING_BILL.name().equals(fileUploadDetailsVo.getFileType())){
                    totalSbFailed ++;
                }else {
                    totalBoeFailed ++;
                }
            } catch (Exception e) {
                JSONObject errorJson = new JSONObject();
                errorJson.put("error-message", e.getMessage());
                errorJson.put("error-display-message", ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);

                LOG.error("ERROR >> ConcurrentPdfParsingProcessor >> TXN_ID >> " + fileUploadDetailsVo.getTxnId() +
                        " >> SUB_TXN_ID >> "+fileUploadDetailsVo.getSubTxnId()+" >> PAN >> " + fileUploadDetailsVo.getPan(), e);
                synchronized (ConcurrentPdfParsingProcessor.class) {
                    txnHandler.checkAndUpdateTxnStatusForFileParsing(fileUploadDetailsVo.getTxnId(),
                            TransactionType.PDF_FILE_PARSING.name(), fileUploadDetailsVo.getPan(),
                            TransactionStatus.PDF_PARSING_FAILED.name(), errorJson.toString());
                    fileUploadDetailsVo.setStatus(TransactionStatus.PDF_PARSING_FAILED.name());
                    service.saveOrUpdateFileUploadDetails(fileUploadDetailsVo);
                }

                if(PdfFileType.SHIPPING_BILL.name().equals(fileUploadDetailsVo.getFileType())){
                    totalSbFailed ++;
                }else {
                    totalBoeFailed ++;
                }
            }
            LOG.info("INTERMEDIATE_END >> ConcurrentPdfParsingProcessor >> TXN_ID >> " + fileUploadDetailsVo.getTxnId() +
                    " >> SUB_TXN_ID >> "+fileUploadDetailsVo.getSubTxnId()+" >> PAN >> " + fileUploadDetailsVo.getPan());
        }

        try {
            if(totalSbSuccess > 0 || totalSbFailed > 0) {
                meteringHandler.addOrUpdateMeteringDetails(fileUploadDetailsVoList.get(0).getPan(),
                        MeteringEvents.SB_FILE_UPLD.name(), MeteringEntities.FILE_COUNT.getMeteringEntityKey(), 0,
                        totalSbSuccess, totalSbFailed, null);
            }
            if(totalSbPagesParsed > 0) {
                meteringHandler.addOrUpdateMeteringDetails(fileUploadDetailsVoList.get(0).getPan(),
                        MeteringEvents.SB_FILE_UPLD.name(), MeteringEntities.PAGE_COUNT.getMeteringEntityKey(),
                        totalSbPagesParsed, 0, 0, null);
            }
            if(totalBoeSuccess > 0 || totalBoeFailed > 0) {
                meteringHandler.addOrUpdateMeteringDetails(fileUploadDetailsVoList.get(0).getPan(),
                        MeteringEvents.BOE_FILE_UPLD.name(), MeteringEntities.FILE_COUNT.getMeteringEntityKey(), 0,
                        totalBoeSuccess, totalBoeFailed, null);
            }
            if(totalBoePagesParsed > 0) {
                meteringHandler.addOrUpdateMeteringDetails(fileUploadDetailsVoList.get(0).getPan(),
                        MeteringEvents.BOE_FILE_UPLD.name(), MeteringEntities.PAGE_COUNT.getMeteringEntityKey(),
                        totalBoePagesParsed, 0, 0, null);
            }
        }catch (SubscriptionException e){
            LOG.error("ERROR >> ConcurrentPdfParsingProcessor >> TXN_ID >> " + fileUploadDetailsVoList.get(0).getTxnId() +
                    " >> SUB_TXN_ID >> "+fileUploadDetailsVoList.get(0).getSubTxnId()+" >> PAN >> " + fileUploadDetailsVoList.get(0).getPan());
        }

        LOG.info("END >> ConcurrentPdfParsingProcessor >> PAN >> " + fileUploadDetailsVoList.get(0).getPan()
                + " >> FILE_COUNT >> "+fileUploadDetailsVoList.size());
        return true;
    }

}
