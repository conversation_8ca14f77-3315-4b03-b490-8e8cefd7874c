package com.perennialsys.pdfreader.processor.excel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * The Class SheetCell. This class is represent to sheet cell in excel sheet
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SheetCell {
    /**
     * The value.
     */
    private Object value;

    /**
     * The old value.
     */
    private Object oldValue;

    /**
     * The is valid.
     */
    private boolean isValid;

    /**
     * The message.
     */
    private String message;

    /**
     * Instantiates a new sheet cell.
     */
    public SheetCell() {
        super();
    }

    /**
     * Instantiates a new sheet cell.
     *
     * @param value the value
     */
    public SheetCell(Object value) {
        super();
        this.value = value;
        isValid = true;
        message = null;
    }

    /**
     * Mark valid.
     */
    public void markValid() {
        isValid = true;
        message = null;
    }

    /**
     * Mark invalid.
     *
     * @param message the message
     */
    public void markInvalid(String message) {
        isValid = false;
        this.message = message;
    }

    /**
     * Gets the value.
     *
     * @return the value
     */
    public Object getValue() {
        return value;
    }

    /**
     * Sets the value.
     *
     * @param value the new value
     */
    public void setValue(Object value) {
        this.value = value;
    }

    /**
     * Checks if is valid.
     *
     * @return true, if is valid
     */
    public boolean isValid() {
        return isValid;
    }

    /**
     * Sets the valid.
     *
     * @param isValid the new valid
     */
    public void setValid(boolean isValid) {
        this.isValid = isValid;
    }

    /**
     * Gets the message.
     *
     * @return the message
     */
    public String getMessage() {
        return message;
    }

    /**
     * Sets the message.
     *
     * @param message the new message
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * Gets the old value.
     *
     * @return the old value
     */
    public Object getOldValue() {
        return oldValue;
    }

    /**
     * Sets the old value.
     *
     * @param oldValue the new old value
     */
    public void setOldValue(Object oldValue) {
        this.oldValue = oldValue;
    }
}
