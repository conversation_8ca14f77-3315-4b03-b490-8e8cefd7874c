package com.perennialsys.pdfreader.processor.excel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.poi.ss.usermodel.CellType;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SheetData {
    /**
     * The file name.
     */
    private String fileName;

    /**
     * The mapping entity.
     */
    private String mappingEntity;

    /**
     * The headers.
     */
    private SheetHeader[] headers = null;

    /**
     * The offset row count.
     */
    private int offsetRowCount = 0;

    /**
     * The parse row count.
     */
    private int parseRowCount = 0;

    /**
     * The rows.
     */
    @JsonIgnore
    private List<SheetRow> rows = new ArrayList<>();

    /**
     * save valid rows to db.
     */
    @JsonIgnore
    private List<SheetRow> validRows = new ArrayList<>();

    /**
     * send back invalid rows to user for modification.
     */
    private List<SheetRow> invalidRows = new ArrayList<>();

    /**
     * The properties.
     */
    @JsonIgnore
    private Map<String, Object> properties = new HashMap<>();

    /**
     * The key index map.
     */
    @JsonIgnore
    private Map<String, Integer> keyIndexMap = null;

    /**
     * The key data type map.
     */
    @JsonIgnore
    private Map<String, CellType> keyDataTypeMap = null;

    /**
     * Instantiates a new sheet data.
     */
    public SheetData() {
        super();
    }

    /**
     * Instantiates a new sheet data.
     *
     * @param fileName      the file name
     * @param mappingEntity the mapping entity
     * @param headers       the headers
     */
    public SheetData(String fileName, String mappingEntity, SheetHeader[] headers) {
        this.fileName = fileName;
        this.mappingEntity = mappingEntity;
        this.headers = headers;
    }

    /**
     * implementation of custom sheet data functions.
     *
     * @return the headers
     */
    public SheetHeader[] getHeaders() {
        return headers;
    }

    /**
     * Gets the rows.
     *
     * @return the rows
     */
    public List<SheetRow> getRows() {
        if (rows == null) {
            rows = new ArrayList<>();
        }
        return rows;
    }

    /**
     * Sets the rows.
     *
     * @param rows the new rows
     */
    public void setRows(List<SheetRow> rows) {
        this.rows = rows;
    }

    /**
     * Adds the property.
     *
     * @param key   the key
     * @param value the value
     */
    public void addProperty(String key, Object value) {
        properties.put(key, value);
    }

    /**
     * Gets the property.
     *
     * @param key the key
     * @return the property
     */
    public Object getProperty(String key) {
        return properties.get(key);
    }

    /**
     * Gets the row at.
     *
     * @param index the index
     * @return the row at
     */
    public SheetRow getRowAt(int index) {
        return rows.get(index);
    }

    /**
     * Gets the file name.
     *
     * @return the file name
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * Gets the mapping entity.
     *
     * @return the mapping entity
     */
    public String getMappingEntity() {
        return mappingEntity;
    }

    /**
     * Gets the key index map.
     *
     * @return the key index map
     */
    public Map<String, Integer> getKeyIndexMap() {
        if (keyIndexMap != null) {
            return keyIndexMap;
        }
        keyIndexMap = new HashMap<>();
        for (int i = 0; i < headers.length; i++) {
            keyIndexMap.put(headers[i].getMappingKey(), i);
        }
        return keyIndexMap;
    }

    /**
     * Gets the key data type map.
     *
     * @return the key data type map
     */
    public Map<String, CellType> getKeyDataTypeMap() {
        if (keyDataTypeMap != null) {
            return keyDataTypeMap;
        }
        keyDataTypeMap = new HashMap<String, CellType>();
        for (int i = 0; i < headers.length; i++) {
            keyDataTypeMap.put(headers[i].getMappingKey(), headers[i].getDataType());
        }
        return keyDataTypeMap;
    }

    /**
     * Gets the valid rows.
     *
     * @return the valid rows
     */
    public List<SheetRow> getValidRows() {
        if (validRows == null) {
            validRows = new ArrayList<>();
        }
        return validRows;
    }

    public void setValidRows(List<SheetRow> validRows) {
        validRows = validRows;
    }

    /**
     * Gets the invalid rows.
     *
     * @return the invalid rows
     */
    public List<SheetRow> getInvalidRows() {
        if (invalidRows == null) {
            invalidRows = new ArrayList<>();
        }
        return invalidRows;
    }

    /**
     * Sets the invalid rows.
     *
     * @param invalidRows the new invalid rows
     */
    public void setInvalidRows(List<SheetRow> invalidRows) {
        this.invalidRows = invalidRows;
    }

    /**
     * Adds the valid row.
     *
     * @param sheetRow the sheet row
     * @return true, if successful
     */
    public boolean addValidRow(SheetRow sheetRow) {
        if (validRows == null) {
            validRows = new ArrayList<>();
        }
        return validRows.add(sheetRow);
    }

    /**
     * Adds the invalid row.
     *
     * @param sheetRow the sheet row
     * @return true, if successful
     */
    public boolean addInvalidRow(SheetRow sheetRow) {
        if (invalidRows == null) {
            invalidRows = new ArrayList<>();
        }
        return invalidRows.add(sheetRow);
    }

    /**
     * Gets the properties.
     *
     * @return the properties
     */
    public Map<String, Object> getProperties() {
        return properties;
    }

    /**
     * Sets the properties.
     *
     * @param properties the properties
     */
    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    /**
     * Gets the offset row count.
     *
     * @return the offset row count
     */
    public int getOffsetRowCount() {
        return offsetRowCount;
    }

    /**
     * Sets the offset row count.
     *
     * @param offsetRowCount the new offset row count
     */
    public void setOffsetRowCount(int offsetRowCount) {
        this.offsetRowCount = offsetRowCount;
    }

    /**
     * Gets the parses the row count.
     *
     * @return the parses the row count
     */
    public int getParseRowCount() {
        return parseRowCount;
    }

    /**
     * Sets the parses the row count.
     *
     * @param parseRowCount the new parses the row count
     */
    public void setParseRowCount(int parseRowCount) {
        this.parseRowCount = parseRowCount;
    }
}
