package com.perennialsys.pdfreader.processor.excel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.codehaus.jackson.annotate.JsonIgnore;

/**
 * The Class SheetRow. this class represent excel file sheet row.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SheetRow {
    /**
     * The row id.
     */
    private int rowId;

    /**
     * The cells.
     */
    private SheetCell[] cells = null;

    /**
     * The compare idx arr.
     */
    @JsonIgnore
    private int[] compareIdxArr;

    @com.fasterxml.jackson.annotation.JsonIgnore
    private int rowIndex;

    /**
     * Instantiates a new sheet row.
     */
    public SheetRow() {
        super();
    }

    /**
     * Instantiates a new sheet row.
     *
     * @param rowId the row id
     * @param size  the size
     */
    public SheetRow(int rowId, int size) {
        super();
        this.rowId = rowId;
        cells = new SheetCell[size];
    }

    /**
     * Gets the row id.
     *
     * @return the row id
     */
    public int getRowId() {
        return rowId;
    }

    /**
     * Sets the row id.
     *
     * @param rowId the new row id
     */
    public void setRowId(int rowId) {
        this.rowId = rowId;
    }

    /**
     * Gets the cells.
     *
     * @return the cells
     */
    public SheetCell[] getCells() {
        return cells;
    }

    /**
     * Gets the data at.
     *
     * @param index the index
     * @return the data at
     */
    public SheetCell getDataAt(int index) {
        return cells[index];
    }

    /**
     * Adds the data at.
     *
     * @param index the index
     * @param cell  the cell
     */
    public void addDataAt(int index, SheetCell cell) {
        cells[index] = cell;
    }

    /**
     * Gets the value at.
     *
     * @param index the index
     * @return the value at
     */
    public Object getValueAt(int index) {
        return cells[index] == null ? null : cells[index].getValue();
    }

    public SheetCell getCellAt(int index) {
        return cells[index];
    }

    /**
     * Gets the old value at.
     *
     * @param index the index
     * @return the old value at
     */
    public Object getOldValueAt(int index) {
        return cells[index] == null ? null : cells[index].getOldValue();
    }

    /**
     * Gets the compare idx arr.
     *
     * @return the compare idx arr
     */
    public int[] getCompareIdxArr() {
        return compareIdxArr;
    }

    /**
     * Sets the compare idx arr.
     *
     * @param compareIdxArr the new compare idx arr
     */
    public void setCompareIdxArr(int[] compareIdxArr) {
        this.compareIdxArr = compareIdxArr;
    }


    public void setCells(SheetCell[] cells) {
        this.cells = cells;
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
    }
}
