package com.perennialsys.pdfreader.processor.excel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.poi.ss.usermodel.CellType;
/**
 * <AUTHOR>
 */

/**
 * The Class SheetHeader. This class is used to maintain excel sheet meta data,
 * rules, mapping keys.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Builder
@Data
public class SheetHeader {
    /**
     * The display name.
     */
    private String displayName;

    /**
     * The mapping key.
     */
    private String mappingKey;

    /**
     * The data type.
     */
    private CellType dataType;

    /**
     * The required.
     */
    private boolean required;

    /**
     * The visible.
     */
    private boolean visible;

    /**
     * The file index.
     */
    private int fileIndex;

    /**
     * The rules.
     */
//    private Set<EInvoiceRuleVO> rules;

    /**
     * The row index.
     */
    private int rowIndex;

    /**
     * The col index.
     */
    private int colIndex;

    /**
     * The tot row cnt.
     */
    private int totRowCnt;

    /**
     * Instantiates a new sheet header.
     */
    public SheetHeader() {
        super();
    }

    /**
     * Instantiates a new sheet header.
     *
     * @param mappingKey  the mapping key
     * @param dataType    the data type
     * @param required    the required
     * @param displayName the display name
     * @param fileIndex   the file index
     * @param visible     the visible
     */

    public SheetHeader(String mappingKey, CellType dataType, boolean required, String displayName, int fileIndex,
                       boolean visible) {
        this.mappingKey = mappingKey;
        this.dataType = dataType;
        this.required = required;
        this.fileIndex = fileIndex;
        this.displayName = displayName;
        this.visible = visible;
    }
}
