package com.perennialsys.pdfreader.processor;

import com.perennialsys.pdfreader.bean.SftpConnectionBean;
import com.perennialsys.pdfreader.constants.MeteringEntities;
import com.perennialsys.pdfreader.constants.MeteringEvents;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.db.mongo.MongoDao;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.enums.TransactionType;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.helper.FileHelper;
import com.perennialsys.pdfreader.helper.SftpHandler;
import com.perennialsys.pdfreader.pdfParser.BillOfEntryIPdfParser;
import com.perennialsys.pdfreader.pdfParser.ShippingBillIPdfParser;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.subscriptionService.handler.IMeteringHandler;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import com.perennialsys.pdfreader.vo.SftpDetailsVO;
import com.perennialsys.pdfreader.vo.SftpFilePathMappingVO;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import org.apache.commons.io.FilenameUtils;
import org.apache.log4j.Logger;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class ConcurrentSftpPdfParsingProcessor implements Runnable {

    private final List<TransactionManagerVO> panFileDnldTxnManagerList;

    private final IService service;

    private final MongoDao mongoDao;

    private final ExportReportProcessor exportReportProcessor;

    private final SftpHandler sftpHandler;

    private SftpDetailsVO sftpDetails;
    private final IMeteringHandler meteringHandler;

    public ConcurrentSftpPdfParsingProcessor(List<TransactionManagerVO> panFileDnldTxnManagerList, IService service,
                                             MongoDao mongoDao, ExportReportProcessor exportReportProcessor,
                                             SftpHandler sftpHandler, IMeteringHandler meteringHandler) {
        this.panFileDnldTxnManagerList = panFileDnldTxnManagerList;
        this.service = service;
        this.mongoDao = mongoDao;
        this.exportReportProcessor = exportReportProcessor;
        this.sftpHandler = sftpHandler;
        this.meteringHandler = meteringHandler;
    }

    private static final Logger LOG = Logger.getLogger(ConcurrentSftpPdfParsingProcessor.class);

    public void run(){
        for (TransactionManagerVO fileDnldTxnManager : panFileDnldTxnManagerList) {
            LOG.info("START >> ConcurrentSftpPdfParsingProcessor >> TXN_ID >> " + fileDnldTxnManager.getTxnId() + " >> PAN >> "
                    + fileDnldTxnManager.getPan());

            List<TransactionManagerVO> fileParsingTxnList = service.getTransactionManagerRepo()
                    .findAllByTxnIdAndPanAndType(fileDnldTxnManager.getTxnId(), fileDnldTxnManager.getPan(),
                            TransactionType.SFTP_PDF_FILE_PARSING.name());
            boolean parsingResult = true;

            if(null != fileParsingTxnList && !fileParsingTxnList.isEmpty()){

                synchronized (service) {
                    service.updateTransactionManger(fileDnldTxnManager, TransactionStatus.PDF_PARSING_IN_PROGRESS.name());
                }

                long totalSbSuccess = 0;
                long totalSbFailed = 0;
                long totalBoeSuccess = 0;
                long totalBoeFailed = 0;
                long totalSbPagesParsed = 0;
                long totalBoePagesParsed = 0;

                for (TransactionManagerVO transactionManagerVO : fileParsingTxnList) {
                    FileUploadDetailsVO fileUploadDetailsVo = service.getFileUploadDetailsRepo()
                            .findFirstByPanAndTxnIdAndSubTxnId(transactionManagerVO.getPan(),
                                    transactionManagerVO.getTxnId(), transactionManagerVO.getSubTxnId());

                    try {

                        if(null == sftpDetails) {
                            sftpDetails = service.getSftpDetailsRepo().findByPanAndIsActive(fileUploadDetailsVo.getPan(), true);

                            if (null == sftpDetails) {
                                throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.SFTP_DETAILS_NOT_FOUND);
                            }

                            if (null == sftpDetails.getPathMappingList() || sftpDetails.getPathMappingList().isEmpty()) {
                                throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.SFTP_PATH_DETAILS_NOT_FOUNE);
                            }
                        }

                        fileUploadDetailsVo.setStatus(TransactionStatus.PDF_PARSING_IN_PROGRESS.name());

                        synchronized (service) {
                            service.updateTransactionManger(transactionManagerVO, TransactionStatus.PDF_PARSING_IN_PROGRESS.name());
                            fileUploadDetailsVo = service.saveOrUpdateFileUploadDetails(fileUploadDetailsVo);
                        }

                        switch (PdfFileType.valueOf(fileUploadDetailsVo.getFileType())) {
                            case SHIPPING_BILL: {
                                totalSbPagesParsed += new ShippingBillIPdfParser(service, mongoDao).parsePdf(fileUploadDetailsVo, true);
                                break;
                            }
                            case BILL_OF_ENTRY: {
                                totalBoePagesParsed += new BillOfEntryIPdfParser(service, mongoDao).parsePdf(fileUploadDetailsVo, true);
                                break;
                            }
                            default: {
                                break;
                            }
                        }

                        fileUploadDetailsVo.setStatus(TransactionStatus.PDF_PARSING_COMPLETE.name());

                        synchronized (service) {
                            service.updateTransactionManger(transactionManagerVO, TransactionStatus.PDF_PARSING_COMPLETE.name());
                            service.saveOrUpdateFileUploadDetails(fileUploadDetailsVo);

                            if(PdfFileType.SHIPPING_BILL.name().equals(fileUploadDetailsVo.getFileType())){
                                totalSbSuccess ++;
                            }else {
                                totalBoeSuccess ++;
                            }
                        }

                        exportReportToSftpOutFolder(fileUploadDetailsVo, transactionManagerVO);

                    } catch (PdfReaderException e) {
                        LOG.error("ERROR >> ConcurrentSftpPdfParsingProcessor >> TXN_ID >> " + fileDnldTxnManager.getTxnId()
                            + " >> PAN >> " + fileDnldTxnManager.getPan(), e);
                        synchronized (service) {
                            transactionManagerVO.setOtherDetails(e.getErrorMessge());
                            service.updateTransactionManger(transactionManagerVO, TransactionStatus.PDF_PARSING_FAILED.name());
                            fileUploadDetailsVo.setStatus(TransactionStatus.PDF_PARSING_FAILED.name());
                            service.saveOrUpdateFileUploadDetails(fileUploadDetailsVo);
                        }
                        parsingResult = false;

                        if(PdfFileType.SHIPPING_BILL.name().equals(fileUploadDetailsVo.getFileType())){
                            totalSbFailed ++;
                        }else {
                            totalBoeFailed ++;
                        }
                    } catch (Exception e) {
                        LOG.error("ERROR >> ConcurrentSftpPdfParsingProcessor >> TXN_ID >> " + fileDnldTxnManager.getTxnId()
                            + " >> PAN >> " + fileDnldTxnManager.getPan(), e);
                        synchronized (service) {
                            transactionManagerVO.setOtherDetails(e.getLocalizedMessage());
                            service.updateTransactionManger(transactionManagerVO, TransactionStatus.PDF_PARSING_FAILED.name());
                            fileUploadDetailsVo.setStatus(TransactionStatus.PDF_PARSING_FAILED.name());
                            service.saveOrUpdateFileUploadDetails(fileUploadDetailsVo);
                        }
                        parsingResult = false;

                        if(PdfFileType.SHIPPING_BILL.name().equals(fileUploadDetailsVo.getFileType())){
                            totalSbFailed ++;
                        }else {
                            totalBoeFailed ++;
                        }
                    }
                }

                service.updateTransactionManger(fileDnldTxnManager, parsingResult ?
                        TransactionStatus.PDF_PARSING_COMPLETE.name() : TransactionStatus.PDF_PARSING_FAILED.name());

                try {
                    if(totalSbSuccess > 0 || totalSbFailed > 0) {
                        meteringHandler.addOrUpdateMeteringDetails(fileDnldTxnManager.getPan(),
                                MeteringEvents.SB_SFTP_FILE_UPLD.name(), MeteringEntities.FILE_COUNT.getMeteringEntityKey(), 0,
                                totalSbSuccess, totalSbFailed, null);
                    }
                    if(totalSbPagesParsed > 0) {
                        meteringHandler.addOrUpdateMeteringDetails(fileDnldTxnManager.getPan(),
                                MeteringEvents.SB_SFTP_FILE_UPLD.name(), MeteringEntities.PAGE_COUNT.getMeteringEntityKey(),
                                totalSbPagesParsed, 0, 0, null);
                    }
                    if(totalBoeSuccess > 0 || totalBoeFailed > 0) {
                        meteringHandler.addOrUpdateMeteringDetails(fileDnldTxnManager.getPan(),
                                MeteringEvents.BOE_SFTP_FILE_UPLD.name(), MeteringEntities.FILE_COUNT.getMeteringEntityKey(), 0,
                                totalBoeSuccess, totalBoeFailed, null);
                    }
                    if(totalBoePagesParsed > 0) {
                        meteringHandler.addOrUpdateMeteringDetails(fileDnldTxnManager.getPan(),
                                MeteringEvents.BOE_SFTP_FILE_UPLD.name(), MeteringEntities.PAGE_COUNT.getMeteringEntityKey(),
                                totalBoePagesParsed, 0, 0, null);
                    }
                }catch (SubscriptionException e){
                    LOG.error("ERROR >> ConcurrentPdfParsingProcessor >> TXN_ID >> " + fileDnldTxnManager.getTxnId() +
                            " >> SUB_TXN_ID >> "+fileDnldTxnManager.getSubTxnId()+" >> PAN >> " + fileDnldTxnManager.getPan());
                }
            }else {
                fileDnldTxnManager.setOtherDetails("No file found for parsing");

                synchronized (service) {
                    service.updateTransactionManger(fileDnldTxnManager, TransactionStatus.PDF_PARSING_FAILED.name());
                }
            }

            LOG.info("END >> ConcurrentSftpPdfParsingProcessor >> TXN_ID >> " + fileDnldTxnManager.getTxnId() + " >> PAN >> "
                    + fileDnldTxnManager.getPan());
        }
    }

    private void exportReportToSftpOutFolder(FileUploadDetailsVO fileUploadDetailsVo, TransactionManagerVO transactionManagerVO)
            throws PdfReaderException{
        LOG.info("START >> ConcurrentSftpPdfParsingProcessor >> METHOD >> exportReportToSftpOutFolder >> TXN_ID >> "
            + fileUploadDetailsVo.getTxnId() + " >> PAN >> " + fileUploadDetailsVo.getPan()+ " >> SUB_TXN_ID >> "
            +fileUploadDetailsVo.getSubTxnId()+" >> FILE_TYPE >> "+ fileUploadDetailsVo.getFileType()+" >> UPLD_FILE_NAME >> "
            +fileUploadDetailsVo.getFileName());

        File excelReport = null;

        synchronized (service) {
            service.updateTransactionManger(transactionManagerVO, TransactionStatus.SFTP_FILE_UPLOAD_IP.name());
        }
        try{
            switch (PdfFileType.valueOf(fileUploadDetailsVo.getFileType())){
                case BILL_OF_ENTRY: {
                    excelReport = exportReportProcessor.exportBoeDetailsRecordFile(fileUploadDetailsVo.getPan(),
                            PdfFileType.BILL_OF_ENTRY, fileUploadDetailsVo.getSubTxnId(), true);
                    break;
                }
                case SHIPPING_BILL: {
                    excelReport = exportReportProcessor.exportSbDetailsRecordFile(fileUploadDetailsVo.getPan(),
                            PdfFileType.SHIPPING_BILL, fileUploadDetailsVo.getSubTxnId(), true);
                    break;
                }
            }

            if(null != excelReport){
                //Upload the Excel report to SFTP out folder
                Optional<SftpFilePathMappingVO> fileMappingOptional = sftpDetails.getPathMappingList()
                    .stream()
                    .filter(pathDetails -> pathDetails.getFileType().equals(fileUploadDetailsVo.getFileType()))
                    .findFirst();

                if(!fileMappingOptional.isPresent()){
                    LOG.error("ERROR >> ConcurrentSftpPdfParsingProcessor >> METHOD >> exportReportToSftpOutFolder >> TXN_ID >> "
                        + fileUploadDetailsVo.getTxnId() + " >> PAN >> " + fileUploadDetailsVo.getPan()+ " >> SUB_TXN_ID >> "
                        +fileUploadDetailsVo.getSubTxnId()+" >> FILE_TYPE >> "+ fileUploadDetailsVo.getFileType()+" >> UPLD_FILE_NAME >> "
                        +fileUploadDetailsVo.getFileName()+" >> "+ResponseMessage.SFTP_PATH_DETAILS_NOT_FOUNE);
                    throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.SFTP_PATH_DETAILS_NOT_FOUNE);
                }

                SftpFilePathMappingVO filePathMappingVO = fileMappingOptional.get();

                SftpConnectionBean connectionBean = sftpHandler.connectToSftp(sftpDetails);
                try{
                    String reportExtension = FilenameUtils.getExtension(excelReport.getName());
                    String sftpFilePath = filePathMappingVO.getOutFilePath() + File.separator +
                        FilenameUtils.removeExtension(fileUploadDetailsVo.getFileName())+"."+reportExtension;
                    sftpHandler.uploadFile(connectionBean.getSftpChannel(),  excelReport.getAbsolutePath(),
                        sftpFilePath, sftpDetails);
                }finally {
                    if(null != connectionBean){
                        sftpHandler.closeSftpConnection(connectionBean);
                    }
                }

                FileHelper.removeLocalCopy(Collections.singletonList(fileUploadDetailsVo.getFileLoc()));
                FileHelper.removeLocalCopy(Collections.singletonList(excelReport.getAbsolutePath()));

                synchronized (service) {
                    service.updateTransactionManger(transactionManagerVO, TransactionStatus.SFTP_FILE_UPLOAD_DONE.name());
                }
            }else {
                LOG.error("ERROR >> ConcurrentSftpPdfParsingProcessor >> METHOD >> exportReportToSftpOutFolder >> TXN_ID >> "
                    + fileUploadDetailsVo.getTxnId() + " >> PAN >> " + fileUploadDetailsVo.getPan()+ " >> SUB_TXN_ID >> "
                    +fileUploadDetailsVo.getSubTxnId()+" >> FILE_TYPE >> "+ fileUploadDetailsVo.getFileType()+" >> UPLD_FILE_NAME >> "
                    +fileUploadDetailsVo.getFileName()+" >> "+ResponseMessage.ERROR_WHILE_EXPORTING_REPORT);
                throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.ERROR_WHILE_EXPORTING_REPORT);
            }
        }catch (Exception e){

            LOG.error("ERROR >> ConcurrentSftpPdfParsingProcessor >> METHOD >> exportReportToSftpOutFolder >> TXN_ID >> "
                + fileUploadDetailsVo.getTxnId() + " >> PAN >> " + fileUploadDetailsVo.getPan()+ " >> SUB_TXN_ID >> "
                +fileUploadDetailsVo.getSubTxnId()+" >> FILE_TYPE >> "+ fileUploadDetailsVo.getFileType()+" >> UPLD_FILE_NAME >> "
                +fileUploadDetailsVo.getFileName()+" >> ", e);

            if(e instanceof PdfReaderException){
                transactionManagerVO.setOtherDetails(e.getMessage());
            }else {
                transactionManagerVO.setOtherDetails(e.getLocalizedMessage());
            }
            synchronized (service) {
                service.updateTransactionManger(transactionManagerVO, TransactionStatus.SFTP_FILE_UPLOAD_DONE.name());
            }
        }

        LOG.info("END >> ConcurrentSftpPdfParsingProcessor >> METHOD >> exportReportToSftpOutFolder >> TXN_ID >> "
            + fileUploadDetailsVo.getTxnId() + " >> PAN >> " + fileUploadDetailsVo.getPan()+ " >> SUB_TXN_ID >> "
            +fileUploadDetailsVo.getSubTxnId()+" >> FILE_TYPE >> "+ fileUploadDetailsVo.getFileType()+" >> UPLD_FILE_NAME >> "
            +fileUploadDetailsVo.getFileName());
    }
}
