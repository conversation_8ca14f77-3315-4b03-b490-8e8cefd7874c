package com.perennialsys.pdfreader.processor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.dto.GenerateReportDetailsDto;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.IReportsHandler;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import org.apache.log4j.Logger;
import org.json.JSONObject;

import java.util.Date;

public class ConcurrentReportGenerationProcessor implements Runnable {

    private static final Logger LOG = Logger.getLogger(ConcurrentReportGenerationProcessor.class);
    private ExportReportsManagerVO exportReportsManagerVO;
    private final IReportsHandler reportsHandler;
    private final IService service;


    public ConcurrentReportGenerationProcessor(ExportReportsManagerVO exportReportsManagerVO,
                                               IService service, IReportsHandler reportsHandler) {
        this.exportReportsManagerVO = exportReportsManagerVO;
        this.service = service;
        this.reportsHandler = reportsHandler;
    }

    @Override
    public void run() {
        LOG.info("START >> ConcurrentReportGenerationProcessor >> PAN >> " + exportReportsManagerVO.getPan() +
                " >> REPORT_TYPE >> " + exportReportsManagerVO.getReportType());

        String status;
        JSONObject errorJson = null;
        GenerateReportDetailsDto generateReportDetailsDto = null;

        synchronized (service) {
            //Set the export report manager VO status as in progress.
            exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name());
            exportReportsManagerVO.setUpdatedAt(new Date());

            service.getReportsManagerRepo().save(exportReportsManagerVO);
        }

        try {
            generateReportDetailsDto = new ObjectMapper()
                    .readValue(exportReportsManagerVO.getOtherDetails(), GenerateReportDetailsDto.class);

            reportsHandler.exportReportForFileTxn(exportReportsManagerVO.getPan(), exportReportsManagerVO.getTxnId(),
                    generateReportDetailsDto.getStartPeriod(), generateReportDetailsDto.getEndPeriod(), false,
                    generateReportDetailsDto.getFileIdList(), generateReportDetailsDto.getDescription(),
                    exportReportsManagerVO.getReportType(), exportReportsManagerVO.getReportType()
            );

            synchronized (service) {
                //Set the export report manager VO status as Completed.
                status = TransactionStatus.REPORT_GENERATION_COMPLETED.name();

                exportReportsManagerVO = service.getReportsManagerRepo().findById(exportReportsManagerVO.getId()).get();
                exportReportsManagerVO.setUpdatedAt(new Date());

                service.getReportsManagerRepo().save(exportReportsManagerVO);
            }
        } catch (PdfReaderException e) {
            LOG.error("START >> ConcurrentReportGenerationProcessor >> PAN >> " + exportReportsManagerVO.getPan() +
                    " >> REPORT_TYPE >> " + exportReportsManagerVO.getReportType(), e);

            errorJson = new JSONObject();
            errorJson.put("error-message", e.getErrorMessge());
            errorJson.put("error-display-message", e.getErrorMessge());

            status = TransactionStatus.REPORT_GENERATION_FAILED.name();
        } catch (Exception e) {
            LOG.error("START >> ConcurrentReportGenerationProcessor >> PAN >> " + exportReportsManagerVO.getPan() +
                    " >> REPORT_TYPE >> " + exportReportsManagerVO.getReportType(), e);

            errorJson = new JSONObject();
            errorJson.put("error-message", e.getMessage());
            errorJson.put("error-display-message", ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);

            status = TransactionStatus.REPORT_GENERATION_FAILED.name();
        }

        synchronized (service) {
            exportReportsManagerVO = service.getReportsManagerRepo().findById(exportReportsManagerVO.getId()).get();

            //Update the Export report manager VO status based on the file generation process status
            exportReportsManagerVO.setStatus(status);
            exportReportsManagerVO.setUpdatedAt(new Date());

            if (!TransactionStatus.REPORT_GENERATION_COMPLETED.name().equals(status)) {
                //Report generation has failed. Add the error details into the export report manager VO.
                if (generateReportDetailsDto == null) {
                    generateReportDetailsDto = new GenerateReportDetailsDto();
                }
                generateReportDetailsDto.setErrorDetails(errorJson.toString());
                exportReportsManagerVO.setOtherDetails(generateReportDetailsDto.toString());
            }

            service.getReportsManagerRepo().save(exportReportsManagerVO);
        }

        LOG.info("END >> ConcurrentReportGenerationProcessor >> PAN >> " + exportReportsManagerVO.getPan() +
                " >> REPORT_TYPE >> " + exportReportsManagerVO.getReportType() + " >> STATUS >> " + status);
    }
}
