package com.perennialsys.pdfreader.processor;

import com.perennialsys.pdfreader.bean.SftpConnectionBean;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.enums.TransactionType;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.impl.TransactionHandler;
import com.perennialsys.pdfreader.helper.SftpHandler;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.subscriptionService.handler.IMeteringHandler;
import com.perennialsys.pdfreader.vo.SftpDetailsVO;
import com.perennialsys.pdfreader.vo.SftpFilePathMappingVO;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <AUTHOR> Nagare
 * @since 20/06/2023
 *
 * This processor will be used to download and upload the files to SFTP
 */
public class ConcurrentSftpFileDownloadProcessor implements Runnable{
    private static final Logger LOG = Logger.getLogger(ConcurrentSftpFileDownloadProcessor.class);

    private final SftpDetailsVO sftpDetails;

    private final IService service;

    private final TransactionHandler transactionHandler;

    protected final SftpHandler sftpHandler;

    private ExecutorService executorService = null;
    private IMeteringHandler meteringHandler;

    public ConcurrentSftpFileDownloadProcessor(SftpDetailsVO sftpDetails, IService service,
                                               TransactionHandler transactionHandler,SftpHandler sftpHandler,
                                               IMeteringHandler meteringHandler) {
        this.sftpDetails = sftpDetails;
        this.service = service;
        this.transactionHandler = transactionHandler;
        this.sftpHandler = sftpHandler;
        this.meteringHandler = meteringHandler;
    }

    @Override
    public void run() {
        checkAndDownloadInputFiles();
    }

    /**
     * <AUTHOR> Nagare
     * @since 20/06/2023
     * </p>
     * This method will check if there are any files in the input file location on SFTP and start the download job for
     * each input file path.
     */
    private void checkAndDownloadInputFiles(){
        LOG.info("START >> CLASS >> SftpJobSchedulerHandler >> METHOD >> checkAndCreateFileDownloadJob >> PAN >> "
                +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp());
        TransactionManagerVO fileDownloadTxnVo = null;
        try{
            List<Future<Boolean>> futureList = new ArrayList<>();
            //Check if the new files are present
            for(SftpFilePathMappingVO filePathMapping : sftpDetails.getPathMappingList()) {

                //Connect to SFTP
                SftpConnectionBean sftpConnectionBean = sftpHandler.connectToSftp(sftpDetails);
                if (null != sftpConnectionBean) {
                    if (sftpHandler.checkIfFilesExist(sftpConnectionBean.getSftpChannel(), filePathMapping.getInFilePath(), sftpDetails)) {
                        //New file exist then create the file download job for the input file path.
                        if (null == fileDownloadTxnVo) {
                            fileDownloadTxnVo = transactionHandler.addEntryInTransactionManager(UUID.randomUUID().toString(),
                                    sftpDetails.getPan(), TransactionType.SFTP_PDF_FILE_PROCESSING.name(),
                                    null, TransactionStatus.SFTP_FILE_DOWNLOAD_IP.name());
                        }

                        futureList.add(getExecutorService().submit(new ConcurrentSftpFileDownloader(sftpDetails, sftpConnectionBean, service,
                                fileDownloadTxnVo, transactionHandler, filePathMapping, sftpHandler, meteringHandler)));
                    } else {
                        LOG.debug("INTERMEDIATE >> CLASS >> SftpJobSchedulerHandler >> METHOD >> checkAndCreateFileDownloadJob >> PAN >> "
                                + sftpDetails.getPan() + " >> HOST_IP >> " + sftpDetails.getHostIp() + " >> IN_DIR_PATH >> "
                                + filePathMapping.getInFilePath() + " >> No New File Found");
                    }
                }
            }
            if(null != executorService){
                executorService.shutdown();
            }

            boolean fileDownloadResult = true;
            if(!futureList.isEmpty()){
                for (Future<Boolean> booleanFuture : futureList) {
                    boolean batchResult = booleanFuture.get();
                    if(fileDownloadResult) {
                        fileDownloadResult = batchResult;
                    }
                }

                fileDownloadTxnVo.setUpdatedAt(new Date());
                fileDownloadTxnVo.setStatus(fileDownloadResult ? TransactionStatus.SFTP_FILE_DOWNLOAD_DONE.name() :
                        TransactionStatus.SFTP_FILE_DOWNLOAD_FAILED.name());
                synchronized (service) {
                    service.getTransactionManagerRepo().save(fileDownloadTxnVo);
                }
            }
        }catch (PdfReaderException | ExecutionException | InterruptedException e){
            LOG.error("ERROR >> CLASS >> SftpJobSchedulerHandler >> METHOD >> checkAndCreateFileDownloadJob >> PAN >> "
                +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp()+" >> ", e);

            if(null != fileDownloadTxnVo) {
                //If transaction manager entry is present then update it with the error details
                fileDownloadTxnVo.setUpdatedAt(new Date());
                fileDownloadTxnVo.setStatus(TransactionStatus.SFTP_FILE_DOWNLOAD_FAILED.name());

                String message;
                if(e instanceof PdfReaderException){
                    message = ((PdfReaderException) e).getErrorMessge();
                }else {
                    message = e.getLocalizedMessage();
                }

                fileDownloadTxnVo.setOtherDetails(message);
                synchronized (service) {
                    service.getTransactionManagerRepo().save(fileDownloadTxnVo);
                }
            }
        }

        LOG.info("END >> CLASS >> SftpJobSchedulerHandler >> METHOD >> checkAndCreateFileDownloadJob >> PAN >> "
                +sftpDetails.getPan()+" >> HOST_IP >> "+sftpDetails.getHostIp());
    }

    private ExecutorService getExecutorService(){
        if(null == executorService){
            executorService = Executors.newFixedThreadPool(10);
        }
        return executorService;
    }
}
