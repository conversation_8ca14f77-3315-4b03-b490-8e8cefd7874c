package com.perennialsys.pdfreader.processor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.db.mongo.MongoDbCursor;
import com.perennialsys.pdfreader.dto.GenerateReportDetailsDto;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.BoeDutyDrawbackDetailsFields;
import com.perennialsys.pdfreader.enums.BoeMoowrDetailsFields;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.enums.SBItemDetailsFields;
import com.perennialsys.pdfreader.enums.TemplateCode;
import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.excel.StreamHelper;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.helper.FileHelper;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.util.AppConfig;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.NumberUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.ExportReportTemplateMappingVO;
import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeAdditionalContainerDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeAdditionalDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeAdditionalSingleWindowConstituentsDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeAdditionalSingleWindowControlDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeAdditionalSingleWindowDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeAdditionalSupportingDocDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeBondDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoePaymentDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeWarehouseDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.SbDetailsDocument;
import com.perennialsys.pdfreader.vo.mongoDocuments.SbDetailsProduct;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
public class ExportReportProcessor {

    private static final Logger LOG = Logger.getLogger(ExportReportProcessor.class);

    private final IService service;

    private final int BATCH_SIZE = Integer.parseInt(AppConfig.getAppProperty("max.record.upsert.batch.size", "1000"));

    @Autowired
    ExportReportProcessor(IService service) {
        this.service = service;
    }


    public File exportSbDetailsRecordFile(String pan, PdfFileType fileType, String fileTxnId, boolean isSftp) throws PdfReaderException {

        List<SbDetailsDocument> sbDetailsDocumentList = service.getShippingBillDetailsRecords(pan, Collections.singletonList(fileTxnId));

        ExportReportsManagerVO exportReportsManagerVO;

        //If the report is generated already then get the same report and do not generate new report.
        if (!sbDetailsDocumentList.isEmpty()) {
            int fileCount = sbDetailsDocumentList.stream().map(SbDetailsDocument::getSubTxnId).collect(Collectors.toSet()).size();
            //Generate the report with the record and export it.
            exportReportsManagerVO = service.checkAndAddEntryIntoExportReportManager(pan, fileTxnId,
                    fileType.name(), null,
                    TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name(), null, null, null, fileCount, null);

            writeSbDataToExcel(pan, fileType, sbDetailsDocumentList, exportReportsManagerVO, isSftp);

        } else {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >>" +
                    " Error while exporting SB report >> No data found for report generation");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.DATA_NOT_FOND);
        }

        return StreamHelper.prepareExcelFile(exportReportsManagerVO, isSftp);
    }

    public File exportSbDetailsRecordFile(String pan, String txnId, List<String> fileIdList, String startPeriod,
                                          String endPeriod, boolean isSftp, String description, PdfFileType fileType) throws PdfReaderException {

        List<SbDetailsDocument> sbDetailsDocumentList = service.getShippingBillDetailsRecords(pan, fileIdList);

        ExportReportsManagerVO exportReportsManagerVO;

        //If the report is generated already then get the same report and do not generate new report.
        if (!sbDetailsDocumentList.isEmpty()) {
            int fileCount = sbDetailsDocumentList.stream().map(SbDetailsDocument::getSubTxnId).collect(Collectors.toSet()).size();
            Date startPeriodDate = null;
            Date endPeriodDate = null;
            if (StringUtils.isNotBlank(startPeriod) && StringUtils.isNotBlank(endPeriod)) {
                startPeriodDate = DateFormatUtil.convertMMYYYYToDate(startPeriod);
                endPeriodDate = DateFormatUtil.getLastDaydate(DateFormatUtil.convertMMYYYYToDate(endPeriod));
            }

            //Generate the report with the record and export it.
            exportReportsManagerVO = service.checkAndAddEntryIntoExportReportManager(pan, txnId,
                    fileType.name(), null,
                    TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name(), startPeriodDate, endPeriodDate, null,
                    fileCount, description);

            writeSbDataToExcel(pan, fileType, sbDetailsDocumentList, exportReportsManagerVO, isSftp);

        } else {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >>" +
                    " Error while exporting SB report >> No data found for report generation");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.DATA_NOT_FOND);
        }

        return StreamHelper.prepareExcelFile(exportReportsManagerVO, isSftp);
    }

    public File exportSbDetailsRecordFile(String pan, PdfFileType fileType, String reportGenerationId, String startPeriod,
                                          String endPeriod, boolean isSftp, String description) throws PdfReaderException {

        ExportReportsManagerVO exportReportsManagerVO;

        if (StringUtils.isNotBlank(reportGenerationId)) {
            exportReportsManagerVO = service.getReportsManagerRepo().findByFileId(reportGenerationId);
        } else if (StringUtils.isNotBlank(startPeriod) && StringUtils.isNotBlank(endPeriod)) {
            Date startPeriodDate = DateFormatUtil.convertMMYYYYToDate(startPeriod);
            Date endPeriodDate = DateFormatUtil.getLastDaydate(DateFormatUtil.convertMMYYYYToDate(endPeriod));

            List<SbDetailsDocument> sbDetailsDocumentList = service.getShippingBillDetailsRecords(pan, startPeriodDate, endPeriodDate);

            //If the report is generated already then get the same report and do not generate new report.
            if (!sbDetailsDocumentList.isEmpty()) {
                int fileCount = sbDetailsDocumentList.stream().map(SbDetailsDocument::getSubTxnId).collect(Collectors.toSet()).size();

                //Generate the report with the record and export it.
                exportReportsManagerVO = service.checkAndAddEntryIntoExportReportManager(pan, UUID.randomUUID().toString(),
                        fileType.name(), null,
                        TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name(), startPeriodDate, endPeriodDate,
                        null, fileCount, description);

                writeSbDataToExcel(pan, fileType, sbDetailsDocumentList, exportReportsManagerVO, isSftp);

            } else {
                LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >>" +
                        " Error while exporting SB report >> No data found for report generation");
                throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.DATA_NOT_FOND);
            }
        } else {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >>" +
                    " Error while exporting SB report >> Data Not found");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        if (null == exportReportsManagerVO) {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >>" +
                    " Error while exporting SB report >> Export details not found.");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_NOT_FOUND);
        }

        return StreamHelper.prepareExcelFile(exportReportsManagerVO, isSftp);
    }

    public void writeSbDataToExcel(String pan, PdfFileType fileType,
                                    List<SbDetailsDocument> sbDetailsDocumentList,
                                    ExportReportsManagerVO exportReportsManagerVO, boolean isSftp) throws PdfReaderException {

        String fileGeneratedId = exportReportsManagerVO.getFileId();

        if(StringUtils.isBlank(fileGeneratedId)){
            fileGeneratedId = UUID.randomUUID().toString();
        }

        String fileNameWithExtension = FileHelper.getFileNameWithExtension(fileType.getExcelFileName(), pan);
        String folderPathToStoreReport = FileHelper.getLocalFolderPath(pan, FileHelper.EXPORT_REPORT, fileType.name(), isSftp);
        GenerateReportDetailsDto generateReportDetailsDto = null;
        int totalRecords = 0;
        int totalRecordsWritten = 0;
        try (Workbook workbook = FileHelper.getSXSSFWorkbookForFile(fileType.getExcelFileName())) {
            if (!sbDetailsDocumentList.isEmpty()) {
                if(StringUtils.isNotBlank(exportReportsManagerVO.getOtherDetails())) {
                    generateReportDetailsDto = new ObjectMapper().readValue(exportReportsManagerVO.getOtherDetails(),
                            GenerateReportDetailsDto.class);
                }else {
                    generateReportDetailsDto = new GenerateReportDetailsDto();
                }

                SXSSFSheet exportStatementSheet = (SXSSFSheet) workbook.getSheetAt(0);
                int index = 0;
                int lastRowIndex = 1;
                totalRecords = sbDetailsDocumentList.stream().mapToInt(sbDtls -> sbDtls.getProducts().size()).sum();
                Map<String, CellStyle> cellStyleMap = new HashMap<>();
                //3. Create the Excel file from the entries.
                for (SbDetailsDocument sbDetailsDocument : sbDetailsDocumentList) {
                    int count = 0;
                    for (SbDetailsProduct product : sbDetailsDocument.getProducts()) {
                        //Last row num is used to create new row and index is used to put value in sr.no column.
                        //Set data to each cell.
                        Row row = exportStatementSheet.createRow(++lastRowIndex);
                        FileHelper.createCellAndWriteDataWithStyle(++index, row, workbook, 0, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getIec(), row, workbook, 1, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getApplicantType(), row, workbook, 2, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getShippingBillNo(), row, workbook, 3, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getShippingBillDate(), row, workbook, 4, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getInvoiceNo(), row, workbook, 5, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getInvoiceDate(), row, workbook, 6, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getLeoDate(), row, workbook, 7, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getProductCode(), row, workbook, 8, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getProductDescription(), row, workbook, 9, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getQtyAsPerShippingBill(), row, workbook, 10, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getUqc(), row, workbook, 11, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getAvailableStock(), row, workbook, 12, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getQtyConsumedInClaim(), row, workbook, 13, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getSbInsurance(), row, workbook, 14, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getSbDiscount(), row, workbook, 15, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getSbCommission(), row, workbook, 16, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getSbOtherDeductions(), row, workbook, 17, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getFobVal(), row, workbook, 18, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getFobVal(), row, workbook, 19, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getInvVal(), row, workbook, 20, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getInvTrem(), row, workbook, 21, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getFreight(), row, workbook, 22, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getInsurance(), row, workbook, 23, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getDiscount(), row, workbook, 24, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getCommission(), row, workbook, 25, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getOtherDeductions(), row, workbook, 26, true, "", false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getExchangeRate(), row, workbook, 27, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getCustomHouseName(), row, workbook, 28, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getPortCode(), row, workbook, 29, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getGstin(), row, workbook, 30, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getGstinType(), row, workbook, 31, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getMode(), row, workbook, 32, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getExim(), row, workbook, 33, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getMeis(), row, workbook, 34, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getDbk(), row, workbook, 35, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getDeecDfia(), row, workbook, 36, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getDfrc(), row, workbook, 37, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getReExp(), row, workbook, 38, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getLut(), row, workbook, 39, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getStateOfOrigin(), row, workbook, 40, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getCountryOdFinalDestination(), row, workbook, 41, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getPortOfFinalDestination(), row, workbook, 42, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getCountryOfDischarge(), row, workbook, 43, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getExportorName(), row, workbook, 44, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getExportorAddress(), row, workbook, 45, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getConsigneeName(), row, workbook, 46, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getConsigneeAddress(), row, workbook, 47, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getThirdPartyName(), row, workbook, 48, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getThirdPartyAddress(), row, workbook, 49, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getAdCode(), row, workbook, 50, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getRbiWaiverNo(), row, workbook, 51, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getRbiWaiverDate(), row, workbook, 52, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getCbName(), row, workbook, 53, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getAeo(), row, workbook, 54, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getIfscNo(), row, workbook, 55, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getDbkClaim(), row, workbook, 56, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getIgstAmt(), row, workbook, 57, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getCessAmt(), row, workbook, 58, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getRodtepAmt(), row, workbook, 59, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getLeoNo(), row, workbook, 60, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getBrcRealisationDate(), row, workbook, 61, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getRate(), row, workbook, 62, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getItemValFc(), row, workbook, 63, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getItemFob(), row, workbook, 64, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getPmv(), row, workbook, 65, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getIgstStat(), row, workbook, 66, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getItmIgstAmt(), row, workbook, 67, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getSchCode(), row, workbook, 68, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getSchemaDescription(), row, workbook, 69, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getSqcMsr(), row, workbook, 70, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getPtAbroad(), row, workbook, 71, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getCompCess(), row, workbook, 72, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getEndUse(), row, workbook, 73, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getThirdPartyItem(), row, workbook, 74, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getItemHsnCode(), row, workbook, 75, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getAssessableValue(), row, workbook, 76, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getFtaBenefitAvailed(), row, workbook, 77, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getRewardBenefit(), row, workbook, 78, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getDbkSrNo(), row, workbook, 79, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getDbkValue(), row, workbook, 80, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getDbkRate(), row, workbook, 81, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getDbkAmt(), row, workbook, 82, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getRosctlAmt(), row, workbook, 83, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getEgmNo(), row, workbook, 84, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getEgmDt(), row, workbook, 85, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getRosctlAmt(), row, workbook, 86, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getRodtp(), row, workbook, 87, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getRodtepAmt(), row, workbook, 88, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(sbDetailsDocument.getInvCurrency(), row, workbook, 89, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getCust1(), row, workbook, 90, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getCust2(), row, workbook, 91, true, null, false, cellStyleMap);
                        FileHelper.createCellAndWriteDataWithStyle(product.getCust3(), row, workbook, 92, true, null, false, cellStyleMap);

                        count ++;

                        if(count >= 100){
                            totalRecordsWritten += count;

                            generateReportDetailsDto.setCompletionPercentage(NumberUtil
                                .calculatePercentage(totalRecords, totalRecordsWritten));

                            exportReportsManagerVO.setOtherDetails(generateReportDetailsDto.toString());
                            service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
                        }
                    }
                }
            }


            //4. Store it to s3 or local based on the property.
            String filePathExport = FileHelper.exportAndUploadFile(pan, workbook, fileNameWithExtension,
                    folderPathToStoreReport, exportReportsManagerVO.getReportType(), isSftp);

            //5. Update status in exim export report manager table with report generated status and file details.
            exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_COMPLETED.name());
            exportReportsManagerVO.setFileId(fileGeneratedId);
            exportReportsManagerVO.setReportFilLoc(filePathExport);
            exportReportsManagerVO.setReportName(fileNameWithExtension);

            if(null != generateReportDetailsDto) {
                generateReportDetailsDto.setCompletionPercentage(NumberUtil
                        .calculatePercentage(totalRecords, totalRecordsWritten));
                exportReportsManagerVO.setOtherDetails(generateReportDetailsDto.toString());
            }

            service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
        } catch (Exception e) {
            LOG.error("ERROR >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >> " +
                    "Error while exporting SB report >> e >> ", e);
            exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_FAILED.name());
            service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_EXPORTING_REPORT, ResponseMessage.ERROR_WHILE_EXPORTING_REPORT);
        }
    }

    public File exportBoeDetailsRecordFile(String pan, PdfFileType fileType, String fileTxnId, boolean isSftp) throws PdfReaderException {

        ExportReportsManagerVO exportReportsManagerVO;

        MongoDbCursor<BoeDetailsDocument> cursor = service.getBoeDetailsRecords(pan, Collections.singletonList(fileTxnId));
        Set<String> subTxnIdSet = new HashSet<>();
        if (cursor.getCount() > 0) {
            //Generate the report with the error record and export it.
            exportReportsManagerVO = service.checkAndAddEntryIntoExportReportManager(pan, fileTxnId,
                    fileType.name(), null,
                    TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name(), null, null, null,
                    cursor.getCount(), null);

            try (Workbook workbook = FileHelper.getSXSSFWorkbookForFile(fileType.getExcelFileName())) {
                writeBoeDetailsToExcel(cursor, workbook, subTxnIdSet);

                writeBoeAdditionalDetailsToExcel(workbook, subTxnIdSet, pan);

                String fileNameWithExtension = FileHelper.getFileNameWithExtension(fileType.getExcelFileName(), pan);

                String folderPathToStoreReport = FileHelper.getLocalFolderPath(pan, FileHelper.EXPORT_REPORT, fileType.name(), isSftp);

                //4. Store it to s3 or local based on the property.
                String filePathExport = FileHelper.exportAndUploadFile(pan, workbook, fileNameWithExtension,
                        folderPathToStoreReport, exportReportsManagerVO.getReportType(), isSftp);


                //5. Update status in exim export report manager table with report generated status and file details.
                exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_COMPLETED.name());
                exportReportsManagerVO.setFileId(UUID.randomUUID().toString());
                exportReportsManagerVO.setReportFilLoc(filePathExport);
                exportReportsManagerVO.setReportName(fileNameWithExtension);
                service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
            } catch (Exception e) {
                LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >>" +
                        " Error while exporting BOE report >> e >> ", e);
                exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_FAILED.name());
                service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
                throw new PdfReaderException(ResponseCode.ERROR_WHILE_EXPORTING_REPORT, ResponseMessage.ERROR_WHILE_EXPORTING_REPORT);
            }
        } else {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportBoeDetailsRecordFile >>" +
                    " Error while exporting BOE report >> No data found for report generation");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.DATA_NOT_FOND);
        }

        return StreamHelper.prepareExcelFile(exportReportsManagerVO, isSftp);
    }

    public File exportBoeDetailsRecordFile(String pan, String txnId, List<String> fileTxnIdList, String startPeriod,
                                           String endPeriod, boolean isSftp, String description, PdfFileType fileType) throws PdfReaderException {

        ExportReportsManagerVO exportReportsManagerVO;

        MongoDbCursor<BoeDetailsDocument> cursor = service.getBoeDetailsRecords(pan, fileTxnIdList);

        if (cursor.getCount() > 0) {

            Date startPeriodDate = null;
            Date endPeriodDate = null;
            if (StringUtils.isNotBlank(startPeriod) && StringUtils.isNotBlank(endPeriod)) {
                startPeriodDate = DateFormatUtil.convertMMYYYYToDate(startPeriod);
                endPeriodDate = DateFormatUtil.getLastDaydate(DateFormatUtil.convertMMYYYYToDate(endPeriod));
            }

            //Generate the report with the error record and export it.
            exportReportsManagerVO = service.checkAndAddEntryIntoExportReportManager(pan, txnId,
                    fileType.name(), null,
                    TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name(), startPeriodDate, endPeriodDate, null,
                    fileTxnIdList.size(), description);

            try (Workbook workbook = FileHelper.getSXSSFWorkbookForFile(fileType.getExcelFileName())) {

                Set<String> subTxnIdSet = new HashSet<>();

                writeBoeDetailsToExcel(cursor, workbook, subTxnIdSet);

                writeBoeAdditionalDetailsToExcel(workbook, subTxnIdSet, pan);

                String fileNameWithExtension = FileHelper.getFileNameWithExtension(fileType.getExcelFileName(), pan);

                String folderPathToStoreReport = FileHelper.getLocalFolderPath(pan, FileHelper.EXPORT_REPORT, fileType.name(), isSftp);

                //4. Store it to s3 or local based on the property.
                String filePathExport = FileHelper.exportAndUploadFile(pan, workbook, fileNameWithExtension,
                        folderPathToStoreReport, exportReportsManagerVO.getReportType(), isSftp);


                //5. Update status in exim export report manager table with report generated status and file details.
                exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_COMPLETED.name());
                exportReportsManagerVO.setFileId(UUID.randomUUID().toString());
                exportReportsManagerVO.setReportFilLoc(filePathExport);
                exportReportsManagerVO.setReportName(fileNameWithExtension);
                service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
            } catch (Exception e) {
                LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >>" +
                        " Error while exporting BOE report >> e >> ", e);
                exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_FAILED.name());
                service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
                throw new PdfReaderException(ResponseCode.ERROR_WHILE_EXPORTING_REPORT, ResponseMessage.ERROR_WHILE_EXPORTING_REPORT);
            }

        } else {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportBoeDetailsRecordFile >>" +
                    " Error while exporting BOE report >> No data found for report generation");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.DATA_NOT_FOND);
        }

        return StreamHelper.prepareExcelFile(exportReportsManagerVO, isSftp);
    }

    public File exportBoeDetailsRecordFile(String pan, PdfFileType fileType, String reportGenerationId, String startPeriod,
                                           String endPeriod, boolean isSftp, String description) throws PdfReaderException {

        ExportReportsManagerVO exportReportsManagerVO;

        if (StringUtils.isNotBlank(reportGenerationId)) {
            exportReportsManagerVO = service.getReportsManagerRepo().findByFileId(reportGenerationId);
        } else if (StringUtils.isNotBlank(startPeriod) && StringUtils.isNotBlank(endPeriod)) {
            Date startPeriodDate = DateFormatUtil.convertMMYYYYToDate(startPeriod);
            Date endPeriodDate = DateFormatUtil.getLastDaydate(DateFormatUtil.convertMMYYYYToDate(endPeriod));

            MongoDbCursor<BoeDetailsDocument> cursor = service.getBoeDetailsRecords(pan, startPeriodDate, endPeriodDate);

            //If the report is generated already then get the same report and do not generate new report.
            if (cursor.getCount() > 0) {
                //Generate the report with the error record and export it.
                exportReportsManagerVO = service.checkAndAddEntryIntoExportReportManager(pan, UUID.randomUUID().toString(),
                        fileType.name(), null,
                        TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name(), null, null,
                        null, cursor.getCount(), description);

                try (Workbook workbook = FileHelper.getSXSSFWorkbookForFile(fileType.getExcelFileName())) {

                    Set<String> subTxnIdSet = new HashSet<>();

                    writeBoeDetailsToExcel(cursor, workbook, subTxnIdSet);

                    writeBoeAdditionalDetailsToExcel(workbook, subTxnIdSet, pan);

                    String fileNameWithExtension = FileHelper.getFileNameWithExtension(fileType.getExcelFileName(), pan);

                    String folderPathToStoreReport = FileHelper.getLocalFolderPath(pan, FileHelper.EXPORT_REPORT, fileType.name(), isSftp);

                    //4. Store it to s3 or local based on the property.
                    String filePathExport = FileHelper.exportAndUploadFile(pan, workbook, fileNameWithExtension,
                            folderPathToStoreReport, exportReportsManagerVO.getReportType(), isSftp);


                    //5. Update status in exim export report manager table with report generated status and file details.
                    exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_COMPLETED.name());
                    exportReportsManagerVO.setFileId(UUID.randomUUID().toString());
                    exportReportsManagerVO.setReportFilLoc(filePathExport);
                    exportReportsManagerVO.setReportName(fileNameWithExtension);
                    service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
                } catch (Exception e) {
                    LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportSbDetailsRecordFile >>" +
                            " Error while exporting BOE report >> e >> ", e);
                    exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_FAILED.name());
                    service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
                    throw new PdfReaderException(ResponseCode.ERROR_WHILE_EXPORTING_REPORT, ResponseMessage.ERROR_WHILE_EXPORTING_REPORT);
                }
            } else {
                LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportBoeDetailsRecordFile >>" +
                        " Error while exporting BOE report >> No data found for report generation");
                throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.DATA_NOT_FOND);
            }
        } else {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportBoeDetailsRecordFile >>" +
                    " Error while exporting SB report >> Data Not found");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        if (null == exportReportsManagerVO) {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportBoeDetailsRecordFile >>" +
                    " Error while exporting SB report >> Export details not found.");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_NOT_FOUND);
        }

        if (!TransactionStatus.REPORT_GENERATION_COMPLETED.name().equals(exportReportsManagerVO.getStatus())) {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportBoeDetailsRecordFile >>" +
                    " Error while exporting SB report >> Report generation not complete.");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.REPORT_NOT_GENERATED);
        }

        return StreamHelper.prepareExcelFile(exportReportsManagerVO, isSftp);
    }

    private void writeBoeDetailsToExcel(MongoDbCursor<BoeDetailsDocument> cursor, Workbook workbook, Set<String> subTxnIdSet) {

        List<ExportReportTemplateMappingVO> templMappnigList = service.getExportReportTemplateMappingRepo().
                findAllByRefId(TemplateCode.BILL_OF_ENTRY.getId());

        List<BoeDetailsDocument> boeDetailsDocumentsList;
        Map<String, CellStyle> cellStyleMap = new HashMap<>();
        while (true) {
            boeDetailsDocumentsList = cursor.getRecords(BATCH_SIZE);

            if (boeDetailsDocumentsList.isEmpty()) {
                break;
            }
            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(0);
            int index = 0;
            int lastRowIndex = 2;
            //3. Create the Excel file from the entries.
            for (BoeDetailsDocument boeDetailsDocument : boeDetailsDocumentsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), ++index);
                keyValueMap.put(BoeDetailsFields.ITEM_CODE.getExcelMappingKey(), boeDetailsDocument.getItemCode());
                keyValueMap.put(BoeDetailsFields.ITEM_DESC.getExcelMappingKey(), boeDetailsDocument.getItemDesc());
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeDetailsDocument.getBillNo());
                keyValueMap.put(BoeDetailsFields.BOE_DATE.getExcelMappingKey(), boeDetailsDocument.getBoeDate());
                keyValueMap.put(BoeDetailsFields.CUSTOM_HOUSE_NAME.getExcelMappingKey(), boeDetailsDocument.getCustomHouse());
                keyValueMap.put(BoeDetailsFields.HSN.getExcelMappingKey(), boeDetailsDocument.getHsn());
                keyValueMap.put(BoeDetailsFields.IMPORTED_QTY.getExcelMappingKey(), boeDetailsDocument.getImportedQty());
                keyValueMap.put(BoeDetailsFields.UQC.getExcelMappingKey(), boeDetailsDocument.getUqc());
                keyValueMap.put(BoeDetailsFields.ASSESS.getExcelMappingKey(), boeDetailsDocument.getAssess());
                keyValueMap.put(BoeDetailsFields.BCD_RATE.getExcelMappingKey(), boeDetailsDocument.getBcdRate());
                keyValueMap.put(BoeDetailsFields.CUSTOM_CESS_RATE.getExcelMappingKey(), boeDetailsDocument.getCustomCessRate());
                keyValueMap.put(BoeDetailsFields.IMPORTED_COUNTRY.getExcelMappingKey(), boeDetailsDocument.getImportedCountry());
                keyValueMap.put(BoeDetailsFields.SUPPLIER_NAME.getExcelMappingKey(), boeDetailsDocument.getSupplierName());
                keyValueMap.put(BoeDetailsFields.IMPORTERS_NAME.getExcelMappingKey(), boeDetailsDocument.getImportersName());
                keyValueMap.put(BoeDetailsFields.FOREIGN_MATERIAL_SUP_NAME.getExcelMappingKey(), boeDetailsDocument.getForeignMaterialsSupName());
                keyValueMap.put(BoeDetailsFields.SUPPLIER_ADDRESS.getExcelMappingKey(), boeDetailsDocument.getSupplierAddress());
                keyValueMap.put(BoeDetailsFields.IMPORTERS_ADDRESS.getExcelMappingKey(), boeDetailsDocument.getImportersAddress());
                keyValueMap.put(BoeDetailsFields.INV_NO.getExcelMappingKey(), boeDetailsDocument.getInvNo());
                keyValueMap.put(BoeDetailsFields.INV_DATE.getExcelMappingKey(), boeDetailsDocument.getInvDate());
                keyValueMap.put(BoeDetailsFields.IGM_NO.getExcelMappingKey(), boeDetailsDocument.getIgmNo());
                keyValueMap.put(BoeDetailsFields.HIGHSEAS.getExcelMappingKey(), boeDetailsDocument.getHss());
                keyValueMap.put(BoeDetailsFields.PROV_FINAL.getExcelMappingKey(), boeDetailsDocument.getProvFinal());
                keyValueMap.put(BoeDetailsFields.PORT_OF_LOADING.getExcelMappingKey(), boeDetailsDocument.getPortOfLoading());
                keyValueMap.put(BoeDetailsFields.IGM_DATE.getExcelMappingKey(), boeDetailsDocument.getIgmDate());
                keyValueMap.put(BoeDetailsFields.MODE.getExcelMappingKey(), boeDetailsDocument.getMode());
                keyValueMap.put(BoeDetailsFields.INW_DATE.getExcelMappingKey(), boeDetailsDocument.getInwDate());
                keyValueMap.put(BoeDetailsFields.GIGMNO.getExcelMappingKey(), boeDetailsDocument.getGigmNo());
                keyValueMap.put(BoeDetailsFields.GIGMDT.getExcelMappingKey(), boeDetailsDocument.getGigmDt());
                keyValueMap.put(BoeDetailsFields.MAWB_NO.getExcelMappingKey(), boeDetailsDocument.getMaqbNo());
                keyValueMap.put(BoeDetailsFields.DATE.getExcelMappingKey(), boeDetailsDocument.getDate());
                keyValueMap.put(BoeDetailsFields.HAWB_NO.getExcelMappingKey(), boeDetailsDocument.getHawbNo());
                keyValueMap.put(BoeDetailsFields.MANIFEST_DATE.getExcelMappingKey(), boeDetailsDocument.getManifiestDate());
                keyValueMap.put(BoeDetailsFields.PKG.getExcelMappingKey(), boeDetailsDocument.getPkg());
                keyValueMap.put(BoeDetailsFields.GW.getExcelMappingKey(), boeDetailsDocument.getGw());
                keyValueMap.put(BoeDetailsFields.SEC_48.getExcelMappingKey(), boeDetailsDocument.getSec48());
                keyValueMap.put(BoeDetailsFields.RE_IMP.getExcelMappingKey(), boeDetailsDocument.getReImp());
                keyValueMap.put(BoeDetailsFields.ADV_BE.getExcelMappingKey(), boeDetailsDocument.getAdvBe());
                keyValueMap.put(BoeDetailsFields.EXAM.getExcelMappingKey(), boeDetailsDocument.getExam());
                keyValueMap.put(BoeDetailsFields.FIRST_CHECK.getExcelMappingKey(), boeDetailsDocument.getFirstCheck());
                keyValueMap.put(BoeDetailsFields.AD_CODE.getExcelMappingKey(), boeDetailsDocument.getAdCode());
                keyValueMap.put(BoeDetailsFields.TRANSACTING_AD_CODE.getExcelMappingKey(), boeDetailsDocument.getAdCode());
                keyValueMap.put(BoeDetailsFields.BE_TYPE.getExcelMappingKey(), boeDetailsDocument.getBeType());
                keyValueMap.put(BoeDetailsFields.EXCHANGE_RATE.getExcelMappingKey(), boeDetailsDocument.getExchangeRate());
                keyValueMap.put(BoeDetailsFields.GSTIN.getExcelMappingKey(), boeDetailsDocument.getGstin());
                keyValueMap.put(BoeDetailsFields.GSTIN_TYPE.getExcelMappingKey(), boeDetailsDocument.getGstinType());
                keyValueMap.put(BoeDetailsFields.IEC.getExcelMappingKey(), boeDetailsDocument.getIec());
                keyValueMap.put(BoeDetailsFields.PORT_CODE.getExcelMappingKey(), boeDetailsDocument.getPortCode());
                keyValueMap.put(BoeDetailsFields.BCD_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getBcdAmount());
                keyValueMap.put(BoeDetailsFields.UNIT_PROCE.getExcelMappingKey(), boeDetailsDocument.getUnitPrice());
                keyValueMap.put(BoeDetailsFields.ACD_RATE.getExcelMappingKey(), boeDetailsDocument.getAcdRate());
                keyValueMap.put(BoeDetailsFields.ACD_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getAcdAmount());
                keyValueMap.put(BoeDetailsFields.SWS_RATE.getExcelMappingKey(), boeDetailsDocument.getSwsRate());
                keyValueMap.put(BoeDetailsFields.SWS_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getSwsAmount());
                keyValueMap.put(BoeDetailsFields.TOTAL_DUTY.getExcelMappingKey(), boeDetailsDocument.getTotalDuty());
                keyValueMap.put(BoeDetailsFields.ITEM_ASSESSABLE_VALUE.getExcelMappingKey(), boeDetailsDocument.getAssessableValueAsPerBoe());
                keyValueMap.put(BoeDetailsFields.SAD_RATE.getExcelMappingKey(), boeDetailsDocument.getSadRate());
                keyValueMap.put(BoeDetailsFields.SAD_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getSadAmount());
                keyValueMap.put(BoeDetailsFields.IGST_RATE.getExcelMappingKey(), boeDetailsDocument.getIgstRate());
                keyValueMap.put(BoeDetailsFields.IGST_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getIgstAmount());
                keyValueMap.put(BoeDetailsFields.CESS_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getCessAmount());
                keyValueMap.put(BoeDetailsFields.ADD_RATE.getExcelMappingKey(), boeDetailsDocument.getAddRate());
                keyValueMap.put(BoeDetailsFields.ADD_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getAddAmount());
                keyValueMap.put(BoeDetailsFields.CVD_RATE.getExcelMappingKey(), boeDetailsDocument.getCvdRate());
                keyValueMap.put(BoeDetailsFields.CVD_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getCvdAmount());
                keyValueMap.put(BoeDetailsFields.INV_VALUE.getExcelMappingKey(), boeDetailsDocument.getInvValue());
                keyValueMap.put(BoeDetailsFields.VALUATION_INV_VALUE.getExcelMappingKey(), boeDetailsDocument.getInvValue());
                keyValueMap.put(BoeDetailsFields.INV_CURRENCY.getExcelMappingKey(), boeDetailsDocument.getInvCurrency());
                keyValueMap.put(BoeDetailsFields.VALUATION_INV_CURRENCY.getExcelMappingKey(), boeDetailsDocument.getInvCurrency());
                keyValueMap.put(BoeDetailsFields.TERM.getExcelMappingKey(), boeDetailsDocument.getTerm());
                keyValueMap.put(BoeDetailsFields.FREIGHT.getExcelMappingKey(), boeDetailsDocument.getFreight());
                keyValueMap.put(BoeDetailsFields.ITEM_VALUE.getExcelMappingKey(), boeDetailsDocument.getItemValue());
                keyValueMap.put(BoeDetailsFields.OOC_DATE.getExcelMappingKey(), boeDetailsDocument.getOocDate());
                keyValueMap.put(BoeDetailsFields.OOC_NO.getExcelMappingKey(), boeDetailsDocument.getOocNo());
                keyValueMap.put(BoeDetailsFields.LIC_UQC.getExcelMappingKey(), boeDetailsDocument.getLicUqc());
                keyValueMap.put(BoeDetailsFields.LIC_DEBIT_DUTY.getExcelMappingKey(), boeDetailsDocument.getLicDebitDuty());
                keyValueMap.put(BoeDetailsFields.LIC_SL_NO.getExcelMappingKey(), boeDetailsDocument.getLicSlNo());
                keyValueMap.put(BoeDetailsFields.LIC_NO.getExcelMappingKey(), boeDetailsDocument.getLicNo());
                keyValueMap.put(BoeDetailsFields.LIC_DATE.getExcelMappingKey(), boeDetailsDocument.getLicDate());
                keyValueMap.put(BoeDetailsFields.LIC_CODE.getExcelMappingKey(), boeDetailsDocument.getLicCode());
                keyValueMap.put(BoeDetailsFields.LIC_PORT.getExcelMappingKey(), boeDetailsDocument.getLicPort());
                keyValueMap.put(BoeDetailsFields.LIC_DEBIT_VALUE.getExcelMappingKey(), boeDetailsDocument.getLicDebitValue());
                keyValueMap.put(BoeDetailsFields.LIC_QTY.getExcelMappingKey(), boeDetailsDocument.getLicQty());
                keyValueMap.put(BoeDetailsFields.SVB_REF_NO.getExcelMappingKey(), boeDetailsDocument.getSvbRefNo());
                keyValueMap.put(BoeDetailsFields.SVB_REF_DATE.getExcelMappingKey(), boeDetailsDocument.getSvbRefDate());
                keyValueMap.put(BoeDetailsFields.SVB_PART_CODE.getExcelMappingKey(), boeDetailsDocument.getSvbPartCode());
                keyValueMap.put(BoeDetailsFields.SVB_LAB.getExcelMappingKey(), boeDetailsDocument.getSvbLab());
                keyValueMap.put(BoeDetailsFields.SVB_PF1.getExcelMappingKey(), boeDetailsDocument.getSvbPf1());
                keyValueMap.put(BoeDetailsFields.SVB_LOAD_DATE.getExcelMappingKey(), boeDetailsDocument.getSvbLoadDate());
                keyValueMap.put(BoeDetailsFields.SVB_PF2.getExcelMappingKey(), boeDetailsDocument.getSvbPf2());
                keyValueMap.put(BoeDetailsFields.PREV_BOE_NO.getExcelMappingKey(), boeDetailsDocument.getPrevBoeNo());
                keyValueMap.put(BoeDetailsFields.PREV_BOE_DATE.getExcelMappingKey(), boeDetailsDocument.getPrevBoeDate());
                keyValueMap.put(BoeDetailsFields.PREV_BOE_PART_CODE.getExcelMappingKey(), boeDetailsDocument.getPrevBoePartCode());
                keyValueMap.put(BoeDetailsFields.PREV_BOE_UNIT_PRICE.getExcelMappingKey(), boeDetailsDocument.getPrevBoeUnitPrice());
                keyValueMap.put(BoeDetailsFields.PREV_BOE_CURRENCY_CODE.getExcelMappingKey(), boeDetailsDocument.getPrevBoeCurrencyCode());
                keyValueMap.put(BoeDetailsFields.RE_IMP_NOTE_NO.getExcelMappingKey(), boeDetailsDocument.getReImpNoteNo());
                keyValueMap.put(BoeDetailsFields.RE_IMP_SL_NO.getExcelMappingKey(), boeDetailsDocument.getReImpSlNo());
                keyValueMap.put(BoeDetailsFields.RE_IMP_FRT.getExcelMappingKey(), boeDetailsDocument.getReImpFrt());
                keyValueMap.put(BoeDetailsFields.RE_IMP_UNIT_INS.getExcelMappingKey(), boeDetailsDocument.getReImpUnitIns());
                keyValueMap.put(BoeDetailsFields.RE_IMP_DUTY.getExcelMappingKey(), boeDetailsDocument.getReImpDuty());
                keyValueMap.put(BoeDetailsFields.RE_IMP_SB_NO.getExcelMappingKey(), boeDetailsDocument.getReImpSbNo());
                keyValueMap.put(BoeDetailsFields.RE_IMP_SB_DATE.getExcelMappingKey(), boeDetailsDocument.getReImpSbDate());
                keyValueMap.put(BoeDetailsFields.RE_IMP_PORT_CD.getExcelMappingKey(), boeDetailsDocument.getReImpPortCd());
                keyValueMap.put(BoeDetailsFields.RE_IMP_SINV.getExcelMappingKey(), boeDetailsDocument.getReImpSinv());
                keyValueMap.put(BoeDetailsFields.RE_IMP_SITEM_N.getExcelMappingKey(), boeDetailsDocument.getReImpSitemN());
                keyValueMap.put(BoeDetailsFields.ITEM_MANU_TYPE.getExcelMappingKey(), boeDetailsDocument.getItemManuType());
                keyValueMap.put(BoeDetailsFields.ITEM_MANU_MANUFACTURER_CODE.getExcelMappingKey(), boeDetailsDocument.getItemManuManufacturerCode());
                keyValueMap.put(BoeDetailsFields.ITEM_MANU_SORCE_CY.getExcelMappingKey(), boeDetailsDocument.getItemManuSorceCy());
                keyValueMap.put(BoeDetailsFields.ITEM_MANU_TRANS_CY.getExcelMappingKey(), boeDetailsDocument.getItemManuTransCy());
                keyValueMap.put(BoeDetailsFields.ITEM_MANU_ADDRESS.getExcelMappingKey(), boeDetailsDocument.getItemManuAddress());
                keyValueMap.put(BoeDetailsFields.ACCESSORY_ITM_DTLS.getExcelMappingKey(), boeDetailsDocument.getAccessoryItmDtls());
                keyValueMap.put(BoeDetailsFields.BE_STATUS.getExcelMappingKey(), boeDetailsDocument.getBeStatus());
                keyValueMap.put(BoeDetailsFields.DEF_BE.getExcelMappingKey(), boeDetailsDocument.getDefBe());
                keyValueMap.put(BoeDetailsFields.KACHA.getExcelMappingKey(), boeDetailsDocument.getKacha());
                keyValueMap.put(BoeDetailsFields.COUNTRY_OF_CONSIGNMENT.getExcelMappingKey(), boeDetailsDocument.getCountryOfConsignment());
                keyValueMap.put(BoeDetailsFields.PORT_OF_SHIPMENT.getExcelMappingKey(), boeDetailsDocument.getPortOfShipment());
                keyValueMap.put(BoeDetailsFields.CB_NAME.getExcelMappingKey(), boeDetailsDocument.getCbName());
                keyValueMap.put(BoeDetailsFields.AEO.getExcelMappingKey(), boeDetailsDocument.getAeo());
                keyValueMap.put(BoeDetailsFields.UCR.getExcelMappingKey(), boeDetailsDocument.getUcr());
                keyValueMap.put(BoeDetailsFields.BCD.getExcelMappingKey(), boeDetailsDocument.getBcd());
                keyValueMap.put(BoeDetailsFields.ACD.getExcelMappingKey(), boeDetailsDocument.getAcd());
                keyValueMap.put(BoeDetailsFields.SWS.getExcelMappingKey(), boeDetailsDocument.getSws());
                keyValueMap.put(BoeDetailsFields.NCCD.getExcelMappingKey(), boeDetailsDocument.getNccd());
                keyValueMap.put(BoeDetailsFields.ADD.getExcelMappingKey(), boeDetailsDocument.getAdd());
                keyValueMap.put(BoeDetailsFields.CVD.getExcelMappingKey(), boeDetailsDocument.getCvd());
                keyValueMap.put(BoeDetailsFields.IGST.getExcelMappingKey(), boeDetailsDocument.getIgst());
                keyValueMap.put(BoeDetailsFields.CESS.getExcelMappingKey(), boeDetailsDocument.getCess());
                keyValueMap.put(BoeDetailsFields.TOTAL_ASS_VALUE.getExcelMappingKey(), boeDetailsDocument.getTotalAssValue());
                keyValueMap.put(BoeDetailsFields.SG.getExcelMappingKey(), boeDetailsDocument.getSg());
                keyValueMap.put(BoeDetailsFields.AED.getExcelMappingKey(), boeDetailsDocument.getAed());
                keyValueMap.put(BoeDetailsFields.GSIA.getExcelMappingKey(), boeDetailsDocument.getGsia());
                keyValueMap.put(BoeDetailsFields.TTA.getExcelMappingKey(), boeDetailsDocument.getTta());
                keyValueMap.put(BoeDetailsFields.HEALTH.getExcelMappingKey(), boeDetailsDocument.getHealth());
                keyValueMap.put(BoeDetailsFields.DUTY_SUMMARY_TOTAL_DUTY.getExcelMappingKey(), boeDetailsDocument.getDutySummaryTotalDuty());
                keyValueMap.put(BoeDetailsFields.INT.getExcelMappingKey(), boeDetailsDocument.getDutySummaryInt());
                keyValueMap.put(BoeDetailsFields.PENALTY.getExcelMappingKey(), boeDetailsDocument.getPenalty());
                keyValueMap.put(BoeDetailsFields.FINE.getExcelMappingKey(), boeDetailsDocument.getFine());
                keyValueMap.put(BoeDetailsFields.TOTAL_AMOUNT.getExcelMappingKey(), boeDetailsDocument.getTotalAmount());
                keyValueMap.put(BoeDetailsFields.PUR_ORDE_NO.getExcelMappingKey(), boeDetailsDocument.getPurOrdeNo());
                keyValueMap.put(BoeDetailsFields.PUR_ORDER_DATE.getExcelMappingKey(), boeDetailsDocument.getPurOrderDate());
                keyValueMap.put(BoeDetailsFields.LC_NO.getExcelMappingKey(), boeDetailsDocument.getLcNo());
                keyValueMap.put(BoeDetailsFields.LC_DATE.getExcelMappingKey(), boeDetailsDocument.getLcDate());
                keyValueMap.put(BoeDetailsFields.CONTRAC_NO.getExcelMappingKey(), boeDetailsDocument.getContracNo());
                keyValueMap.put(BoeDetailsFields.CONTRACT_DATE.getExcelMappingKey(), boeDetailsDocument.getContractDate());
                keyValueMap.put(BoeDetailsFields.BUYERS_NAME.getExcelMappingKey(), boeDetailsDocument.getBuyersName());
                keyValueMap.put(BoeDetailsFields.BUYERS_ADDRESS.getExcelMappingKey(), boeDetailsDocument.getBuyersAddress());
                keyValueMap.put(BoeDetailsFields.SELLERS_NAME.getExcelMappingKey(), boeDetailsDocument.getSellersName());
                keyValueMap.put(BoeDetailsFields.SELLERS_ADDRESS.getExcelMappingKey(), boeDetailsDocument.getSellersAddress());
                keyValueMap.put(BoeDetailsFields.THIRD_PARTY_NAME.getExcelMappingKey(), boeDetailsDocument.getThirdPartyName());
                keyValueMap.put(BoeDetailsFields.THIRD_PARTY_ADDRESS.getExcelMappingKey(), boeDetailsDocument.getThirdPartyAddress());
                keyValueMap.put(BoeDetailsFields.TRANSACTING_PARTIES_AEO.getExcelMappingKey(), boeDetailsDocument.getTransactingPartiesAeo());
                keyValueMap.put(BoeDetailsFields.TRANSACTING_AD_CODE.getExcelMappingKey(), boeDetailsDocument.getAdCode());
                keyValueMap.put(BoeDetailsFields.INSURANCE.getExcelMappingKey(), boeDetailsDocument.getInsurance());
                keyValueMap.put(BoeDetailsFields.VALUATION_HSS.getExcelMappingKey(), boeDetailsDocument.getValuationHss());
                keyValueMap.put(BoeDetailsFields.LOADING.getExcelMappingKey(), boeDetailsDocument.getLoading());
                keyValueMap.put(BoeDetailsFields.COMMN.getExcelMappingKey(), boeDetailsDocument.getCommn());
                keyValueMap.put(BoeDetailsFields.PAY_TERMS.getExcelMappingKey(), boeDetailsDocument.getPayTerms());
                keyValueMap.put(BoeDetailsFields.VALUATION_TERMS.getExcelMappingKey(), boeDetailsDocument.getValTerms());
                keyValueMap.put(BoeDetailsFields.RELTD.getExcelMappingKey(), boeDetailsDocument.getReltd());
                keyValueMap.put(BoeDetailsFields.SVB_CH.getExcelMappingKey(), boeDetailsDocument.getSvbCh());
                keyValueMap.put(BoeDetailsFields.SVB_NO.getExcelMappingKey(), boeDetailsDocument.getSvbNo());
                keyValueMap.put(BoeDetailsFields.VALUATION_DATE.getExcelMappingKey(), boeDetailsDocument.getValuationDate());
                keyValueMap.put(BoeDetailsFields.LOA.getExcelMappingKey(), boeDetailsDocument.getLoa());
                keyValueMap.put(BoeDetailsFields.C_B.getExcelMappingKey(), boeDetailsDocument.getCb());
                keyValueMap.put(BoeDetailsFields.COC.getExcelMappingKey(), boeDetailsDocument.getCoc());
                keyValueMap.put(BoeDetailsFields.COP.getExcelMappingKey(), boeDetailsDocument.getCop());
                keyValueMap.put(BoeDetailsFields.HIND_CHG.getExcelMappingKey(), boeDetailsDocument.getHindChg());
                keyValueMap.put(BoeDetailsFields.G_S.getExcelMappingKey(), boeDetailsDocument.getGs());
                keyValueMap.put(BoeDetailsFields.DOC_CH.getExcelMappingKey(), boeDetailsDocument.getDocCh());
                keyValueMap.put(BoeDetailsFields.COO.getExcelMappingKey(), boeDetailsDocument.getCoo());
                keyValueMap.put(BoeDetailsFields.R_LF.getExcelMappingKey(), boeDetailsDocument.getRLf());
                keyValueMap.put(BoeDetailsFields.OTH_COST.getExcelMappingKey(), boeDetailsDocument.getOthCost());
                keyValueMap.put(BoeDetailsFields.LD_ULD.getExcelMappingKey(), boeDetailsDocument.getLdUld());
                keyValueMap.put(BoeDetailsFields.WS.getExcelMappingKey(), boeDetailsDocument.getWs());
                keyValueMap.put(BoeDetailsFields.OTC.getExcelMappingKey(), boeDetailsDocument.getOtc());
                keyValueMap.put(BoeDetailsFields.MISC_CHG.getExcelMappingKey(), boeDetailsDocument.getMiscChg());
                keyValueMap.put(BoeDetailsFields.ASS_VALUE.getExcelMappingKey(), boeDetailsDocument.getAssValue());
                keyValueMap.put(BoeDetailsFields.FS.getExcelMappingKey(), boeDetailsDocument.getFs());
                keyValueMap.put(BoeDetailsFields.PQ.getExcelMappingKey(), boeDetailsDocument.getPq());
                keyValueMap.put(BoeDetailsFields.DC.getExcelMappingKey(), boeDetailsDocument.getDc());
                keyValueMap.put(BoeDetailsFields.WC.getExcelMappingKey(), boeDetailsDocument.getWc());
                keyValueMap.put(BoeDetailsFields.AQ.getExcelMappingKey(), boeDetailsDocument.getAq());
                keyValueMap.put(BoeDetailsFields.UPI.getExcelMappingKey(), boeDetailsDocument.getUpi());
                keyValueMap.put(BoeDetailsFields.ITEM_DTLS_COO.getExcelMappingKey(), boeDetailsDocument.getItemDtlsCoo());
                keyValueMap.put(BoeDetailsFields.C_QTY.getExcelMappingKey(), boeDetailsDocument.getCQty());
                keyValueMap.put(BoeDetailsFields.C_UQC.getExcelMappingKey(), boeDetailsDocument.getCUqc());
                keyValueMap.put(BoeDetailsFields.S_QTY.getExcelMappingKey(), boeDetailsDocument.getSQty());
                keyValueMap.put(BoeDetailsFields.S_UQC.getExcelMappingKey(), boeDetailsDocument.getSUqc());
                keyValueMap.put(BoeDetailsFields.SCH.getExcelMappingKey(), boeDetailsDocument.getSch());
                keyValueMap.put(BoeDetailsFields.STND_PR.getExcelMappingKey(), boeDetailsDocument.getStndPr());
                keyValueMap.put(BoeDetailsFields.RSP.getExcelMappingKey(), boeDetailsDocument.getRsp());
                keyValueMap.put(BoeDetailsFields.REIMP.getExcelMappingKey(), boeDetailsDocument.getReImp());
                keyValueMap.put(BoeDetailsFields.PROV.getExcelMappingKey(), boeDetailsDocument.getProv());
                keyValueMap.put(BoeDetailsFields.END_USE.getExcelMappingKey(), boeDetailsDocument.getEndUse());
                keyValueMap.put(BoeDetailsFields.PRODN.getExcelMappingKey(), boeDetailsDocument.getProdn());
                keyValueMap.put(BoeDetailsFields.CNTRL.getExcelMappingKey(), boeDetailsDocument.getCntrl());
                keyValueMap.put(BoeDetailsFields.QUALFR.getExcelMappingKey(), boeDetailsDocument.getQualfr());
                keyValueMap.put(BoeDetailsFields.CONTNT.getExcelMappingKey(), boeDetailsDocument.getContnt());
                keyValueMap.put(BoeDetailsFields.STMNT.getExcelMappingKey(), boeDetailsDocument.getStmnt());
                keyValueMap.put(BoeDetailsFields.SUP_DOCS.getExcelMappingKey(), boeDetailsDocument.getSupDocs());
                keyValueMap.put(BoeDetailsFields.NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getNotnNo());
                keyValueMap.put(BoeDetailsFields.NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getNotnSrNo());
                keyValueMap.put(BoeDetailsFields.BCD_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getBcdDutyFg());
                keyValueMap.put(BoeDetailsFields.ACD_DUTY_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getAcdDutyNotnNo());
                keyValueMap.put(BoeDetailsFields.ACD_DUTY_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getAcdDutyNotnSrNo());
                keyValueMap.put(BoeDetailsFields.ACD_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getAcdDutyFg());
                keyValueMap.put(BoeDetailsFields.SWS_DUTY_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getSwsDutyNotnNo());
                keyValueMap.put(BoeDetailsFields.SWS_DUTY_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getSwsDutyNotnSrNo());
                keyValueMap.put(BoeDetailsFields.SWS_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getSwsDutyFg());
                keyValueMap.put(BoeDetailsFields.SAD_DUTY_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getSadDutyNotnNo());
                keyValueMap.put(BoeDetailsFields.SAD_DUTY_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getSadDutyNotnSrNo());
                keyValueMap.put(BoeDetailsFields.SAD_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getSadDutyFg());
                keyValueMap.put(BoeDetailsFields.IGST_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getIgstNotnNo());
                keyValueMap.put(BoeDetailsFields.IGST_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getIgstNotnSrNo());
                keyValueMap.put(BoeDetailsFields.IGST_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getIgstDutyFg());
                keyValueMap.put(BoeDetailsFields.CESS_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getCessNotnNo());
                keyValueMap.put(BoeDetailsFields.CESS_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getCessNotnSrNo());
                keyValueMap.put(BoeDetailsFields.CESS_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getCessDutyFg());
                keyValueMap.put(BoeDetailsFields.ADD_DUTY_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getAddDutyNotnNo());
                keyValueMap.put(BoeDetailsFields.ADD_DUTY_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getAddDutyNotnSrNo());
                keyValueMap.put(BoeDetailsFields.ADD_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getAddDutyFg());
                keyValueMap.put(BoeDetailsFields.CVD_DUTY_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getCvdDutyNotnNo());
                keyValueMap.put(BoeDetailsFields.CVD_DUTY_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getCvdDutyNotnSrNo());
                keyValueMap.put(BoeDetailsFields.CVD_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getCvdDutyFg());
                keyValueMap.put(BoeDetailsFields.SG_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getSgNotnNo());
                keyValueMap.put(BoeDetailsFields.SG_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getSgNotnSrNo());
                keyValueMap.put(BoeDetailsFields.SG_RATE.getExcelMappingKey(), boeDetailsDocument.getSgRate());
                keyValueMap.put(BoeDetailsFields.SG_AMT.getExcelMappingKey(), boeDetailsDocument.getSgAmt());
                keyValueMap.put(BoeDetailsFields.SG_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getSgDutyFg());
                keyValueMap.put(BoeDetailsFields.SP_EXD_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getSpExdNotn_no());
                keyValueMap.put(BoeDetailsFields.SP_EXD_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getSpExdNotnSrNo());
                keyValueMap.put(BoeDetailsFields.SP_EXD_RATE.getExcelMappingKey(), boeDetailsDocument.getSpExdRate());
                keyValueMap.put(BoeDetailsFields.SP_EXD_AMT.getExcelMappingKey(), boeDetailsDocument.getSpExdAmt());
                keyValueMap.put(BoeDetailsFields.SP_EXD_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getSpExdDutyFg());
                keyValueMap.put(BoeDetailsFields.CH_CESS_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getChCessNotnNo());
                keyValueMap.put(BoeDetailsFields.CH_CESS_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getChCessNotnSrNo());
                keyValueMap.put(BoeDetailsFields.CH_CESS_RATE.getExcelMappingKey(), boeDetailsDocument.getChCessRate());
                keyValueMap.put(BoeDetailsFields.CH_CESS_AMT.getExcelMappingKey(), boeDetailsDocument.getChCessAmt());
                keyValueMap.put(BoeDetailsFields.CH_CESS_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getChCessDutyFg());
                keyValueMap.put(BoeDetailsFields.TTA_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getTtaNotnNo());
                keyValueMap.put(BoeDetailsFields.TTA_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getTtaNotnSrNo());
                keyValueMap.put(BoeDetailsFields.TTA_RATE.getExcelMappingKey(), boeDetailsDocument.getTtaRate());
                keyValueMap.put(BoeDetailsFields.TTA_AMT.getExcelMappingKey(), boeDetailsDocument.getTtaAmt());
                keyValueMap.put(BoeDetailsFields.TTA_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getTtaDutyFg());
                keyValueMap.put(BoeDetailsFields.OTH_DUTY_CESS_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getOthDutyCessNotnNo());
                keyValueMap.put(BoeDetailsFields.OTH_DUTY_CESS_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getOthDutyCessNotnSrNo());
                keyValueMap.put(BoeDetailsFields.OTH_DUTY_CESS_RATE.getExcelMappingKey(), boeDetailsDocument.getOthDutyCessRate());
                keyValueMap.put(BoeDetailsFields.OTH_DUTY_CESS_AMT.getExcelMappingKey(), boeDetailsDocument.getOthDutyCessAmt());
                keyValueMap.put(BoeDetailsFields.OTH_DUTY_CESS_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getOthDutyCessDutyFg());
                keyValueMap.put(BoeDetailsFields.CVD_EDC_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getCvdEdcNotnNo());
                keyValueMap.put(BoeDetailsFields.CVD_EDC_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getCvdEdcNotnSrNo());
                keyValueMap.put(BoeDetailsFields.CVD_EDC_RATE.getExcelMappingKey(), boeDetailsDocument.getCvdEdcRate());
                keyValueMap.put(BoeDetailsFields.CVD_EDC_AMT.getExcelMappingKey(), boeDetailsDocument.getCvdEdcAmt());
                keyValueMap.put(BoeDetailsFields.CVD_EDC_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getCvdEdcDutyFg());
                keyValueMap.put(BoeDetailsFields.CVD_HEC_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getCvdHecNotnNo());
                keyValueMap.put(BoeDetailsFields.CVD_HEC_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getCvdHecNotnSrNo());
                keyValueMap.put(BoeDetailsFields.CVD_HEC_RATE.getExcelMappingKey(), boeDetailsDocument.getCvdHecRate());
                keyValueMap.put(BoeDetailsFields.CVD_HEC_AMT.getExcelMappingKey(), boeDetailsDocument.getCvdHecAmt());
                keyValueMap.put(BoeDetailsFields.CVD_HEC_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getCvdHecDutyFg());
                keyValueMap.put(BoeDetailsFields.CUS_EDC_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getCusEdcNotnNo());
                keyValueMap.put(BoeDetailsFields.CUS_EDC_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getCusEdcNotnSrNo());
                keyValueMap.put(BoeDetailsFields.CUS_EDC_RATE.getExcelMappingKey(), boeDetailsDocument.getCusEdcRate());
                keyValueMap.put(BoeDetailsFields.CUS_EDC_AMT.getExcelMappingKey(), boeDetailsDocument.getCusEdcAmt());
                keyValueMap.put(BoeDetailsFields.CUS_EDC_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getCusEdcDutyFg());
                keyValueMap.put(BoeDetailsFields.CUS_HEC_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getCusHecNotnNo());
                keyValueMap.put(BoeDetailsFields.CUS_HEC_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getCusHecNotnSrNo());
                keyValueMap.put(BoeDetailsFields.CUS_HEC_RATE.getExcelMappingKey(), boeDetailsDocument.getCusHecRate());
                keyValueMap.put(BoeDetailsFields.CUS_HEC_AMT.getExcelMappingKey(), boeDetailsDocument.getCusHecAmt());
                keyValueMap.put(BoeDetailsFields.CUS_HEC_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getCusHecDutyFg());
                keyValueMap.put(BoeDetailsFields.NCD_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getNcdNotnNo());
                keyValueMap.put(BoeDetailsFields.NCD_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getNcdNotnSrNo());
                keyValueMap.put(BoeDetailsFields.NCD_RATE.getExcelMappingKey(), boeDetailsDocument.getNcdRate());
                keyValueMap.put(BoeDetailsFields.NCD_AMT.getExcelMappingKey(), boeDetailsDocument.getNcdAmt());
                keyValueMap.put(BoeDetailsFields.NCD_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getNcdDutyFg());
                keyValueMap.put(BoeDetailsFields.AGGR_NOTN_NO.getExcelMappingKey(), boeDetailsDocument.getAggrNotnNo());
                keyValueMap.put(BoeDetailsFields.AGGR_NOTN_SR_NO.getExcelMappingKey(), boeDetailsDocument.getAggrNotnSrNo());
                keyValueMap.put(BoeDetailsFields.AGGR_RATE.getExcelMappingKey(), boeDetailsDocument.getAggrRate());
                keyValueMap.put(BoeDetailsFields.AGGR_AMT.getExcelMappingKey(), boeDetailsDocument.getAggrAmt());
                keyValueMap.put(BoeDetailsFields.AGGR_DUTY_FG.getExcelMappingKey(), boeDetailsDocument.getAggrDutyFg());
                keyValueMap.put(BoeDetailsFields.BILL_OF_ENTRY_SUBMISSION_DATE.getExcelMappingKey(), boeDetailsDocument.getBillOfEntrySubmissionDate());
                keyValueMap.put(BoeDetailsFields.BILL_OF_ENTRY_SUBMISSION_TIME.getExcelMappingKey(), boeDetailsDocument.getBillOfEntrySubmissionTime());
                keyValueMap.put(BoeDetailsFields.BILL_OF_ENTRY_ASSESSMENT_DATE.getExcelMappingKey(), boeDetailsDocument.getBillOfEntryAssessmentDate());
                keyValueMap.put(BoeDetailsFields.BILL_OF_ENTRY_ASSESSMENT_TIME.getExcelMappingKey(), boeDetailsDocument.getBillOfEntryAssessmentTime());
                keyValueMap.put(BoeDetailsFields.BILL_OF_ENTRY_EXAMINATION_DATE.getExcelMappingKey(), boeDetailsDocument.getBillOfEntryExaminationDate());
                keyValueMap.put(BoeDetailsFields.BILL_OF_ENTRY_EXAMINATION_TIME.getExcelMappingKey(), boeDetailsDocument.getBillOfEntryExaminationTime());
                keyValueMap.put(BoeDetailsFields.BILL_OF_ENTRY_OOC_DATE.getExcelMappingKey(), boeDetailsDocument.getBillOfEntryOocDate());
                keyValueMap.put(BoeDetailsFields.BILL_OF_ENTRY_OOC_TIME.getExcelMappingKey(), boeDetailsDocument.getBillOfEntryOocTime());
                keyValueMap.put(BoeDetailsFields.CUST_1.getExcelMappingKey(), boeDetailsDocument.getCustom1());
                keyValueMap.put(BoeDetailsFields.CUST_2.getExcelMappingKey(), boeDetailsDocument.getCustom2());
                keyValueMap.put(BoeDetailsFields.CUST_3.getExcelMappingKey(), boeDetailsDocument.getCustom3());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }

                subTxnIdSet.add(boeDetailsDocument.getSubTxnId());
            }
        }
    }

    private void writeBoeAdditionalDetailsToExcel(Workbook workBook, Set<String> subTxnIdSet, String pan) {

        MongoDbCursor<BoeAdditionalDetailsDocument> cursor = service.getBoeAdditionalDetailsRecords(pan, new ArrayList<>(subTxnIdSet));

        if (cursor.getCount() > 0) {
            Map<String, Integer> lastRowIndexMap = new HashMap<>();
            List<ExportReportTemplateMappingVO> singleWindowDetailsTemplMappnigList =
                    service.getExportReportTemplateMappingRepo().
                            findAllByRefId(TemplateCode.SINGLE_WINDOW_DETAILS.getId());

            lastRowIndexMap.put(TemplateCode.SINGLE_WINDOW_DETAILS.name(), 2);

            List<ExportReportTemplateMappingVO> singleWindowConstDetailsTemplMappnigList =
                    service.getExportReportTemplateMappingRepo().
                            findAllByRefId(TemplateCode.SINGLE_WINDOW_CONST_DETAILS.getId());

            lastRowIndexMap.put(TemplateCode.SINGLE_WINDOW_CONST_DETAILS.name(), 2);

            List<ExportReportTemplateMappingVO> singleWindowControlDetailsTemplMappnigList =
                    service.getExportReportTemplateMappingRepo().
                            findAllByRefId(TemplateCode.SINGLE_WINDOW_CONTROL_DETAILS.getId());

            lastRowIndexMap.put(TemplateCode.SINGLE_WINDOW_CONTROL_DETAILS.name(), 2);

            List<ExportReportTemplateMappingVO> supportingDocsDetailsTemplMappnigList =
                    service.getExportReportTemplateMappingRepo().
                            findAllByRefId(TemplateCode.SUPPORTING_DOCS_DETAILS.getId());

            lastRowIndexMap.put(TemplateCode.SUPPORTING_DOCS_DETAILS.name(), 2);

            List<ExportReportTemplateMappingVO> additionalContainerDetailsTemplMappnigList =
                    service.getExportReportTemplateMappingRepo().
                            findAllByRefId(TemplateCode.ADDITIONAL_CONTAINER_DETAILS.getId());

            lastRowIndexMap.put(TemplateCode.ADDITIONAL_CONTAINER_DETAILS.name(), 2);

            List<ExportReportTemplateMappingVO> boeWarehouseDtlsListTemplMappnigList =
                    service.getExportReportTemplateMappingRepo().
                            findAllByRefId(TemplateCode.BOE_WAREHOUSE_DETAILS.getId());

            lastRowIndexMap.put(TemplateCode.BOE_WAREHOUSE_DETAILS.name(), 2);

            List<ExportReportTemplateMappingVO> boePaymentDtlsListTemplMappnigList =
                    service.getExportReportTemplateMappingRepo().
                            findAllByRefId(TemplateCode.BOE_PAYMENT_DETAILS.getId());

            lastRowIndexMap.put(TemplateCode.BOE_PAYMENT_DETAILS.name(), 2);

            List<ExportReportTemplateMappingVO> boeBondDtlsListTemplMappnigList =
                    service.getExportReportTemplateMappingRepo().
                            findAllByRefId(TemplateCode.BOE_BOND_DETAILS.getId());

            lastRowIndexMap.put(TemplateCode.BOE_BOND_DETAILS.name(), 2);

            List<BoeAdditionalDetailsDocument> boeDetailsDocumentsList;

            while (true) {
                boeDetailsDocumentsList = cursor.getRecords(BATCH_SIZE);

                if (boeDetailsDocumentsList.isEmpty()) {
                    break;
                }
                //3. Create the Excel file from the entries.
                for (BoeAdditionalDetailsDocument boeAdditionalDetailsDocument : boeDetailsDocumentsList) {
                    writeBoeBondDetailsToExcel(boeAdditionalDetailsDocument.getBoeBondDetails(),
                            boeAdditionalDetailsDocument.getBoeNo(), workBook, boeBondDtlsListTemplMappnigList, lastRowIndexMap);

                    writeBoePaymentDetailsToExcel(boeAdditionalDetailsDocument.getBoePaymentDtlsList(),
                            boeAdditionalDetailsDocument.getBoeNo(), workBook, boePaymentDtlsListTemplMappnigList, lastRowIndexMap);

                    writeBoeWarehouseDetailsToExcel(boeAdditionalDetailsDocument.getBoeWarehouseDtlsList(),
                            boeAdditionalDetailsDocument.getBoeNo(), workBook, boeWarehouseDtlsListTemplMappnigList, lastRowIndexMap);

                    writeBoeSingleWindowDetailsToExcel(boeAdditionalDetailsDocument.getSingleWindowDetailsList(),
                            boeAdditionalDetailsDocument.getBoeNo(), workBook, singleWindowDetailsTemplMappnigList, lastRowIndexMap);

                    writeBoeSingleWindowConstituentsDetailsToExcel(boeAdditionalDetailsDocument.getSingleWindowConstDtlsList(),
                            boeAdditionalDetailsDocument.getBoeNo(), workBook, singleWindowConstDetailsTemplMappnigList, lastRowIndexMap);

                    writeBoeSingleWindowControlDetailsToExcel(boeAdditionalDetailsDocument.getSingleWindowControlDtlsList(),
                            boeAdditionalDetailsDocument.getBoeNo(), workBook, singleWindowControlDetailsTemplMappnigList, lastRowIndexMap);

                    writeBoeSupportingDocDetailsToExcel(boeAdditionalDetailsDocument.getSupportingDocsDtlsList(),
                            boeAdditionalDetailsDocument.getBoeNo(), workBook, supportingDocsDetailsTemplMappnigList, lastRowIndexMap);

                    writeBoeContainerDetailsToExcel(boeAdditionalDetailsDocument.getAdditionalContainerDtlsList(),
                            boeAdditionalDetailsDocument.getBoeNo(), workBook, additionalContainerDetailsTemplMappnigList, lastRowIndexMap);
                }
            }
        }
    }

    private void writeBoeBondDetailsToExcel(List<BoeBondDetailsDocument> bondDtlsList, String boeNo,
                                            Workbook workbook, List<ExportReportTemplateMappingVO> templMappnigList, Map<String, Integer> lastRowIndexMap) {
        LOG.error("START >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeBondDetailsToExcel");
        if (null != bondDtlsList && !bondDtlsList.isEmpty()) {
            Map<String, CellStyle> cellStyleMap = new HashMap<>();
            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(1);
            int index = 0;
            int lastRowIndex = lastRowIndexMap.get(TemplateCode.BOE_BOND_DETAILS.name());

            for (BoeBondDetailsDocument bondDetail : bondDtlsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), lastRowIndex-2);
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeNo);
                keyValueMap.put(BoeDetailsFields.BOND_NO.getExcelMappingKey(), bondDetail.getBondNo());
                keyValueMap.put(BoeDetailsFields.BOND_PORT.getExcelMappingKey(), bondDetail.getBondPort());
                keyValueMap.put(BoeDetailsFields.BOND_CODE.getExcelMappingKey(), bondDetail.getBondCode());
                keyValueMap.put(BoeDetailsFields.BOND_DEBT_AMT.getExcelMappingKey(), bondDetail.getBondDebtAmt());
                keyValueMap.put(BoeDetailsFields.BOND_BG_AMT.getExcelMappingKey(), bondDetail.getBondBgAmt());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }
            }
            lastRowIndexMap.put(TemplateCode.BOE_BOND_DETAILS.name(), lastRowIndex);
        }

        LOG.error("END >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeBondDetailsToExcel");
    }

    private void writeBoePaymentDetailsToExcel(List<BoePaymentDetailsDocument> paymentDtlsList, String boeNo,
                                               Workbook workbook, List<ExportReportTemplateMappingVO> templMappnigList, Map<String, Integer> lastRowIndexMap) {
        LOG.error("START >> CLASS >> ExportReportProcessor >> METHOD >> writeBoePaymentDetailsToExcel");
        if (null != paymentDtlsList && !paymentDtlsList.isEmpty()) {

            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(2);
            int index = 0;
            int lastRowIndex = lastRowIndexMap.get(TemplateCode.BOE_PAYMENT_DETAILS.name());
             Map<String, CellStyle> cellStyleMap = new HashMap<>();
            for (BoePaymentDetailsDocument paymentDetail : paymentDtlsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), lastRowIndex-2);
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeNo);
                keyValueMap.put(BoeDetailsFields.PAYMENT_CHALLAN_NO.getExcelMappingKey(),
                        paymentDetail.getPaymentChallanNo());
                keyValueMap.put(BoeDetailsFields.PAYMENT_PAID_NO.getExcelMappingKey(),
                        paymentDetail.getPaymentPaidNo());
                keyValueMap.put(BoeDetailsFields.PAYMENT_AMOUNT.getExcelMappingKey(),
                        paymentDetail.getPaymentAmount());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }
            }
            lastRowIndexMap.put(TemplateCode.BOE_PAYMENT_DETAILS.name(), lastRowIndex);
        }
        LOG.error("END >> CLASS >> ExportReportProcessor >> METHOD >> writeBoePaymentDetailsToExcel");
    }

    private void writeBoeWarehouseDetailsToExcel(List<BoeWarehouseDetailsDocument> warehouseDetailsList, String boeNo,
                                                 Workbook workbook, List<ExportReportTemplateMappingVO> templMappnigList, Map<String, Integer> lastRowIndexMap) {
        LOG.error("START >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeWarehouseDetailsToExcel");
        if (null != warehouseDetailsList && !warehouseDetailsList.isEmpty()) {

            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(3);
            int index = 0;
            int lastRowIndex = lastRowIndexMap.get(TemplateCode.BOE_WAREHOUSE_DETAILS.name());
            Map<String, CellStyle> cellStyleMap = new HashMap<>();
            for (BoeWarehouseDetailsDocument warehouseDetail : warehouseDetailsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), lastRowIndex-2);
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeNo);
                keyValueMap.put(BoeDetailsFields.WH_WBE_NO.getExcelMappingKey(), warehouseDetail.getWhWbeNo());
                keyValueMap.put(BoeDetailsFields.WH_DATE.getExcelMappingKey(), warehouseDetail.getWhDate());
                keyValueMap.put(BoeDetailsFields.WH_WBE_SITE.getExcelMappingKey(), warehouseDetail.getWhWbeSite());
                keyValueMap.put(BoeDetailsFields.WH_CODE.getExcelMappingKey(), warehouseDetail.getWhCode());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }
            }
            lastRowIndexMap.put(TemplateCode.BOE_WAREHOUSE_DETAILS.name(), lastRowIndex);
        }
        LOG.error("END >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeWarehouseDetailsToExcel");
    }

    private void writeBoeSingleWindowDetailsToExcel(List<BoeAdditionalSingleWindowDetailsDocument> singleWindowDetailsList,
                                                    String boeNo, Workbook workbook, List<ExportReportTemplateMappingVO> templMappnigList, Map<String, Integer> lastRowIndexMap) {
        LOG.error("START >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeSingleWindowDetailsToExcel");
        if (null != singleWindowDetailsList && !singleWindowDetailsList.isEmpty()) {

            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(4);
            int index = 0;
            int lastRowIndex = lastRowIndexMap.get(TemplateCode.SINGLE_WINDOW_DETAILS.name());
            Map<String, CellStyle> cellStyleMap = new HashMap<>();
            for (BoeAdditionalSingleWindowDetailsDocument boeAdditionalSingleWindowDtls : singleWindowDetailsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), lastRowIndex-2);
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeNo);
                keyValueMap.put(BoeDetailsFields.INV_SN.getExcelMappingKey(), boeAdditionalSingleWindowDtls.getInvSN());
                keyValueMap.put(BoeDetailsFields.ITEM_SN.getExcelMappingKey(), boeAdditionalSingleWindowDtls.getItemSN());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_TYPE.getExcelMappingKey(), boeAdditionalSingleWindowDtls.getSingleWindowType());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_QUALIFIER.getExcelMappingKey(), boeAdditionalSingleWindowDtls.getSingleWindowQualifier());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_INFO_CODE.getExcelMappingKey(), boeAdditionalSingleWindowDtls.getSingleWindowInfoCode());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_INFO_TEXT.getExcelMappingKey(), boeAdditionalSingleWindowDtls.getSingleWindowInfoText());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_INFO_MSR.getExcelMappingKey(), boeAdditionalSingleWindowDtls.getSingleWindowInfoMsr());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_UQC.getExcelMappingKey(), boeAdditionalSingleWindowDtls.getSingleWindowUqc());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }
            }
            lastRowIndexMap.put(TemplateCode.SINGLE_WINDOW_DETAILS.name(), lastRowIndex);
        }
        LOG.error("END >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeSingleWindowDetailsToExcel");
    }

    private void writeBoeSingleWindowConstituentsDetailsToExcel(List<BoeAdditionalSingleWindowConstituentsDetailsDocument>
                                                                        singleWindowConstDetailsList, String boeNo, Workbook workbook,
                                                                List<ExportReportTemplateMappingVO> templMappnigList, Map<String, Integer> lastRowIndexMap) {
        LOG.error("START >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeSingleWindowConstituentsDetailsToExcel");
        if (null != singleWindowConstDetailsList && !singleWindowConstDetailsList.isEmpty()) {

            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(5);
            int index = 0;
            int lastRowIndex = lastRowIndexMap.get(TemplateCode.SINGLE_WINDOW_CONST_DETAILS.name());
            Map<String, CellStyle> cellStyleMap = new HashMap<>();
            for (BoeAdditionalSingleWindowConstituentsDetailsDocument boeAdditionalSingleConstWindowDtls : singleWindowConstDetailsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), lastRowIndex-2);
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeNo);
                keyValueMap.put(BoeDetailsFields.INV_SN.getExcelMappingKey(),
                        boeAdditionalSingleConstWindowDtls.getInvSN());
                keyValueMap.put(BoeDetailsFields.ITEM_SN.getExcelMappingKey(),
                        boeAdditionalSingleConstWindowDtls.getItemSN());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONST_CS_NO.getExcelMappingKey(),
                        boeAdditionalSingleConstWindowDtls.getSingleWindowConstCsNo());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONST_NAME.getExcelMappingKey(),
                        boeAdditionalSingleConstWindowDtls.getSingleWindowConstName());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONST_CODE.getExcelMappingKey(),
                        boeAdditionalSingleConstWindowDtls.getSingleWindowConstCode());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONST_PERCENTAGE.getExcelMappingKey(),
                        boeAdditionalSingleConstWindowDtls.getSingleWindowConstPercentage());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONST_YIELD_PCT.getExcelMappingKey(),
                        boeAdditionalSingleConstWindowDtls.getSingleWindowConstYieldPct());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONST_ING.getExcelMappingKey(),
                        boeAdditionalSingleConstWindowDtls.getSingleWindowConstIng());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }
            }
            lastRowIndexMap.put(TemplateCode.SINGLE_WINDOW_CONST_DETAILS.name(), lastRowIndex);
        }
        LOG.error("END >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeSingleWindowConstituentsDetailsToExcel");
    }

    private void writeBoeSingleWindowControlDetailsToExcel(List<BoeAdditionalSingleWindowControlDetailsDocument>
                                                                   singleWindowDetailsList, String boeNo, Workbook workbook,
                                                           List<ExportReportTemplateMappingVO> templMappnigList, Map<String, Integer> lastRowIndexMap) {
        LOG.error("START >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeSingleWindowControlDetailsToExcel");
        if (null != singleWindowDetailsList && !singleWindowDetailsList.isEmpty()) {

            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(6);
            int index = 0;
            int lastRowIndex = lastRowIndexMap.get(TemplateCode.SINGLE_WINDOW_CONTROL_DETAILS.name());
            Map<String, CellStyle> cellStyleMap = new HashMap<>();
            for (BoeAdditionalSingleWindowControlDetailsDocument boeAdditionalSingleWindowControlDtls : singleWindowDetailsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), lastRowIndex-2);
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeNo);
                keyValueMap.put(BoeDetailsFields.INV_SN.getExcelMappingKey(), boeAdditionalSingleWindowControlDtls.getInvSN());
                keyValueMap.put(BoeDetailsFields.ITEM_SN.getExcelMappingKey(), boeAdditionalSingleWindowControlDtls.getItemSN());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_TYPE.getExcelMappingKey(),
                        boeAdditionalSingleWindowControlDtls.getSingleWindowControlType());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_LOCATION.getExcelMappingKey(),
                        boeAdditionalSingleWindowControlDtls.getSingleWindowControlLocation());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_START_DT.getExcelMappingKey(),
                        boeAdditionalSingleWindowControlDtls.getSingleWindowControlStartDt());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_END_DT.getExcelMappingKey(),
                        boeAdditionalSingleWindowControlDtls.getSingleWindowControlEndDt());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_RES_CD.getExcelMappingKey(),
                        boeAdditionalSingleWindowControlDtls.getSingleWindowControlResCd());
                keyValueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_RES_TEXT.getExcelMappingKey(),
                        boeAdditionalSingleWindowControlDtls.getSingleWindowControlResText());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }
            }
            lastRowIndexMap.put(TemplateCode.SINGLE_WINDOW_CONTROL_DETAILS.name(), lastRowIndex);
        }
        LOG.error("END >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeSingleWindowControlDetailsToExcel");
    }

    private void writeBoeSupportingDocDetailsToExcel(List<BoeAdditionalSupportingDocDetailsDocument>
                                                             supportingDocDetailsList, String boeNo, Workbook workbook,
                                                     List<ExportReportTemplateMappingVO> templMappnigList, Map<String, Integer> lastRowIndexMap) {
        LOG.error("START >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeSupportingDocDetailsToExcel");
        if (null != supportingDocDetailsList && !supportingDocDetailsList.isEmpty()) {

            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(7);
            int index = 0;
            int lastRowIndex = lastRowIndexMap.get(TemplateCode.SUPPORTING_DOCS_DETAILS.name());
            Map<String, CellStyle> cellStyleMap = new HashMap<>();
            for (BoeAdditionalSupportingDocDetailsDocument boeAdditionalSupportingDocDtls : supportingDocDetailsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), lastRowIndex-2);
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeNo);
                keyValueMap.put(BoeDetailsFields.SUPPORTING_DOCS_ICE_GATE_ID.getExcelMappingKey(),
                        boeAdditionalSupportingDocDtls.getSupportingDocsIceGateId());
                keyValueMap.put(BoeDetailsFields.SUPPORTING_DOCS_TYPE.getExcelMappingKey(),
                        boeAdditionalSupportingDocDtls.getSupportingDocsType());
                keyValueMap.put(BoeDetailsFields.SUPPORTING_DOCI_RN.getExcelMappingKey(),
                        boeAdditionalSupportingDocDtls.getSupportingDocIrn());
                keyValueMap.put(BoeDetailsFields.SUPPORTING_DOCS_CD.getExcelMappingKey(),
                        boeAdditionalSupportingDocDtls.getSupportingDocsCd());
                keyValueMap.put(BoeDetailsFields.SUPPORTING_DOCS_ISSUE_PLACE.getExcelMappingKey(),
                        boeAdditionalSupportingDocDtls.getSupportingDocsIssuePlace());
                keyValueMap.put(BoeDetailsFields.SUPPORTING_DOCS_ISSUE_DT.getExcelMappingKey(),
                        boeAdditionalSupportingDocDtls.getSupportingDocsIssueDt());
                keyValueMap.put(BoeDetailsFields.SUPPORTING_DOCS_EXPORT_DT.getExcelMappingKey(),
                        boeAdditionalSupportingDocDtls.getSupportingDocsExportDt());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }
            }
            lastRowIndexMap.put(TemplateCode.SUPPORTING_DOCS_DETAILS.name(), lastRowIndex);
        }
        LOG.error("END >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeSupportingDocDetailsToExcel");
    }

    private void writeBoeContainerDetailsToExcel(List<BoeAdditionalContainerDetailsDocument>
                                                         containerDetailsList, String boeNo, Workbook workbook,
                                                 List<ExportReportTemplateMappingVO> templMappnigList, Map<String, Integer> lastRowIndexMap) {
        LOG.error("START >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeContainerDetailsToExcel");
        if (null != containerDetailsList && !containerDetailsList.isEmpty()) {

            SXSSFSheet impStatementSheet = (SXSSFSheet) workbook.getSheetAt(8);
            int index = 0;
            int lastRowIndex = lastRowIndexMap.get(TemplateCode.ADDITIONAL_CONTAINER_DETAILS.name());
            Map<String, CellStyle> cellStyleMap = new HashMap<>();
            for (BoeAdditionalContainerDetailsDocument containerDetail : containerDetailsList) {
                //Last row num is used to create new row and index is used to put value in sr.no column.
                //Set data to each cell.
                Row row = impStatementSheet.createRow(++lastRowIndex);

                Map<String, Object> keyValueMap = new HashMap<>();
                keyValueMap.put(BoeDetailsFields.SR_NO.getExcelMappingKey(), lastRowIndex-2);
                keyValueMap.put(BoeDetailsFields.BOE_NO.getExcelMappingKey(), boeNo);
                keyValueMap.put(BoeDetailsFields.INV_SN.getExcelMappingKey(), containerDetail.getInvNo());
                keyValueMap.put(BoeDetailsFields.ITEM_SN.getExcelMappingKey(), containerDetail.getItemNo());
                keyValueMap.put(BoeDetailsFields.ADDITIONAL_CONTAINER_NO.getExcelMappingKey(),
                        containerDetail.getAdditionalContainerNo());
                keyValueMap.put(BoeDetailsFields.ADDITIONAL_CONTAINER_TRUCK_NO.getExcelMappingKey(),
                        containerDetail.getAdditionalContainerTruckNo());
                keyValueMap.put(BoeDetailsFields.ADDITIONAL_CONTAINER_SEAL_NO.getExcelMappingKey(),
                        containerDetail.getAdditionalContainerSealNo());
                keyValueMap.put(BoeDetailsFields.ADDITIONAL_CONTAINER_FCL_LCL.getExcelMappingKey(),
                        containerDetail.getAdditionalContainerFclLcL());

                for (ExportReportTemplateMappingVO templateMappingVO : templMappnigList) {
                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, keyValueMap.get(templateMappingVO.getMappingKey()),
                            workbook, null, false, true, cellStyleMap);
                }
            }
            lastRowIndexMap.put(TemplateCode.ADDITIONAL_CONTAINER_DETAILS.name(), lastRowIndex);
        }
        LOG.error("END >> CLASS >> ExportReportProcessor >> METHOD >> writeBoeContainerDetailsToExcel");
    }

    /**
     * @param pan                PAN
     * @param txnId
     * @param reportGenerationId Report Generation ID
     * @param startPeriod        Start Period
     * @param endPeriod          End Period
     * @param isSftp             id SFTP Export
     * @param description        Description of the export
     * @param cursor             Mongo Db cursor with Document type
     * @param fileType           File type to be exported
     * @return Returns the File Object for the file which is generated
     * @throws PdfReaderException Throws Pdf Reader Exception
     * <AUTHOR> Nagare
     * @description </p>This method is a generic method to export all the file types
     * using the Document type of cursor and mapping that with the export template mapping.
     * @since 15-11-2023
     */
    public File exportReportFile(String pan, String txnId, String reportGenerationId, Date startPeriod, Date endPeriod,
                                 boolean isSftp, String description, MongoDbCursor<Document> cursor, PdfFileType fileType
    ) throws PdfReaderException {
        LOG.info("START >> CLASS >> ExportReportProcessor >> METHOD >> exportReportFile >> PAN >> " + pan +
                " >> FILE_TYPE >> " + fileType.name() + " >> REPORT_GENERATION_ID >> " + reportGenerationId +
                " >> START+PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " +
                description);
        ExportReportsManagerVO exportReportsManagerVO;

        if (StringUtils.isNotBlank(reportGenerationId)) {
            exportReportsManagerVO = service.getReportsManagerRepo().findByFileId(reportGenerationId);
        } else if (null != cursor) {

            //If the report is generated already then get the same report and do not generate new report.
            if (cursor.getCount() > 0) {
                //Generate the report with the error record and export it.
                exportReportsManagerVO = service.checkAndAddEntryIntoExportReportManager(pan,
                        StringUtils.isNotBlank(txnId) ? txnId : UUID.randomUUID().toString(),
                        fileType.name(), null,
                        TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name(), startPeriod, endPeriod,
                        null, cursor.getCount(), description);

                try (Workbook workbook = FileHelper.getSXSSFWorkbookForFile(fileType.getExcelFileName())) {

                    writeDataToExcel(cursor, workbook, fileType.name(), fileType.getLastRowIndex());

                    String fileNameWithExtension = FileHelper.getFileNameWithExtension(fileType.getExcelFileName(), pan);

                    String folderPathToStoreReport = FileHelper.getLocalFolderPath(pan, FileHelper.EXPORT_REPORT, fileType.name(), isSftp);

                    //4. Store it to s3 or local based on the property.
                    String filePathExport = FileHelper.exportAndUploadFile(pan, workbook, fileNameWithExtension,
                            folderPathToStoreReport, exportReportsManagerVO.getReportType(), isSftp);


                    //5. Update status in exim export report manager table with report generated status and file details.
                    exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_COMPLETED.name());
                    exportReportsManagerVO.setFileId(UUID.randomUUID().toString());
                    exportReportsManagerVO.setReportFilLoc(filePathExport);
                    exportReportsManagerVO.setReportName(fileNameWithExtension);
                    service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
                } catch (Exception e) {
                    LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportReportFile >>" +
                            " Error while exporting BOE report >> e >> ", e);
                    exportReportsManagerVO.setStatus(TransactionStatus.REPORT_GENERATION_FAILED.name());
                    service.saveOrUpdateEximExportReportManager(exportReportsManagerVO);
                    throw new PdfReaderException(ResponseCode.ERROR_WHILE_EXPORTING_REPORT, ResponseMessage.ERROR_WHILE_EXPORTING_REPORT);
                }
            } else {
                LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportReportFile >> PAN >> " + pan +
                        " >> FILE_TYPE >> " + fileType.name() + " >> REPORT_GENERATION_ID >> " + reportGenerationId +
                        " >> START+PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " +
                        description + " >> Error while exporting " + fileType.name() + " report >> No data found for report generation");
                throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.DATA_NOT_FOND);
            }
        } else {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportReportFile >> PAN >> " + pan +
                    " >> FILE_TYPE >> " + fileType.name() + " >> REPORT_GENERATION_ID >> " + reportGenerationId +
                    " >> START+PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " +
                    description + " >> Error while exporting " + fileType.name() + " report >> Data Not found");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        if (null == exportReportsManagerVO) {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportReportFile >>PAN >> " + pan +
                    " >> FILE_TYPE >> " + fileType.name() + " >> REPORT_GENERATION_ID >> " + reportGenerationId +
                    " >> START+PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " +
                    description + " >> Error while exporting " + fileType.name() + " report >> Export details not found.");
            throw new PdfReaderException(ResponseCode.DATA_NOT_FOUND, ResponseMessage.FILE_NOT_FOUND);
        }

        if (!TransactionStatus.REPORT_GENERATION_COMPLETED.name().equals(exportReportsManagerVO.getStatus())) {
            LOG.error("ERROR >> CLASS >> ExportReportProcessor >> METHOD >> exportReportFile >>PAN >> " + pan +
                    " >> FILE_TYPE >> " + fileType.name() + " >> REPORT_GENERATION_ID >> " + reportGenerationId +
                    " >> START+PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " +
                    description + " >> Error while exporting " + fileType.name() + " report >> Report generation not complete.");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.REPORT_NOT_GENERATED);
        }

        LOG.info("END >> CLASS >> ExportReportProcessor >> METHOD >> exportReportFile >> PAN >> " + pan +
                " >> FILE_TYPE >> " + fileType.name() + " >> REPORT_GENERATION_ID >> " + reportGenerationId +
                " >> START+PERIOD >> " + startPeriod + " >> END_PERIOD >> " + endPeriod + " >> DESCRIPTION >> " +
                description);
        return StreamHelper.prepareExcelFile(exportReportsManagerVO, isSftp);
    }

    private void writeDataToExcel(MongoDbCursor<Document> cursor, Workbook workbook, String fileType, int lastRow) {

        List<ExportReportTemplateMappingVO> templMappnigList = service.getExportReportTemplateMappingRepo().
                findAllByRefId(TemplateCode.valueOf(fileType).getId());

        List<Document> documentsList;
        AtomicInteger index = new AtomicInteger(0);
        AtomicInteger lastRowIndex = new AtomicInteger(lastRow);
        Map<String, CellStyle> cellStyleMap = new HashMap<>();
        SXSSFSheet sheet = (SXSSFSheet) workbook.getSheetAt(0);
        while (true) {
            documentsList = cursor.getRecords(BATCH_SIZE);

            if (documentsList.isEmpty()) {
                break;
            }

            documentsList.forEach(doc -> {
                Row row = sheet.createRow(lastRowIndex.incrementAndGet());

                templMappnigList.forEach(templateMappingVO -> {
                    Object value = null;
                    if (templateMappingVO.getMappingKey().equals(BoeDetailsFields.SR_NO.getExcelMappingKey())) {
                        value = index.incrementAndGet();
                    } else {
                        String documentKey = getDocumentKeyFromExcelMappingKey(fileType, templateMappingVO.getMappingKey());
                        if (doc.containsKey(documentKey)) {
                            value = doc.get(documentKey);
                        }
                    }

                    FileHelper.setDataToWorkBookCell(templateMappingVO, row, value, workbook, null,
                            false, true, cellStyleMap);
                });
            });
        }
    }

    private String getDocumentKeyFromExcelMappingKey(String fileType, String excelMappingKey) {
        String docMappingKey = null;
        switch (PdfFileType.valueOf(fileType)) {
            case SHIPPING_BILL: {
                docMappingKey = SBInvoiceDetailsFields.getValueFromExcelMappingKey(excelMappingKey);
                if (StringUtils.isBlank(docMappingKey)) {
                    docMappingKey = SBItemDetailsFields.getValueFromExcelMappingKey(excelMappingKey);
                }
                break;
            }
            case BILL_OF_ENTRY: {
                docMappingKey = BoeDetailsFields.getValueFromExcelMappingKey(excelMappingKey);
                break;
            }
            case BILL_OF_ENTRY_DUTY_DRAWBACK: {
                docMappingKey = BoeDutyDrawbackDetailsFields.getValueFromExcelMappingKey(excelMappingKey);
                break;
            }
            case BILL_OF_ENTRY_MOOWR: {
                docMappingKey = BoeMoowrDetailsFields.getValueFromExcelMappingKey(excelMappingKey);
                break;
            }
        }

        return docMappingKey;
    }
}
