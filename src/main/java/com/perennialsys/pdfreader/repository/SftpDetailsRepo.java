package com.perennialsys.pdfreader.repository;

import com.perennialsys.pdfreader.vo.SftpDetailsVO;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface SftpDetailsRepo extends CrudRepository<SftpDetailsVO, Long> {

    List<SftpDetailsVO> findAllByIsActiveOrderByUpdatedAtAsc(boolean isActive);

    SftpDetailsVO findByPanAndIsActive(String pan, boolean isActive);

}
