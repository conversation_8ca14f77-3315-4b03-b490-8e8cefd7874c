package com.perennialsys.pdfreader.repository;

import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface PdfReaderPdfFieldStrategyMappingRepo extends CrudRepository<PdfFieldStrategyMappingVO, Long> {
    List<PdfFieldStrategyMappingVO> findAllBySectionDetails_SectionKeyAndFileType(String sectionKey, String fileType);

    PdfFieldStrategyMappingVO findFirstByMappingKeyAndSectionDetails_SectionKeyAndFileType(String mappingKey, String sectionKey, String fileType);

}
