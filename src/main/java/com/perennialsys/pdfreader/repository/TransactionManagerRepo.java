package com.perennialsys.pdfreader.repository;

import com.perennialsys.pdfreader.constants.DBTables;
import com.perennialsys.pdfreader.vo.TransactionManagerVO;
import org.apache.commons.lang3.builder.EqualsExclude;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface TransactionManagerRepo extends CrudRepository<TransactionManagerVO, Long> {

    @Query(value = "from TransactionManagerVO  where txnId=?1 and type=?2 and pan=?3")
    TransactionManagerVO getTxnByTxnAndTypeAndPan(String txnId, String txnType, String pan);

    @Query(value = "SELECT count(*) FROM " + DBTables.TRANSACTION_MANAGER_TBL + " where  STATUS=?1 and TYPE in ?2", nativeQuery = true)
    int getTxnCountByStatusAndType(String status, List<String> txnTypeList);

    List<TransactionManagerVO> findAllByStatusAndTypeIn(String status, List<String> typeList);

    List<TransactionManagerVO> findAllByStatusAndTypeInOrderByUpdatedAtAsc(String status, List<String> typeList);

    List<TransactionManagerVO> findAllByTxnIdAndPanAndType(String txnId, String pan, String type);

    TransactionManagerVO findFirstByTxnIdAndPan(String txnId, String pan);

    TransactionManagerVO getFirstByPanAndTypeOrderByUpdatedAtAsc(String pan, String type);

    @Query(nativeQuery = true, value = "select fud.FILE_TYPE, fud.FILE_NAME, tm.STATUS, tm.UPDATED_AT, tm.TXN_ID, " +
            "tm.TYPE, tm.OTHER_DETAILS, tm.USER_NAME from "+DBTables.TRANSACTION_MANAGER_TBL+
            " tm right join "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" fud on tm.TXN_ID = fud.TXN_ID where " +
            "fud.FILE_TYPE = ?2 and tm.PAN =?1 order by tm.ID desc limit 1")
    List<Object[]> getLatestTxnFromTypeAndPan(@Param("1") String pan, @Param("2") String fileType);

    @Query(nativeQuery = true, value = "select distinct (tm.TXN_ID), fud.FILE_TYPE, tm.STATUS, tm.UPDATED_AT, " +
            " tm.CREATED_AT, tm.OTHER_DETAILS, tm.ID, tm.USER_NAME from "+DBTables.TRANSACTION_MANAGER_TBL+
            " tm right join "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" fud on fud.TXN_ID = tm.TXN_ID where " +
            "fud.FILE_TYPE = ?2 and tm.PAN =?1 order by tm.ID desc limit ?3, ?4")
    List<Object[]> getTxnsFromTypeAndPan(@Param("1") String pan, @Param("2") String fileType,
                                         @Param("3")int skip, @Param("4")int limit);

    @Transactional
    @Modifying
    @Query(value = "update TransactionManagerVO set status = ?1, otherDetails = ?2 where txnId =?3")
    void updateByTxnId(@Param("1") String status, @Param("2") String otherDetails, @Param("3") String txnId);
}
