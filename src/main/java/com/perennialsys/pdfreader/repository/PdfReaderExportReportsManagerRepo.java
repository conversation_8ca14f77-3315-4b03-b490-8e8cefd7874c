package com.perennialsys.pdfreader.repository;

import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PdfReaderExportReportsManagerRepo extends CrudRepository<ExportReportsManagerVO, Long> {

    ExportReportsManagerVO findFirstByTxnIdAndPanAndReportTypeAndStatusInOrderByCreatedAtDesc(String txnId, String pan,
                                                                                              String reportType,
                                                                                              List<String> statusList);

    ExportReportsManagerVO findByTxnIdAndPanAndReportTypeAndStatus(String txnId, String pan, String reportType,
                                                                   String status);
    ExportReportsManagerVO findByTxnIdAndSubTxnIdAndPanAndReportTypeAndStatus(String txnId, String subTxnID, String pan,
                                                                              String reportType, String status);

    ExportReportsManagerVO findByFileId(String fileId);
    ExportReportsManagerVO findByFileIdAndPan(String fileId, String pan);

    List<ExportReportsManagerVO> findAllByPanAndReportTypeOrderByIdDesc(String pan, String reportType,
                                                                        Pageable pageable);

    List<ExportReportsManagerVO> findAllByPanAndReportTypeInOrderByIdDesc(String pan, List<String> reportTypeList,
                                                                        Pageable pageable);

    int countAllByPanAndReportTypeInOrderByIdDesc(String pan, List<String> reportTypeList);

    int countAllByStatus(String status);

    List<ExportReportsManagerVO> findAllByStatusOrderByIdAsc(String status, Pageable pageable);
}
