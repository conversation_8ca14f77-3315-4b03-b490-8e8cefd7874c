package com.perennialsys.pdfreader.repository;

import com.perennialsys.pdfreader.vo.FieldIdentificationStrategyVO;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface FieldIdentificationStrategyRepo extends CrudRepository<FieldIdentificationStrategyVO, Long> {

    List<FieldIdentificationStrategyVO> findAllByPanAndFileTypeAndIsActive(String pan, String fileType, boolean isActive);

    FieldIdentificationStrategyVO findFirstByPanAndFileTypeAndTargetField(String pan, String fileType, String targetField);
    List<FieldIdentificationStrategyVO> findAllByPanAndFileType(String pan, String fileType);
    List<FieldIdentificationStrategyVO> findAllByPan(String pan);

    @Transactional
     void deleteByPanAndFileTypeAndTargetField(String pan, String fileType, String targetField);
}
