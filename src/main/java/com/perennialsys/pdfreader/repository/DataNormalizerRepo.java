package com.perennialsys.pdfreader.repository;

import com.perennialsys.pdfreader.vo.DataNormalizerVO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DataNormalizerRepo extends CrudRepository<DataNormalizerVO, Long> {

    @Override
    @Query("From DataNormalizerVO")
    List<DataNormalizerVO> findAll();

}
