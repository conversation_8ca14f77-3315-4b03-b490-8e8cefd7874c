package com.perennialsys.pdfreader.repository;

import com.perennialsys.pdfreader.constants.DBTables;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface FileUploadDetailsRepo extends CrudRepository<FileUploadDetailsVO, Long> {

    FileUploadDetailsVO findFirstByPanAndTxnIdAndSubTxnId(String pan, String txnId, String subTxnId);

    List<FileUploadDetailsVO> findAllByStatusInOrderByIdAsc(List<String> status);
    int countByStatusIn(List<String> status);
    int countByTxnIdAndStatusIn(String txnId, List<String> status);
    int countByPanAndFileTypeAndStatusIn(String pan, String fileType, List<String> status);
    long countByPanAndFileTypeAndStatusInAndTxnId(String pan, String fileType, List<String> status, String txnID);

    @Query(nativeQuery = true, value = "select count(1), STATUS, IS_DELETED from "+ DBTables.TBL_FILE_UPLOAD_DETAILS_TBL +
            " where TXN_ID = ?1 group by STATUS, IS_DELETED")
    List<Object[]> countByTxnId(@Param("1") String txnId);

    List<FileUploadDetailsVO> findAllByPanAndTxnIdAndStatusInAndIsDeletedOrderByIdDesc(String pan, String txnId, List<String> statusList,
                                                                        Pageable pageable, boolean isDeleted);

    FileUploadDetailsVO findFirstByPanAndSubTxnId(String pan, String subTxnId);
    List<FileUploadDetailsVO> findAllByPanAndTxnIdAndIsDeleted(String pan, String txnId, boolean isDeleted);
    List<FileUploadDetailsVO> findAllByPanAndTxnIdAndFileType(String pan, String txnId, String fileType);

    List<FileUploadDetailsVO> findAllByPanAndTxnIdAndStatusOrderByIdDesc(String pan, String txnId, String Status, Pageable pageable);

    @Query(nativeQuery = true, value = "select count(distinct (TXN_ID)) from "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+
            " where PAN = ?1 and FILE_TYPE = ?2")
    int getFileUploadTxnCount(String pan, String fileType);

    List<FileUploadDetailsVO> findAllByPanAndSubTxnIdInOrderByIdDesc(String pan, List<String> subTxnIdList);

    @Query(nativeQuery = true, value = "select SUB_TXN_ID, TXN_ID from "+DBTables.TBL_FILE_UPLOAD_DETAILS_TBL+" where TXN_ID =?1 and PAN =?2 " +
            "and FILE_TYPE =?3 and STATUS =?4 and IS_DELETED =?5")
    List<Object[]> getSubTxnIdsByTxnIDPanAndFileTypeAndStatus(@Param("1") String txnId, @Param("2") String pan, @Param("3") String fileType,
                                                         @Param("4") String status, @Param("5") boolean isDeleted);

    @Transactional
    @Modifying
    @Query(value = "update FileUploadDetailsVO set status = ?1, updatedAt = now() where subTxnId in ?2")
    void updateStatusFromSubTxnId(@Param("1") String status,@Param("2") List<String> subTxnIdList);
}
