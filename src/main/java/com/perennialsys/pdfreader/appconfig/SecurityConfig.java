package com.perennialsys.pdfreader.appconfig;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;


@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Override
    protected void configure(HttpSecurity http) throws Exception{
        http.csrf().disable()
            .authorizeRequests()
                .anyRequest().permitAll()
            .and()
            .headers()
                .frameOptions().sameOrigin()
                .httpStrictTransportSecurity().maxAgeInSeconds(31536000).includeSubDomains(true)
                .and().contentSecurityPolicy("default-src 'self'; img-src * data:; style-src * 'unsafe-inline'");
    }

}
