package com.perennialsys.pdfreader.appconfig;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * <AUTHOR>
 * <p>
 * Class for configuration of the Database with hibernate
 */
@Configuration
@EnableTransactionManagement
@PropertySource(value = {"classpath:db.properties"})
@SuppressWarnings({"squid:S1258"})
public class HibernateConfiguration {

    private static final int GENSALT_LOG2_ROUNDS = 12;

    private final Environment environment;

    @Autowired
    public HibernateConfiguration(Environment environment) {
        this.environment = environment;
    }


    /**
     * @return datasource
     */
    @Bean
    @Qualifier("pdfReaderDataSource")
    @Primary
    public DataSource pdfReaderDataSource() {

        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setDriverClassName(environment.getRequiredProperty("hibernate.connection.driver_class"));
        hikariConfig.setJdbcUrl(environment.getRequiredProperty("spring.datasource.url"));
        hikariConfig.setUsername(environment.getRequiredProperty("spring.datasource.username"));
        hikariConfig.setPassword(environment.getRequiredProperty("spring.datasource.password"));

        hikariConfig.setMinimumIdle(Integer.parseInt(environment.getRequiredProperty("spring.datasource.hikari.minimumIdle")));
        hikariConfig.setMaximumPoolSize(Integer.parseInt(environment.getRequiredProperty("spring.datasource.hikari.maximumPoolSize")));
        hikariConfig.setIdleTimeout(Long.parseLong(environment.getRequiredProperty("spring.datasource.hikari.idleTimeout")));
        hikariConfig.setPoolName(environment.getRequiredProperty("spring.datasource.hikari.pool-name"));
        hikariConfig.setMaxLifetime(Long.parseLong(environment.getRequiredProperty("spring.datasource.hikari.maxLifetime")));
        hikariConfig.setConnectionTimeout(Long.parseLong(environment.getRequiredProperty("spring.datasource.hikari.connectionTimeout")));
        hikariConfig.setLeakDetectionThreshold(Long.parseLong(environment.getRequiredProperty("spring.datasource.hikari.leak-detection-threshold")));

        return new HikariDataSource(hikariConfig);
    }

    @Bean
    public JpaTransactionManager transactionManager() {
        final JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setDataSource(pdfReaderDataSource());
        return transactionManager;
    }

    /**
     * @return encoded password
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(GENSALT_LOG2_ROUNDS, null);
    }

    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() {
        //JpaVendorAdapteradapter can be autowired as well if it's configured in application properties.
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setGenerateDdl(false);

        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setJpaVendorAdapter(vendorAdapter);
        factory.setJpaProperties(hibernateProperties());
        //Add package to scan for entities.
        factory.setPackagesToScan("com.perennialsys.pdfreader.vo");
        factory.setDataSource(pdfReaderDataSource());
        return factory;
    }

    @Bean
    public PersistenceExceptionTranslationPostProcessor exceptionTranslation() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

    private Properties hibernateProperties() {
        Properties properties = new Properties();
        properties.put("hibernate.dialect", environment.getRequiredProperty("hibernate.dialect"));
        properties.put("hibernate.connection.driver_class",
                environment.getRequiredProperty("hibernate.connection.driver_class"));
        properties.put("hibernate.connection.verifyServerCertificate",
                environment.getRequiredProperty("hibernate.connection.verifyServerCertificate"));
        properties.put("hibernate.connection.useSSL",
                environment.getRequiredProperty("hibernate.connection.useSSL"));
        properties.put("hibernate.connection.requireSSL",
                environment.getRequiredProperty("hibernate.connection.requireSSL"));
        properties.put("hibernate.format_sql",
                environment.getRequiredProperty("hibernate.format_sql"));
        properties.put("hibernate.show_sql",
                environment.getRequiredProperty("hibernate.show_sql"));
        properties.put("hibernate.hbm2ddl.auto",
                environment.getRequiredProperty("hibernate.hbm2ddl.auto"));
        properties.put("hibernate.jdbc.batch_size",
                environment.getRequiredProperty("hibernate.jdbc.batch_size"));
        properties.put("hibernate.current_session_context_class",
                environment.getRequiredProperty("hibernate.current_session_context_class"));
        properties.put("hibernate.cache.use_second_level_cache",
                environment.getRequiredProperty("hibernate.cache.use_second_level_cache"));
        properties.put("hibernate.cache.use_query_cache",
                environment.getRequiredProperty("hibernate.cache.use_query_cache"));
        properties.put("hibernate.cache.region.factory_class",
                environment.getRequiredProperty("hibernate.cache.region.factory_class"));
        properties.put("hibernate.connection.isolation",
                environment.getRequiredProperty("hibernate.connection.isolation"));
        properties.put("hibernate.c3p0.min_size",
                environment.getRequiredProperty("hibernate.c3p0.min_size"));
        properties.put("hibernate.c3p0.max_size",
                environment.getRequiredProperty("hibernate.c3p0.max_size"));
        properties.put("hibernate.c3p0.timeout",
                environment.getRequiredProperty("hibernate.c3p0.timeout"));
        properties.put("hibernate.c3p0.max_statements",
                environment.getRequiredProperty("hibernate.c3p0.max_statements"));
        properties.put("hibernate.c3p0.idle_test_period",
                environment.getRequiredProperty("hibernate.c3p0.idle_test_period"));
        properties.put("hibernate.c3p0.acquireRetryAttempts",
                environment.getRequiredProperty("hibernate.c3p0.acquireRetryAttempts"));
        properties.put("hibernate.c3p0.breakOnAcquireFailure",
                environment.getRequiredProperty("hibernate.c3p0.breakOnAcquireFailure"));
        properties.put("hibernate.c3p0.preferredTestQuery",
                environment.getRequiredProperty("hibernate.c3p0.preferredTestQuery"));
//        properties.put("hibernate.connection.provider_class",
//                environment.getRequiredProperty("hibernate.connection.provider_class"));
        return properties;
    }
}
