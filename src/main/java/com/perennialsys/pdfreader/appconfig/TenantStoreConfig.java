package com.perennialsys.pdfreader.appconfig;

import com.perennialsys.pdfreader.bean.TenantStore;
import org.springframework.aop.framework.ProxyFactoryBean;
import org.springframework.aop.target.ThreadLocalTargetSource;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;

import java.util.HashMap;

@Configuration
public class TenantStoreConfig {

    @Bean(name = "tenantStore")
    @Scope(scopeName = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    public TenantStore tenantStore(){
        TenantStore tenantStore = new TenantStore();
        tenantStore.setTenantDtlsMap(new HashMap<>());
        return tenantStore;
    }

    @Bean(destroyMethod = "destroy")
    public ThreadLocalTargetSource threadLocalTargetSource(){
        ThreadLocalTargetSource threadLocalTargetSource = new ThreadLocalTargetSource();
        threadLocalTargetSource.setTargetBeanName("tenantStore");

        return threadLocalTargetSource;
    }

    @Primary
    @Bean(name = "proxiedThreadLocalTargetSource")
    public ProxyFactoryBean proxiedThreadLocalTargetSource(ThreadLocalTargetSource threadLocalTargetSource) {
        ProxyFactoryBean proxyFactoryBean = new ProxyFactoryBean();
        proxyFactoryBean.setTargetSource(threadLocalTargetSource);
        return proxyFactoryBean;
    }


}
