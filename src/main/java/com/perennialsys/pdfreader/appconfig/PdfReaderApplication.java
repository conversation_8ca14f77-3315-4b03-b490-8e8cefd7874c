package com.perennialsys.pdfreader.appconfig;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@EntityScan(basePackages = "com.perennialsys.pdfreader.vo")
@EnableJpaRepositories(basePackages = "com.perennialsys.pdfreader.repository")
@SpringBootApplication(scanBasePackages = "com.perennialsys.pdfreader")
public class PdfReaderApplication {
    public static void main(String[] args) {
        SpringApplication.run(PdfReaderApplication.class, args);
    }

}
