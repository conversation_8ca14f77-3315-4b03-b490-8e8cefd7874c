package com.perennialsys.pdfreader.appconfig;


import com.perennialsys.pdfreader.db.mongo.MongoDaoImpl;
import com.perennialsys.pdfreader.interceptor.AuthenticateInterceptor;
import com.perennialsys.pdfreader.util.PropertyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Properties;

@Configuration
public class AppConfig implements WebMvcConfigurer {

    @Autowired
    AuthenticateInterceptor authenticateInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authenticateInterceptor)
                .excludePathPatterns("/",
                        "/swagger-ui.html/**", "swagger-ui.html#!/**",
                        "/swagger-ui.html#/**", "/v3/api-docs/**", "/swagger-ui/**",
                        "/swagger-resources/**", "/webjars/**").excludePathPatterns("/swagger-ui.html");
    }

    @Bean
    public MongoDaoImpl mongoDao() {
        Properties prop = PropertyUtil.getProperties("mongoDb");
        String host = prop.getProperty("mongo_host");
        String port = prop.getProperty("mongo_port");
        String dbName = prop.getProperty("mongo_database");
        String mongoClientURI;
        boolean isAuthenticate = Boolean.parseBoolean(prop.getProperty("is_authenticate", "false"));
        if (isAuthenticate) {
            String username = prop.getProperty("mongo_username");
            String password = prop.getProperty("mongo_password");
            mongoClientURI = "mongodb://" + username + ":" + password + "@" + host + ":" + port + "/" + dbName;
        } else {
            mongoClientURI = "mongodb://" + host + ":" + port + "/" + dbName;
        }
        return new MongoDaoImpl(mongoClientURI, dbName);
    }


}
