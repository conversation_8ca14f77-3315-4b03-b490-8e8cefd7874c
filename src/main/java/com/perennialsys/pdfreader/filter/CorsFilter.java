package com.perennialsys.pdfreader.filter;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

@Component
public class CorsFilter implements Filter {
    Logger LOG = Logger.getLogger(CorsFilter.class);

    //TODO [08-08-2023] [Mr<PERSON><PERSON> Nagar<PERSON>] - This code needs to be removed while deploying the service on prod as
    // this will allow the requests from any host.
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) {
        try{
            HttpServletResponse response = (HttpServletResponse) res;
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD");
            response.setHeader("Access-Control-Allow-Headers", "*");
            if(chain == null){
                LOG.error("##################################### Filter chain is null ############################################");
            }else{
                chain.doFilter(req, res);
            }
        }catch (Exception e){
            LOG.error("##################################### Error occurred ############################################", e);
        }
    }

    public void init(FilterConfig filterConfig) {}

    public void destroy() {}
}
