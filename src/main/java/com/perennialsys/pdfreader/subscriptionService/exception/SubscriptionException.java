package com.perennialsys.pdfreader.subscriptionService.exception;

public class SubscriptionException extends Exception {

    private String errorMessage;

    private String errorCode;


    public SubscriptionException(String errorCode, String errorMessage) {
        super();
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public SubscriptionException(String s, String errorMessage, String errorCode) {
        super(s);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public SubscriptionException(String s, Throwable throwable, String errorMessage, String errorCode) {
        super(s, throwable);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public SubscriptionException(Throwable throwable, String errorMessage, String errorCode) {
        super(throwable);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public SubscriptionException(Throwable e) {
        super(e);
    }

    public SubscriptionException(String errorMessage) {
        super();
        this.errorMessage = errorMessage;
    }

    public SubscriptionException(String errorMessage, Throwable e) {
        super(e);
        this.errorMessage = errorMessage;
    }


    public String getErrorMessge() {
        return errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }
}
