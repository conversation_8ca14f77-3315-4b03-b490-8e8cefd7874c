package com.perennialsys.pdfreader.subscriptionService.handler.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.perennialsys.pdfreader.subscriptionService.constants.ApiParameters;
import com.perennialsys.pdfreader.subscriptionService.constants.ApiUrls;
import com.perennialsys.pdfreader.subscriptionService.constants.ResponseCode;
import com.perennialsys.pdfreader.subscriptionService.constants.ResponseMessage;
import com.perennialsys.pdfreader.subscriptionService.dto.UserDetailsDto;
import com.perennialsys.pdfreader.subscriptionService.handler.IAuthHandler;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.subscriptionService.helper.HttpClientHandler;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Nagare
 * @since 23/11/2023
 *
 * @Description </p>This class will be responsible to handle all the authentication and user related requests
 * to the Subscription service.
 */
@Slf4j
@Service
@PropertySource("classpath:subscriptionService.properties")
public class AuthHandlerImpl implements IAuthHandler {

    private final Environment environment;
    private final HttpClientHandler httpClientHandler;

    @Autowired
    public AuthHandlerImpl(Environment environment, HttpClientHandler httpClientHandler){
        this.environment = environment;
        this.httpClientHandler = httpClientHandler;
    }


    @Override
    public UserDetailsDto validateSessionToken(String sessionToken, String action, String pan) throws SubscriptionException {
        log.info("START >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> {} >> PAN >> {}",
                sessionToken, action, pan);

        UserDetailsDto userDetails = null;
        String requestUrl = environment.getProperty("subscription.service.base.url") + ApiUrls.AUTHENTICATE;
        Map<String, String> headers = new HashMap<>();
        headers.put(ApiParameters.SESSION_TOKEN, sessionToken);
        headers.put(ApiParameters.ACTION, action);

        HttpClientHandler.getDefaultHeaders(headers, sessionToken, environment.getProperty("subscription.service.app.key"),
                environment.getProperty("subscription.product.name"), pan);

        ResponseEntity<JsonNode> responseEntity = httpClientHandler.doPostMethodCall(requestUrl, headers, null, null);
        JSONObject jsonResponse = HttpClientHandler.parseResponse(responseEntity);

        if(null != jsonResponse && (jsonResponse.has("status-code") || jsonResponse.has("statusCode"))) {
            long statusCode = 0;

            if(jsonResponse.has("status-code")){
                statusCode = jsonResponse.getLong("status-code");
            } else if(jsonResponse.has("statusCode")){
                statusCode = jsonResponse.getLong("statusCode");
            }

            if(statusCode > 0){
                //Success Response
                try {
                    userDetails = new ObjectMapper().readValue(jsonResponse.get("data").toString(), UserDetailsDto.class);
                }catch (IOException e){
                    log.error("ERROR >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> PAN >> {} " +
                                    ">> {} >> {}",
                    sessionToken, action, pan, ResponseMessage.UNABLE_TO_PROCESS_REQUEST, e);
                    throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
                }
            }else {
                log.error("ERROR >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> PAN >> {} " +
                                ">> {} >> {}",
                sessionToken, action, pan, jsonResponse.getString("error-message"));
                throw new SubscriptionException(ResponseCode.UNAUTHORIZED_ACCESS, jsonResponse.getString("error-message"));
            }
        }else {
            log.error("ERROR >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> PAN >> {} " +
                            ">> {} >> {}",sessionToken, action, pan, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
            throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
        }

        log.info("END >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> {} >> PAN >> {}",
                sessionToken, action, pan);

        return userDetails;
    }

    @Override
    public List<UserDetailsDto> getUsersByUserId(String sessionToken, List<Long> userIdList, String action,
                                                 String pan) throws SubscriptionException {
        log.info("START >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> {} >> PAN >> {} >>" +
                " USER_ID_LIST >> {}", sessionToken, action, pan, userIdList);

        List<UserDetailsDto> userList;

        String requestUrl = environment.getProperty("subscription.service.base.url") + ApiUrls.AUTHENTICATE;
        Map<String, String> headers = new HashMap<>();
        headers.put(ApiParameters.SESSION_TOKEN, sessionToken);
        headers.put(ApiParameters.ACTION, action);

        HttpClientHandler.getDefaultHeaders(headers, sessionToken, environment.getProperty("subscription.service.app.key"),
                environment.getProperty("subscription.product.name"), pan);

        ResponseEntity<JsonNode> responseEntity = httpClientHandler.doGetMethodCall(requestUrl, headers, null);
        JSONObject jsonResponse = HttpClientHandler.parseResponse(responseEntity);

        if(null != jsonResponse && jsonResponse.has("status-code")){
            long statusCode = jsonResponse.getLong("status-code");
            if(statusCode > 0){
                //Success Response
                try {
                    userList = new Gson().fromJson(jsonResponse.get("data").toString(), new TypeToken<ArrayList<UserDetailsDto>>() {
                    }.getType());
                }catch (JsonSyntaxException e){
                    log.error("END >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> {} >> PAN >> {} >>" +
                " USER_ID_LIST >> {} >> {}", sessionToken, action, pan, userIdList, ResponseMessage.UNABLE_TO_PROCESS_REQUEST, e);
                    throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
                }
            }else {
                log.error("END >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> {} >> PAN >> {} >>" +
                " USER_ID_LIST >> {} >> {}", sessionToken, action, pan, userIdList, jsonResponse.getString("error-message"));
                throw new SubscriptionException(ResponseCode.UNAUTHORIZED_ACCESS, jsonResponse.getString("error-message"));
            }
        }else {
            log.error("END >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> {} >> PAN >> {} >>" +
                " USER_ID_LIST >> {} >> {}", sessionToken, action, pan, userIdList, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
            throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
        }

        log.info("END >> AuthHandler >> METHOD >> validateSessionToken >> SESSION_TOKEN >> {} >> ACTION >> {} >> PAN >> {} >>" +
                " USER_ID_LIST >> {}", sessionToken, action, pan, userIdList);
        return userList;
    }
}
