package com.perennialsys.pdfreader.subscriptionService.handler;

import com.perennialsys.pdfreader.subscriptionService.dto.UserDetailsDto;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;

import java.util.List;

/**
 * <AUTHOR>
 * @since 23/11/2023
 *
 * @Description </p>This inteface will be having all the methods required to handle all the authentication and user
 * related requests to the Subscription service.
 */
public interface IAuthHandler {

    UserDetailsDto validateSessionToken(String sessionToken, String action, String pan) throws SubscriptionException;

    List<UserDetailsDto> getUsersByUserId(String sessionToken, List<Long> userIdList, String action, String pan) throws SubscriptionException;
}