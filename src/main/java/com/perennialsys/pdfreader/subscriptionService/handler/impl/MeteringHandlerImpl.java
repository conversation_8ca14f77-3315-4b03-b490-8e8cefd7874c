package com.perennialsys.pdfreader.subscriptionService.handler.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.perennialsys.pdfreader.subscriptionService.constants.ApiActions;
import com.perennialsys.pdfreader.subscriptionService.constants.ApiParameters;
import com.perennialsys.pdfreader.subscriptionService.constants.ApiUrls;
import com.perennialsys.pdfreader.subscriptionService.constants.ResponseCode;
import com.perennialsys.pdfreader.subscriptionService.constants.ResponseMessage;
import com.perennialsys.pdfreader.subscriptionService.dto.MeteringDetailsDto;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.subscriptionService.handler.IMeteringHandler;
import com.perennialsys.pdfreader.subscriptionService.helper.HttpClientHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Nagare
 * @since 01-08-2024
 * @Description This class will be responsible to handle all the metering related requests
 */
@Slf4j
@Component
@PropertySource("classpath:subscriptionService.properties")
public class MeteringHandlerImpl implements IMeteringHandler {

    private final Environment environment;
    private final HttpClientHandler httpClientHandler;

    @Autowired
    public MeteringHandlerImpl(Environment environment, HttpClientHandler httpClientHandler){
        this.environment = environment;
        this.httpClientHandler = httpClientHandler;
    }


    /**
     * @param pan            PAN
     * @param event          Event
     * @param meteringEntity Metering entity
     * @param count          Count
     * @param successCount   success count
     * @param failedCount    Failure Count
     * @param otherDetails   Other details
     * @return Returns the updated metering details
     * @throws SubscriptionException Throws SubscriptionException
     * <AUTHOR> Nagare
     * @since 01-08-2024
     */
    @Override
    public MeteringDetailsDto addOrUpdateMeteringDetails(String pan, String event,
                                                         String meteringEntity, long count, long successCount,
                                                         long failedCount, String otherDetails)  throws SubscriptionException {

        log.info("START >> MeteringHandlerImpl >> METHOD >> addOrUpdateMeteringDetails >> PAN >> {}", pan);

        MeteringDetailsDto meteringDetails;
        if(StringUtils.isBlank(pan)){
            log.error("ERROR >> MeteringHandlerImpl >> METHOD >> addOrUpdateMeteringDetails >> PAN >> {} >> {}", pan,
                    ResponseMessage.MISSING_MANDATORY_HEADERS);
            throw new SubscriptionException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        if(StringUtils.isNotBlank(pan) && StringUtils.isNotBlank(event) && StringUtils.isNotBlank(meteringEntity)
                && (count > 0 || successCount > 0 || failedCount > 0)) {

            String productCode = environment.getProperty("subscription.product.name");
            String appKey = environment.getProperty("subscription.service.app.key");
            meteringDetails = new MeteringDetailsDto(pan, productCode, event, meteringEntity, count,
                    successCount, failedCount, otherDetails);
            String requestUrl = environment.getProperty("subscription.service.base.url") + ApiUrls.METERING_DETAILS;
            Map<String, String> headers = new HashMap<>();
            headers.put(ApiParameters.ACTION, ApiActions.METERING);
            HttpClientHandler.getDefaultHeaders(headers, null, appKey, productCode, pan);

            ResponseEntity<JsonNode> responseEntity = httpClientHandler.doPostMethodCall(requestUrl, headers, null,
                    meteringDetails.toString());

            JSONObject jsonResponse = HttpClientHandler.parseResponse(responseEntity);

            if (null != jsonResponse && (jsonResponse.has("status-code") || jsonResponse.has("statusCode"))) {
                long statusCode = 0;

                if(jsonResponse.has("status-code")){
                    statusCode = jsonResponse.getLong("status-code");
                } else if(jsonResponse.has("statusCode")){
                    statusCode = jsonResponse.getLong("statusCode");
                }

                if (statusCode > 0) {
                    if(jsonResponse.has("data")){
                        try {
                            meteringDetails = new ObjectMapper().readValue(jsonResponse.get("data").toString(),
                                    MeteringDetailsDto.class);
                        } catch (IOException e) {
                            log.error("ERROR >> MeteringHandlerImpl >> METHOD >> addOrUpdateMeteringDetails >> PAN >> {} >> {}",
                                    pan, ResponseMessage.UNABLE_TO_PROCESS_REQUEST, e);
                            throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST,
                                    ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
                        }
                    }else {
                        meteringDetails = null;
                    }
                } else {
                    log.error("ERROR >> MeteringHandlerImpl >> METHOD >> addOrUpdateMeteringDetails >> PAN >> {} >> {}", pan,
                            jsonResponse.getString("error-message"));
                    throw new SubscriptionException(ResponseCode.UNAUTHORIZED_ACCESS, jsonResponse.getString("error-message"));
                }
            } else {
                log.error("ERROR >> MeteringHandlerImpl >> METHOD >> addOrUpdateMeteringDetails >> PAN >> {} >> {}", pan,
                        ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
                throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST,
                        ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
            }
        }else {
            log.error("ERROR >> MeteringHandlerImpl >> METHOD >> addOrUpdateMeteringDetails >> PAN >> {} >> {}", pan,
                    ResponseMessage.INVALID_METERING_DETAILS);
            throw new SubscriptionException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_METERING_DETAILS);
        }

        log.info("END >> MeteringHandlerImpl >> METHOD >> addOrUpdateMeteringDetails >> PAN >> {}", pan);

        return meteringDetails;
    }
}
