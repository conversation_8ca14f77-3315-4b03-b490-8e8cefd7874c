package com.perennialsys.pdfreader.subscriptionService.handler;

import com.perennialsys.pdfreader.subscriptionService.dto.MeteringDetailsDto;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;

public interface IMeteringHandler {

    MeteringDetailsDto addOrUpdateMeteringDetails(String pan, String event,
                                                  String meteringEntity, long count, long successCount, long failedCount,
                                                  String otherDetails) throws SubscriptionException;

}
