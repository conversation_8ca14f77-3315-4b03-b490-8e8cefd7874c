package com.perennialsys.pdfreader.subscriptionService.helper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.subscriptionService.constants.ApiParameters;
import com.perennialsys.pdfreader.subscriptionService.constants.ResponseCode;
import com.perennialsys.pdfreader.subscriptionService.constants.ResponseMessage;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.vo.SubscriptionServiceRequestAuditLogVo;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> Nagare
 * @since 23/11/2023
 *
 * @Desription </p> This class will have all the Http calls related methods.
 */
@Log4j
@Component
public class HttpClientHandler {

    private final IService service;

    @Autowired
    HttpClientHandler(IService service){
        this.service = service;

    }

    public ResponseEntity<JsonNode> doPostMethodCall(String reqUrl, Map<String, String> headersMap,
                                                            Map<String, String> queryParams, String requestBody
    ) throws SubscriptionException {
        log.info("START >> HttpClientHelper >> METHOD >> doPostMethodCall >> URL >> "+reqUrl);
        RestTemplate restTemplate = new RestTemplate();

        reqUrl = addQueryParamsToUrl(reqUrl, queryParams);

        URI url;
        try {
            url = new URI(reqUrl);
        } catch (URISyntaxException e) {
            log.error("ERROR >> HttpClientHelper >> METHOD >> doPostMethodCall >> URL >> "+reqUrl+
                    " >> Error while creating URI", e);
            throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
        }

        HttpHeaders headers = createHeaders(headersMap);

        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        HttpEntity<?> entity;

        if(StringUtils.isNotBlank(requestBody)){
            entity = new HttpEntity<>(requestBody, headers);
        }else {
            entity = new HttpEntity<>(headers);
        }

        String pan = null;
        String appKey = null;
        String txnId = null;
        String action = null;

        if(null != headersMap){
            pan = headersMap.getOrDefault(ApiParameters.PAN, null);
            appKey = headersMap.getOrDefault(ApiParameters.PASS_KEY, null);
            txnId = headersMap.getOrDefault(ApiParameters.TXN_ID, null);
            action = headersMap.getOrDefault(ApiParameters.ACTION, null);
        }

        SubscriptionServiceRequestAuditLogVo auditLogVo = createAuditLogEntry(pan, txnId, appKey, reqUrl, action,
                headersMap, queryParams, requestBody);

        ResponseEntity<JsonNode> response;
        try {
             response = restTemplate.exchange(url, HttpMethod.POST, entity, JsonNode.class);
        }catch (HttpServerErrorException e){
            if(HttpStatus.INTERNAL_SERVER_ERROR.equals(e.getStatusCode())){
                String errorResponse = e.getResponseBodyAsString();
                if(StringUtils.isNotBlank(errorResponse)) {
                    try {
                        JsonNode errorJson = new ObjectMapper().readTree(errorResponse);

                        response = new ResponseEntity<>(errorJson, HttpStatus.OK);
                    }catch (IOException e1){
                        updateAuditLogEntry(auditLogVo, e.getStatusCode().toString(), errorResponse);
                        throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST,
                                ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
                    }
                }else {
                    updateAuditLogEntry(auditLogVo, e.getStatusCode().toString(), errorResponse);
                    throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST,
                            ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
                }
            }else {
                updateAuditLogEntry(auditLogVo, e.getStatusCode().toString(), e.getResponseBodyAsString());
                throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST,
                        ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
            }
        }
        log.info("END >> HttpClientHelper >> METHOD >> doPostMethodCall >> URL >> "+reqUrl+" >> RESPONSE >> "+response);
        updateAuditLogEntry(auditLogVo, String.valueOf(HttpStatus.OK.value()), null != response.getBody()
                ? response.getBody().toString() : null);

        return response;
    }

    public ResponseEntity<JsonNode> doGetMethodCall(String reqUrl, Map<String, String> headersMap,
                                                           Map<String, String> queryParams
    ) throws SubscriptionException {
        log.info("START >> HttpClientHelper >> METHOD >> doGetMethodCall >> URL >> "+reqUrl);
        RestTemplate restTemplate = new RestTemplate();

        reqUrl = addQueryParamsToUrl(reqUrl, queryParams);

        URI url;
        try {
            url = new URI(reqUrl);
        } catch (URISyntaxException e) {
            log.error("ERROR >> HttpClientHelper >> METHOD >> doGetMethodCall >> URL >> "+reqUrl+
                    " >> Error while creating URI", e);
            throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
        }

        HttpHeaders headers = createHeaders(headersMap);

        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        HttpEntity<?> entity = new HttpEntity<>(headers);

        String pan = null;
        String appKey = null;
        String txnId = null;
        String action = null;

        if(null != headersMap){
            pan = headersMap.getOrDefault(ApiParameters.PAN, null);
            appKey = headersMap.getOrDefault(ApiParameters.PASS_KEY, null);
            txnId = headersMap.getOrDefault(ApiParameters.TXN_ID, null);
            action = headersMap.getOrDefault(ApiParameters.ACTION, null);
        }

        SubscriptionServiceRequestAuditLogVo auditLogVo = createAuditLogEntry(pan, txnId, appKey, reqUrl, action,
                headersMap, queryParams, null);

        ResponseEntity<JsonNode> response;
        try {

            response = restTemplate.exchange(url, HttpMethod.GET, entity, JsonNode.class);

        }catch (HttpServerErrorException e){
            if(HttpStatus.INTERNAL_SERVER_ERROR.equals(e.getStatusCode())){
                String errorResponse = e.getResponseBodyAsString();
                if(StringUtils.isNotBlank(errorResponse)) {
                    try {
                        JsonNode errorJson = new ObjectMapper().readTree(errorResponse);

                        response = new ResponseEntity<>(errorJson, HttpStatus.OK);
                    }catch (IOException e1){
                        updateAuditLogEntry(auditLogVo, e.getStatusCode().toString(), errorResponse);
                        throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
                    }
                }else {
                    updateAuditLogEntry(auditLogVo, e.getStatusCode().toString(), errorResponse);
                    throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
                }
            }else {
                updateAuditLogEntry(auditLogVo, e.getStatusCode().toString(), e.getResponseBodyAsString());
                throw new SubscriptionException(ResponseCode.UNABLE_TO_PROCESS_REQUEST,
                        ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
            }
        }
        log.info("END >> HttpClientHelper >> METHOD >> doGetMethodCall >> URL >> "+reqUrl+" >> RESPONSE >> "+response);

        updateAuditLogEntry(auditLogVo, String.valueOf(HttpStatus.OK.value()), null != response.getBody()
                ? response.getBody().toString() : null);
        return response;
    }

    public static JSONObject parseResponse(ResponseEntity<JsonNode> response){
        JSONObject responseBody = null;

        JsonNode responseJson = response.getBody();
        if(null != responseJson) {
            String jsonString = responseJson.toString();
            responseBody = new JSONObject(jsonString);
        }

        return responseBody;
    }

    public static void getDefaultHeaders(Map<String, String> headerMap, String sessionToken, String appKey, String productName, String pan){
        //headerMap.put("Content-Type", ContentType.APPLICATION_JSON.getMimeType());
        headerMap.put(ApiParameters.PASS_KEY, appKey);

        if(StringUtils.isNotBlank(sessionToken)) {
            headerMap.put(ApiParameters.SESSION_TOKEN, sessionToken);
        }

        headerMap.put(ApiParameters.PRODUCT_NAME, productName);
        headerMap.put(ApiParameters.PAN, pan);
    }

    private static HttpHeaders createHeaders(Map<String, String> headersMap){
        log.info("START >> HttpClientHelper >> METHOD >> createHeaders >> HEADERS >> "+headersMap);

        HttpHeaders headers;

        if(null != headersMap && !headersMap.isEmpty()){
            headers = new HttpHeaders();
            headersMap.forEach(headers::add);
        } else {
            headers = null;
        }

        log.info("END >> HttpClientHelper >> METHOD >> createHeaders >> HEADERS >> "+headersMap);
        return headers;
    }

    private static String addQueryParamsToUrl(String url, Map<String, String> queryParams){
        log.info("START >> HttpClientHelper >> METHOD >> createHeaders >> URL >> "+url+" >> QUERY_PARAMS >> "+
                queryParams);

        StringBuilder urlBuilder = new StringBuilder(url);
        AtomicBoolean questionMarkAdded = new AtomicBoolean(false);

        if(null != queryParams && !queryParams.isEmpty()){
            queryParams.forEach((key, value) -> {
                if(url.contains("?") && questionMarkAdded.get()){
                    urlBuilder.append(key).append("=").append(value);
                }else {
                    urlBuilder.append("?").append(key).append("=").append(value);
                    questionMarkAdded.set(true);
                }
            });
        }

        log.info("START >> HttpClientHelper >> METHOD >> createHeaders >> URL >> "+url+" >> QUERY_PARAMS >> "+
                queryParams);
        return urlBuilder.toString();
    }

    private SubscriptionServiceRequestAuditLogVo createAuditLogEntry(String pan, String txnId, String appKey,
                                                                     String reqUrl, String action,
                                                                     Map<String, String> headersMap,
                                                                     Map<String, String> queryParams, String requestBody) {

        SubscriptionServiceRequestAuditLogVo auditLog = SubscriptionServiceRequestAuditLogVo.builder()
                .pan(pan)
                .action(action)
                .txnId(txnId)
                .apiKey(appKey)
                .resourcePath(reqUrl)
                .createdAt(new Date())
                .build();

        if(null != headersMap && !headersMap.isEmpty()){
            auditLog.setRequestHeaders(headersMap.toString());
        }

        if(null != queryParams && !queryParams.isEmpty()){
            auditLog.setRequestParams(queryParams.toString());
        }

        if(StringUtils.isNotBlank(requestBody)){
            auditLog.setRequestPayload(requestBody);
        }

        return service.getSubscriptionServiceRequestAuditLogRepo().save(auditLog);
    }

    private void updateAuditLogEntry(SubscriptionServiceRequestAuditLogVo auditLog, String status, String response){
        auditLog.setStatus(status);
        auditLog.setResponse(response);
        auditLog.setUpdatedAt(new Date());

        service.getSubscriptionServiceRequestAuditLogRepo().save(auditLog);
    }
}
