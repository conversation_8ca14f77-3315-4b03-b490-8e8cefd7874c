package com.perennialsys.pdfreader.subscriptionService.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;

/**
 * <AUTHOR> Na<PERSON>
 * @since 01-08-2024
 * @Description This DTO will be used to send the metering related details to the subscription service
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MeteringDetailsDto {

    @JsonProperty(value = "pan")
    private String pan;

    @JsonProperty(value = "product-name")
    private String productName;

    @JsonProperty(value = "event")
    private String event;

    @JsonProperty(value = "metering-entity")
    private String meteringEntity;

    @JsonProperty(value = "count")
    private long count;

    @JsonProperty(value = "success-count")
    private long successCount;

    @JsonProperty(value = "failure-count")
    private long failureCount;

    @JsonProperty(value = "other-details")
    private String otherDetails;

    @Override
    public String toString(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pan", this.pan);
        jsonObject.put("product-name", this.productName);
        jsonObject.put("event", this.event);
        jsonObject.put("metering-entity", this.meteringEntity);
        jsonObject.put("count", this.count);
        jsonObject.put("success-count", this.successCount);
        jsonObject.put("failure-count", this.failureCount);
        jsonObject.put("other-details", this.otherDetails);

        return jsonObject.toString();
    }


    /**
     * <AUTHOR> Nagare
     * @since 01-08-2024
     * @Descroiption This method validates the metering details passed in the method
     * @param meteringDetails Metering details DTO
     * @return Returns if the details passed are correct or not.
     */
    public static boolean validate(MeteringDetailsDto meteringDetails){

        //PAN, sessionToken, meteringEntity and event must not be blank.
        //any one of count, successCount or failedCount must be greater than 0.
        return null != meteringDetails && StringUtils.isNotBlank(meteringDetails.pan) && StringUtils.isNotBlank(meteringDetails.productName)
                && StringUtils.isNotBlank(meteringDetails.event) && StringUtils.isNotBlank(meteringDetails.meteringEntity)
                && (meteringDetails.count > 0 || meteringDetails.successCount > 0 || meteringDetails.failureCount > 0);
    }
}
