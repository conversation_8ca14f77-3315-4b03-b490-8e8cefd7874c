package com.perennialsys.pdfreader.subscriptionService.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.json.JSONObject;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDetailsDto {
    @JsonProperty(value = "userId")
    private long userId;
    @JsonProperty(value = "uuid")
    private String uuid;
    @JsonProperty(value = "firstName")
    private String firstName;
    @JsonProperty(value = "lastName")
    private String lastName;
    @JsonProperty(value = "email")
    private String email;
    @JsonProperty(value = "createdAt")
    private String createdAt;

    @Override
    public String toString(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", this.userId);
        jsonObject.put("uuid", this.uuid);
        jsonObject.put("firstName", this.firstName);
        jsonObject.put("lastName", this.lastName);
        jsonObject.put("email", this.email);
        jsonObject.put("createdAt", this.createdAt);

        return jsonObject.toString();
    }
}
