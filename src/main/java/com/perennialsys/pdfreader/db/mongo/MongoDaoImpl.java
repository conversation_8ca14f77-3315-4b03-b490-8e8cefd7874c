package com.perennialsys.pdfreader.db.mongo;

import com.mongodb.BasicDBObject;
import com.mongodb.DB;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientURI;
import com.mongodb.WriteResult;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.WriteModel;
import com.perennialsys.pdfreader.util.PropertyUtil;
import org.bson.codecs.configuration.CodecProvider;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Properties;

import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;

@Repository
@Qualifier("mongoDao")
@SuppressWarnings("rawtypes")
public class MongoDaoImpl implements MongoDao, DisposableBean {

    private MongoDatabase mongoDatabase;
    private MongoClient mongoClient;
    DB db;

    public MongoDaoImpl(String mongoClientURI, String dbName) {

        Properties prop = PropertyUtil.getProperties("mongoDb");
        String minConnectionsPerHost = prop.getProperty("mongo_min_connectionsper_host");
        String maxConnectionsPerHost = prop.getProperty("mongo_max_connectionsper_host");
        String maxConnectionIdleTime = prop.getProperty("mongo_max_connection_idle_time");
        CodecRegistry pojoCodecRegistry = fromProviders(getPojoClassCodecProvider());
        CodecRegistry codecRegistry = fromRegistries(MongoClient.getDefaultCodecRegistry(), pojoCodecRegistry);

        MongoClientOptions.Builder options = MongoClientOptions.builder()
                .minConnectionsPerHost(Integer.parseInt(minConnectionsPerHost))
                .connectionsPerHost(Integer.parseInt(maxConnectionsPerHost))
                .maxConnectionIdleTime(Integer.parseInt(maxConnectionIdleTime))
                .codecRegistry(codecRegistry);

        MongoClientURI connectionURI = new MongoClientURI(mongoClientURI, options);
        mongoClient = new MongoClient(connectionURI);
        db = mongoClient.getDB(dbName);
        mongoDatabase = mongoClient.getDatabase(dbName);
    }

    private CodecProvider getPojoClassCodecProvider() {
        return PojoCodecProvider.builder().automatic(true)
                .build();
    }

    public MongoDaoImpl() {
    }

    @Override
    public MongoClient getMongoClient() {
        return mongoClient;
    }

    @Override
    public DBCollection getCollection(String collectionName) {
        return db.getCollection(collectionName);
    }

    @Override
    public MongoCollection getMongoCollection(String collectionName) {
        return mongoDatabase.getCollection(collectionName);
    }

    @Override
    public <TargetClass> MongoCollection<TargetClass> getMongoCollection(String collectionName, Class<TargetClass> targetClass) {
        return mongoDatabase.getCollection(collectionName, targetClass);
    }

    @Override
    public DBObject findOne(DBObject query, DBObject projection, String collectionName) {
        return getCollection(collectionName).findOne(query, projection);
    }

    @Override
    public WriteResult upsert(DBObject query, DBObject update, String collectionName) {
        return getCollection(collectionName).update(query, update, true, false);
    }

    @Override
    public WriteResult delete(DBObject query, String collectionName) {
        return getCollection(collectionName).remove(query);
    }

    @Override
    public WriteResult updateMulti(DBObject query, DBObject update, String collectionName) {
        return getCollection(collectionName).updateMulti(query, update);
    }

    @Override
    public BulkWriteResult bulkUpsert(List<WriteModel<BasicDBObject>> bulkDocuments, String collectionName) {
        return getMongoCollection(collectionName, BasicDBObject.class).bulkWrite(bulkDocuments, new BulkWriteOptions().ordered(false));
    }

    @Override
    public void bulkInsert(List<BasicDBObject> bulkDocuments, String collectionName) {
        getMongoCollection(collectionName, BasicDBObject.class).insertMany(bulkDocuments);
    }

    @Override
    public void destroy() {
        mongoClient.close();
    }

    @Override
    public DBObject findOne(DBObject query, String collectionName) {
        return getCollection(collectionName).findOne(query);
    }
}
