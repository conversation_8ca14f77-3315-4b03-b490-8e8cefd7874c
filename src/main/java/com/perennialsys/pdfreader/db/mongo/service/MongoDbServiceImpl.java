package com.perennialsys.pdfreader.db.mongo.service;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.QueryOperators;
import com.mongodb.client.MongoCollection;
import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import com.perennialsys.pdfreader.db.mongo.MongoDao;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Implementation of the MongoDB service interface for handling shipping bill records.
 * This class provides methods to retrieve shipping bill record counts and lists of invoices.
 */
@Slf4j
@Service
public class MongoDbServiceImpl implements IMongoDbService{

    private final MongoDao mongoDao;

    @Autowired
    public MongoDbServiceImpl(MongoDao mongoDao) {
        this.mongoDao = mongoDao;
    }

    @Override
    public int getShippingBillRecordCount(String iecCode, Date sbFromDate, Date sbToDate, String serviceName) {
        log.info("START :: CLASS :: MongoDbServiceImpl :: METHOD :: getShippingBillRecordCount  :: IEC :: {} :: SB From Date :: {} :: SB To Date :: {} :: SERVICE_NAME :: {}", iecCode, sbFromDate, sbToDate, serviceName);

        MongoCollection<Document> collection = mongoDao.getMongoCollection( NoSqlDBTables.SB_DETAILS_STATEMENT, Document.class);

        BasicDBList andList = new BasicDBList();

        long count = 0;

        andList.add(new BasicDBObject(SBInvoiceDetailsFields.IEC.getValue(), iecCode));

        // Combine $gte and $lte into one DBObject for the same field
        BasicDBObject shippingBillDateRange = new BasicDBObject();
        shippingBillDateRange.put(QueryOperators.GTE, sbFromDate);
        shippingBillDateRange.put(QueryOperators.LTE, sbToDate);

        andList.add(new BasicDBObject(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), shippingBillDateRange));

        if (serviceName.equalsIgnoreCase("MOOWR")) {
            //TODO : Check for Shipping Bill Type to be consider in Moowr
        } else if (serviceName.equalsIgnoreCase("EBRC")) {
        }

        Bson matchQuery = new BasicDBObject(QueryOperators.AND, andList);
        count = collection.countDocuments(matchQuery);

        log.info("END :: CLASS :: MongoDbServiceImpl :: METHOD :: getShippingBillRecordCount  :: IEC :: {} :: SB From Date :: {} :: SB To Date :: {} :: SERVICE_NAME :: {} :: COUNT :: {}", iecCode, sbFromDate, sbToDate, serviceName, count);
        return (int) count;
    }

    @Override
    public List<Document> getShippingBillInvoicesList(String pan, String iecCode, Date sbFromDate, Date sbToDate, String serviceName, int pageNum, int limit) throws PdfReaderException {
        log.info("START :: CLASS :: MongoDbServiceImpl :: METHOD :: getShippingBillInvoicesList  :: IEC :: {} :: SB_From_Date :: {} :: SB To Date :: {}", iecCode, sbFromDate, sbToDate );

        List<Document> response = new ArrayList<>();

        MongoCollection<Document> collection = mongoDao.getMongoCollection( NoSqlDBTables.SB_DETAILS_STATEMENT, Document.class);

        int skips = limit * (pageNum);
        if (collection == null) {
            response = Collections.emptyList();
        }

        BasicDBList andList = new BasicDBList();
        BasicDBObject projection = new BasicDBObject();

        andList.add(new BasicDBObject(SBInvoiceDetailsFields.IEC.getValue(), iecCode));

        // Combine $gte and $lte into one DBObject for the same field
        BasicDBObject shippingBillDateRange = new BasicDBObject();
        shippingBillDateRange.put(QueryOperators.GTE, sbFromDate);
        shippingBillDateRange.put(QueryOperators.LTE, sbToDate);

        andList.add(new BasicDBObject(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), shippingBillDateRange));

        if(serviceName.equalsIgnoreCase("MOOWR")){
            projection.put(SBInvoiceDetailsFields.CONSIGNEE_NAME.getValue(), 0);
            projection.put("_id", 0);
        } else if (serviceName.equalsIgnoreCase("EBRC")) {
            projection.put("_id", 0);
            //projection.put(SBInvoiceDetailsFields.PRODUCTS.getValue(), 0);

            projection.put(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.INV_NO.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.INV_DATE.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.INV_CURRENCY.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.INV_VAL.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.CONSIGNEE_NAME.getValue(), 1);

            projection.put(SBInvoiceDetailsFields.COMMISSION.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.CONSIGNEE_NAME.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.CREATED_AT.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.DISCOUNT.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.FREIGHT.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.IEC.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.INSURANCE.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.INV_NO.getValue(), 1);
            projection.put(SBInvoiceDetailsFields.PORT_CODE.getValue(), 1);

        }

        Bson matchQuery = new BasicDBObject(QueryOperators.AND, andList);

        response = collection.find(matchQuery).projection(projection).skip(skips).limit(limit).into(new ArrayList<>());
        log.info("START :: CLASS :: MongoDbServiceImpl :: METHOD :: getShippingBillInvoicesList  :: IEC :: {} :: SB From Date :: {} :: SB To Date :: {}", iecCode, sbFromDate, sbToDate);
        return response;

    }
}
