package com.perennialsys.pdfreader.db.mongo;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import org.apache.log4j.Logger;
import org.bson.conversions.Bson;

import java.util.ArrayList;
import java.util.List;

public class MongoDbCursor<T> {
    private MongoCursor<T> mongoCursor;
    private final MongoCollection<T> mongoCollection;
    private final Bson filter;
    private final List<Bson> aggregatePipelineAttributes;

    private static final Logger LOG = Logger.getLogger(MongoDbCursor.class);

    public MongoDbCursor(Bson filter, MongoCollection<T> mongoCollection) {
        this.filter = filter;
        this.mongoCollection = mongoCollection;
        this.aggregatePipelineAttributes = null;
    }

    public MongoDbCursor(MongoCollection<T> mongoCollection, List<Bson> aggregatePipelineAttributes) {
        this.filter = null;
        this.mongoCollection = mongoCollection;
        this.aggregatePipelineAttributes = aggregatePipelineAttributes;
    }

    public MongoDbCursor(Bson filter, MongoCollection<T> mongoCollection, List<Bson> aggregatePipelineAttributes) {
        this.filter = filter;
        this.mongoCollection = mongoCollection;
        this.aggregatePipelineAttributes = aggregatePipelineAttributes;
    }


    public List<T> getRecords(int recordCount){

        if(null == mongoCursor){
            if(null == filter){
                mongoCursor = mongoCollection.find().iterator();
            }else {
                mongoCursor = mongoCollection.find(filter).iterator();
            }
        }
        int count = 0;
        List<T> recordsList = new ArrayList<>();
        while (mongoCursor.hasNext()){
            if(count >= recordCount){
                break;
            }

            recordsList.add(mongoCursor.next());
        }

        return recordsList;
    }

    public List<T> getRecordsWithAggregatePipeline(int recordCount) throws PdfReaderException {

        if(null == aggregatePipelineAttributes || aggregatePipelineAttributes.isEmpty()){
            LOG.error("ERROR >> CLASS >> MongoDbCursor >> METHOD >> getRecordsWithAggregatePipeline >> Aggregation pipeline attributes are empty");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.UNABLE_TO_PROCESS_REQUEST);
        }

        if(null == mongoCursor){
            mongoCursor = mongoCollection.aggregate(aggregatePipelineAttributes).iterator();
        }
        int count = 0;
        List<T> recordsList = new ArrayList<>();
        while (mongoCursor.hasNext()){
            if(count >= recordCount){
                break;
            }

            recordsList.add(mongoCursor.next());
        }

        return recordsList;
    }

    public long getCount(){
        if(null == filter){
            return mongoCollection.countDocuments();
        }else {
            return mongoCollection.countDocuments(filter);
        }
    }
}
