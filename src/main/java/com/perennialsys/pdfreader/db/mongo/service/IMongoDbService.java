package com.perennialsys.pdfreader.db.mongo.service;

import com.perennialsys.pdfreader.exception.PdfReaderException;
import org.bson.Document;

import java.util.Date;
import java.util.List;

public interface IMongoDbService {


    int getShippingBillRecordCount(String iecCode, Date sbFromDate, Date sbToDate, String serviceName);

    List<Document> getShippingBillInvoicesList(String pan, String iecCode, Date sbFromDate, Date sbToDate, String serviceName,
                                               int pageNum, int limit) throws PdfReaderException;

}
