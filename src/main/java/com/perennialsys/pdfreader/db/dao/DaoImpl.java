package com.perennialsys.pdfreader.db.dao;

import com.perennialsys.pdfreader.util.KeyValue;
import com.perennialsys.pdfreader.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.math.BigInteger;
import java.util.Collection;
import java.util.List;

@Component
public class DaoImpl implements IDao {

    private final EntityManager entityManager;

    @Autowired
    DaoImpl(EntityManager entityManager){
        this.entityManager = entityManager;
    }

    @Override
    public <T> List<T> executeHql(Class<T> voClass, String queryString, List<KeyValue> conditions) {
        List<T> resultList = null;
        if(StringUtils.isNotBlank(queryString)) {
            TypedQuery<T> query = entityManager.createQuery(queryString, voClass);

            if (conditions != null) {
                for (KeyValue kv : conditions) {
                    if (kv.getValue() instanceof Collection) {
                        query.setParameter(kv.getKey(), kv.getValue());
                    } else {
                        query.setParameter(kv.getKey(), kv.getValue());
                    }
                }
            }

            resultList = query.getResultList();
        }

        return resultList;
    }

    @Override
    public <T> List<T> executeHql(Class<T> voClass, String queryString, List<KeyValue> conditions, int skip, int limit) {
        List<T> resultList = null;
        if(StringUtils.isNotBlank(queryString)) {
            TypedQuery<T> query = entityManager.createQuery(queryString, voClass);

            if (conditions != null) {
                for (KeyValue kv : conditions) {
                    if (kv.getValue() instanceof Collection) {
                        query.setParameter(kv.getKey(), kv.getValue());
                    } else {
                        query.setParameter(kv.getKey(), kv.getValue());
                    }
                }
            }

            if(limit > 0) {
                query.setFirstResult(skip);
                query.setMaxResults(limit);
            }

            resultList = query.getResultList();
        }

        return resultList;
    }

    @Override
    public List<Object[]> executeSql(String queryString, List<KeyValue> conditions, int skip, int limit) {
        List<Object[]> resultList = null;
        if(StringUtils.isNotBlank(queryString)) {
            Query query = entityManager.createNativeQuery(queryString);

            if (conditions != null) {
                for (KeyValue kv : conditions) {
                    if (kv.getValue() instanceof Collection) {
                        query.setParameter(kv.getKey(), kv.getValue());
                    } else {
                        query.setParameter(kv.getKey(), kv.getValue());
                    }
                }
            }

            if(limit > 0) {
                query.setFirstResult(skip);
                query.setMaxResults(limit);
            }

            resultList = query.getResultList();
        }

        return resultList;
    }

    @Override
    public long executeCountSql(String queryString, List<KeyValue> conditions) {
        long count = 0;
        if(StringUtils.isNotBlank(queryString)) {
            Query query = entityManager.createNativeQuery(queryString);

            if (conditions != null) {
                for (KeyValue kv : conditions) {
                    if (kv.getValue() instanceof Collection) {
                        query.setParameter(kv.getKey(), kv.getValue());
                    } else {
                        query.setParameter(kv.getKey(), kv.getValue());
                    }
                }
            }

            count = ((BigInteger) query.getSingleResult()).longValue();
        }

        return count;
    }

    @Override
    public long executeCountHql(String queryString, List<KeyValue> conditions) {
        long resultList = 0;

        if(StringUtils.isNotBlank(queryString)) {
            Query query = entityManager.createQuery(queryString);

            if (conditions != null) {
                for (KeyValue kv : conditions) {
                    if (kv.getValue() instanceof Collection) {
                        query.setParameter(kv.getKey(), kv.getValue());
                    } else {
                        query.setParameter(kv.getKey(), kv.getValue());
                    }
                }
            }

            resultList = (long) query.getSingleResult();
        }

        return resultList;
    }
}
