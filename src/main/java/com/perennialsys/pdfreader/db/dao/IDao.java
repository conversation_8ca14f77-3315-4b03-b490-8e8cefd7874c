package com.perennialsys.pdfreader.db.dao;

import com.perennialsys.pdfreader.util.KeyValue;

import java.util.List;

public interface IDao {

    <T> List<T> executeHql(Class<T> voClass, String queryString, List<KeyValue> conditions);
    <T> List<T> executeHql(Class<T> voClass, String queryString, List<KeyValue> conditions, int skip, int limit);
    List<Object[]> executeSql(String queryString, List<KeyValue> conditions, int skip, int limit);
    long executeCountSql(String queryString, List<KeyValue> conditions);
    long executeCountHql(String queryString, List<KeyValue> conditions);
    //<T> List<T> getVoList(Class<T> voClass, String queryString, List<KeyValue> conditions);
}
