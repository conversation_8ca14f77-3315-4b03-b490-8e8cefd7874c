package com.perennialsys.pdfreader.excel;

/**
 * <AUTHOR>
 */
public class RowStreamData {

    private final int id;
    private CellStreamData[] cells;

    public RowStreamData(int id, int size) {
        super();
        this.id = id;
        cells = new CellStreamData[size];
    }

    public CellStreamData[] getCells() {
        return cells;
    }

    public void setCells(CellStreamData[] cells) {
        this.cells = cells;
    }

    public int getId() {
        return id;
    }

    public void addDataAt(int index, CellStreamData cell) {
        cells[index] = cell;
    }

    public String getValueAt(int index) {
        return cells[index] == null ? null : cells[index].getValue();
    }
}
