package com.perennialsys.pdfreader.excel;


import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import org.apache.log4j.Logger;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * XSSF and XML Stream Reader
 * <p>
 * If memory footprint is an issue, then for XSSF, you can get at the underlying
 * XML data, and process it yourself. This is intended for intermediate
 * developers who are willing to learn a little bit of low level structure of
 * .xlsx files, and who are happy processing XML in java. Its relatively simple
 * to use, but requires a basic understanding of the file structure. The
 * advantage provided is that you can read a XLSX file with a relatively small
 * memory footprint.
 *
 * <AUTHOR>
 */
public class ExcelSheetStream implements IWorksheetStream {
    private static final Logger LOGGER = Logger.getLogger(ExcelSheetStream.class);

    private int rowNum = 0;
    private int headerRowIdx;
    private RowStreamData headerRow;
    private final OPCPackage opcPkg;
    private final ReadOnlySharedStringsTable stringsTable;
    private final StylesTable stylesTable;
    InputStream inputStream;
    Map<String, RowStreamData> sheetsHeader = new HashMap<>();
    private XMLStreamReader xmlReader;
    private final DataFormatter formatter;
    private static final String CHECK_DATE_REGEX = "(?=.*[m,M])(?=.*[d,D])(?=.*[y,Y]).*";

    public enum StreamDataType {
        BOOLEAN, ERROR, FORMULA, INLINE_STRING, SST_STRING, NUMBER,
    }

    public ExcelSheetStream(String excelPath, int headerRowIdx, String sheetName) throws Exception {
        formatter = new DataFormatter();
        opcPkg = OPCPackage.open(excelPath, PackageAccess.READ);
        stringsTable = new ReadOnlySharedStringsTable(opcPkg);

        XSSFReader xssfReader = new XSSFReader(opcPkg);
        stylesTable = xssfReader.getStylesTable();
        Iterator<InputStream> sheets = xssfReader.getSheetsData();

        if (sheets instanceof XSSFReader.SheetIterator) {
            XSSFReader.SheetIterator sheetiterator = (XSSFReader.SheetIterator) sheets;

            while (sheetiterator.hasNext()) {
                InputStream dummy = sheetiterator.next();
                if (sheetiterator.getSheetName().equalsIgnoreCase(sheetName)) {
                    inputStream = dummy;
                    break;
                } else {
                    dummy.close();
                }
            }
        }

        if (inputStream == null) {
            return;
        }
        XMLInputFactory factory = XMLInputFactory.newInstance();
        xmlReader = factory.createXMLStreamReader(inputStream);

        while (xmlReader.hasNext()) {
            xmlReader.next();
            if (xmlReader.isStartElement()) {
                if (xmlReader.getLocalName().equals("sheetData")) {
                    break;
                }
            }
        }
        headerRow = readHeader(headerRowIdx);

		/*if (sheets instanceof XSSFReader.SheetIterator) {
			XSSFReader.SheetIterator sheetiterator = (XSSFReader.SheetIterator)sheets;
			XMLInputFactory factory = XMLInputFactory.newInstance();


			Iterator<InputStream> sheets = xssfReader.getSheetsData();

			if (sheets instanceof XSSFReader.SheetIterator) {
				XSSFReader.SheetIterator sheetiterator = (XSSFReader.SheetIterator) sheets;

				while (sheetiterator.hasNext()) {
					InputStream dummy = sheetiterator.next();
					if(sheetiterator.getSheetName().equals(sheetName)){
						inputStream = dummy;
						break;
					} else {
						dummy.close();
					}
				}
			}

			while (sheetiterator.hasNext()) {
				rowNum = 0;
				inputStream = sheetiterator.next();
				xmlReader = factory.createXMLStreamReader(inputStream);
				headerRow = readHeader(headerRowIdx);
				this.headerRowIdx = headerRowIdx;
				if(type == GstrFileType.ANX1){
					try{
						// Exclude unrequired sheets
						Enum.valueOf(GstnExcelGstrTab.class, sheetiterator.getSheetName());
					}catch (Exception e){
						System.out.println(e);
						continue;
					}
				}
				sheetsHeader.put(sheetiterator.getSheetName(), headerRow);
			}
		}*/
    }

    // created other constructor for getting sheet based on name
    // <AUTHOR> Ahsan S
    public ExcelSheetStream(String excelPath, int headerRowIdx) throws Exception {
        formatter = new DataFormatter();
        opcPkg = OPCPackage.open(excelPath, PackageAccess.READ);
        stringsTable = new ReadOnlySharedStringsTable(opcPkg);

        XSSFReader xssfReader = new XSSFReader(opcPkg);
        stylesTable = xssfReader.getStylesTable();
        XMLInputFactory factory = XMLInputFactory.newInstance();
        inputStream = xssfReader.getSheetsData().next();
        xmlReader = factory.createXMLStreamReader(inputStream);

        while (xmlReader.hasNext()) {
            xmlReader.next();
            if (xmlReader.isStartElement()) {
                if (xmlReader.getLocalName().equals("sheetData")) {
                    break;
                }
            }
        }
        headerRow = readHeader(headerRowIdx);
        sheetsHeader.put("sheetData", headerRow);
    }

    @Override
    public int rowNum() {
        return rowNum;
    }

    public int getHeaderRowIdx() {
        return headerRowIdx;
    }

    public void setHeaderRowIdx(int headerRowIdx) {
        this.headerRowIdx = headerRowIdx;
    }

    public void setHeaderRow(RowStreamData headerRow) {
        this.headerRow = headerRow;
    }

    private RowStreamData readHeader(int headerRowIdx) throws PdfReaderException {
        try {
            skipRows(headerRowIdx);
            return getRow();
        } catch (XMLStreamException e) {
            throw new PdfReaderException(e);
        }
    }

    @Override
    public RowStreamData getHeader() throws PdfReaderException {
        return headerRow;
    }

    @Override
    public List<RowStreamData> readRows(int batchSize) throws PdfReaderException {
        String elementName = "row";
        List<RowStreamData> dataRows = new ArrayList<>();
        try {
            if (batchSize > 0) {
                while (xmlReader.hasNext()) {
                    xmlReader.next();
                    if (xmlReader.isStartElement()) {
                        if (xmlReader.getLocalName().equals(elementName)) {
                            rowNum++;
                            dataRows.add(getRow());
                            if (dataRows.size() == batchSize) {
                                break;
                            }
                        }
                    }
                }
            }
        } catch (XMLStreamException e) {
            throw new PdfReaderException(e);
        }
        return dataRows;
    }

    @Override
    public void skipRows(int offset) throws PdfReaderException {
        String elementName = "row";
        int skippedRows = 0;
        try {
            if (offset > 0) {
                while (xmlReader.hasNext()) {
                    xmlReader.next();
                    if (xmlReader.isStartElement()) {
                        if (xmlReader.getLocalName().equals(elementName)) {
                            rowNum++;
                            skippedRows++;
                            // dataRows.add(getRow());
                            if (skippedRows == offset) {
                                break;
                            }
                        }
                    }
                }
            }
        } catch (XMLStreamException e) {
            throw new PdfReaderException(e);
        }
    }

    private RowStreamData getRow() throws XMLStreamException {
        List<CellStreamData> rowValues = new ArrayList<>();
        int idx = 0;
        while (xmlReader.hasNext()) {
            xmlReader.next();
            if (xmlReader.isStartElement()) {
                if (xmlReader.getLocalName().equals("c")) {
                    CellReference cellReference = new CellReference(xmlReader.getAttributeValue(null, "r"));
                    // Fill in the possible blank cells!
                    while (rowValues.size() < cellReference.getCol()) {
                        rowValues.add(new CellStreamData(idx, null, null, null));
                        ++idx;
                    }
                    String cellType = xmlReader.getAttributeValue(null, "t");
                    String cellStyleStr = xmlReader.getAttributeValue(null, "s");
                    rowValues.add(getCell(idx, cellType, cellStyleStr));
                    ++idx;
                }
            } else if (xmlReader.isEndElement() && xmlReader.getLocalName().equals("row")) {
                break;
            }
        }
        RowStreamData row = new RowStreamData(rowNum, rowValues.size());
        for (int i = 0; i < rowValues.size(); i++) {
            row.addDataAt(i, rowValues.get(i));
        }
        return row;
    }

    private CellStreamData getCell(int id, String cellType, String cellStyleStr) throws XMLStreamException {
        CellStreamData cell = new CellStreamData(id, null, null, null);
        while (xmlReader.hasNext()) {
            xmlReader.next();
            if (xmlReader.isStartElement()) {
                StreamDataType dataType;
                int formatIndex;
                String formatString;
                if (xmlReader.getLocalName().equals("v")) {
                    // Set up defaults.
                    dataType = StreamDataType.NUMBER;
                    formatIndex = -1;
                    formatString = null;
                    if ("b".equals(cellType)) {
                        dataType = StreamDataType.BOOLEAN;
                    } else if ("e".equals(cellType)) {
                        dataType = StreamDataType.ERROR;
                    } else if ("inlineStr".equals(cellType)) {
                        dataType = StreamDataType.INLINE_STRING;
                    } else if ("s".equals(cellType)) {
                        dataType = StreamDataType.SST_STRING;
                    } else if ("str".equals(cellType)) {
                        dataType = StreamDataType.FORMULA;
                    } else {
                        // Number, but almost certainly with a special style or
                        // format
                        XSSFCellStyle style = null;
                        if (stylesTable != null) {
                            if (cellStyleStr != null) {
                                int styleIndex = Integer.parseInt(cellStyleStr);
                                style = stylesTable.getStyleAt(styleIndex);
                            } else if (stylesTable.getNumCellStyles() > 0) {
                                style = stylesTable.getStyleAt(0);
                            }
                        }
                        if (style != null) {
                            formatIndex = style.getDataFormat();
                            formatString = style.getDataFormatString();
                            if (formatString == null) {
                                formatString = BuiltinFormats.getBuiltinFormat(formatIndex);
                            }
                        }
                    }
                    String value = getCellValue(dataType, formatIndex, formatString, xmlReader.getElementText());

                    cell = new CellStreamData(id, value, dataType, formatString);
                }
            } else if (xmlReader.isEndElement() && xmlReader.getLocalName().equals("c")) {
                break;
            }
        }
        return cell;
    }

    /**
     * @param dataType
     * @param formatIndex
     * @param formatString
     * @param value
     * @return
     */
    private String getCellValue(StreamDataType dataType, int formatIndex, String formatString, String value) {
        String thisStr = null;
        switch (dataType) {
            case BOOLEAN:
                char first = value.charAt(0);
                thisStr = first == '0' ? "FALSE" : "TRUE";
                break;

            case ERROR:
                thisStr = "ERROR:" + value;
                break;

            case FORMULA:
                /*
                 * if (formulasNotResults) { thisStr = formula.toString(); } else {
                 */
                String fv = value;

                if (formatString != null) {
                    try {
                        // Try to use the value as a formattable number
                        double d = Double.parseDouble(fv);
                        thisStr = formatter.formatRawCellContents(d, formatIndex, formatString);
                    } catch (NumberFormatException e) {
                        // Formula is a String result not a Numeric one
                        thisStr = fv;
                    }
                } else {
                    // No formatting applied, just do raw value in all cases
                    thisStr = fv;
                }
                // }
                break;

            case INLINE_STRING:
                XSSFRichTextString rtsi = new XSSFRichTextString(value);
                thisStr = rtsi.toString();
                break;

            case SST_STRING:
                String sstIndex = value;
                try {
                    int idx = Integer.parseInt(sstIndex);
                    XSSFRichTextString rtss = new XSSFRichTextString(stringsTable.getEntryAt(idx));
                    thisStr = rtss.toString();
                } catch (NumberFormatException ex) {
                    LOGGER.error("POILogger.ERROR Failed to parse SST index '" + sstIndex, ex);
                }
                break;

            case NUMBER:
                String n = value;

                /**
                 * format as per excel formatting except for Accounting format
                 * "#,##0.00"
                 */
                //  Changes needed in below condition to handled exponential values
                if (formatString != null && !formatString.contains("#,##0.00") && n.length() > 0) {
                    /** format as per excel formatting */
                    thisStr = formatter.formatRawCellContents(Double.parseDouble(n), formatIndex, formatString);
                    // below condition is added to handled exponential values
                    if (thisStr.contains("E")) {
                        thisStr = n;
                    }
                } else {
                    thisStr = n;
                }


                /** try to format as date */
                // NOTE issue with pattern matching if string contain comma (,)
                // e.g "_(* #,##0.00_);" matches with CHECK_DATE_REGEX
                // but "_(* ###0.00_);" not matches with CHECK_DATE_REGEX
                formatString = formatString.replaceAll(",", "");
                if (formatString.matches(CHECK_DATE_REGEX)) {
                    Date date = DateUtil.getJavaDate(Double.parseDouble(n));
                    SimpleDateFormat GSTR_DATE_SDF = new SimpleDateFormat(DateFormatUtil.ddMMYYYY_Hyphen);
                    thisStr = GSTR_DATE_SDF.format(date);
                }
                break;

            default:
                thisStr = null;
                break;
        }
        return thisStr;
    }

    public void close() throws IOException, XMLStreamException {
        if (xmlReader != null) {
            xmlReader.close();
        }
        if (inputStream != null) {
            inputStream.close();
        }
        if (opcPkg != null) {
            opcPkg.close();
        }

    }

    @Override
    protected void finalize() throws Throwable {
        close();
        super.finalize();
    }

    public Map<String, RowStreamData> getSheetsHeader() {
        return sheetsHeader;
    }

}