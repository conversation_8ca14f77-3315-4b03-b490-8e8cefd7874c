package com.perennialsys.pdfreader.excel;

public class CellStreamData {

    private final int id;
    private String value;
    private final ExcelSheetStream.StreamDataType type;
    private String format;

    public CellStreamData(int id, String value, ExcelSheetStream.StreamDataType type, String format) {
        super();
        this.id = id;
        this.value = value;
        this.type = type;
        this.format = format;
    }

    public int getId() {
        return id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public ExcelSheetStream.StreamDataType getType() {
        return type;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

}