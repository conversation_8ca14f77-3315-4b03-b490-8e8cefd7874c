package com.perennialsys.pdfreader.excel;


import com.perennialsys.pdfreader.constants.AppConstants;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.helper.FileHelper;
import com.perennialsys.pdfreader.util.AmazonS3StorageUtil;
import com.perennialsys.pdfreader.util.PdfReaderConfig;
import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import org.apache.log4j.Logger;

import javax.xml.stream.XMLStreamException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;

public class StreamHelper {

    private static final Logger logger = Logger.getLogger(StreamHelper.class);

    /**
     * closes ExcelSheetStream
     *
     * @param stream
     * @throws IOException
     * @throws XMLStreamException
     */
    public static void closeExcelSheetStream(ExcelSheetStream stream) throws IOException, XMLStreamException {
        if (stream != null) {
            stream.close();
            stream = null;
        }
    }

    public static InputStream prepareExcelFile(FileUploadDetailsVO fileDetails, boolean isSftp) throws PdfReaderException {

        //downloading file from Amazon S3
        if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
            AmazonS3StorageUtil.downloadFromAmazonS3Storage(FileHelper.getDynamicS3FilePath(fileDetails.getPan(),
                fileDetails.getFileType(), isSftp), fileDetails.getFileName(), fileDetails.getFileLoc());
        }

        try {
            File file = new File(fileDetails.getFileLoc());
            if (file.exists()) {
                return Files.newInputStream(file.toPath());
            }
        } catch (Exception e) {
            throw new PdfReaderException(e);
        }
        return null;
    }

    public static File prepareExcelFile(ExportReportsManagerVO fileDetails, boolean isSftp) throws PdfReaderException {

        //downloading file from Amazon S3
        if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
            AmazonS3StorageUtil.downloadFromAmazonS3Storage(FileHelper.getDynamicS3FilePath(fileDetails.getPan(),
                fileDetails.getReportType(), isSftp), fileDetails.getReportName(), fileDetails.getReportFilLoc());
        }

        try {
            File file = new File(fileDetails.getReportFilLoc());
            if (file.exists()) {
                return file;
            }
        } catch (Exception e) {
            throw new PdfReaderException(e);
        }
        return null;
    }

    public static File getFileFromLocation(String pan, String fileType, String fileName, String fileLocation,
                                            boolean isSftp) throws PdfReaderException {

        //downloading file from Amazon S3
        if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
            AmazonS3StorageUtil.downloadFromAmazonS3Storage(FileHelper.getDynamicS3FilePath(pan, fileType, isSftp),
                fileName, fileLocation);
        }

        try {
            File file = new File(fileLocation);
            if (file.exists()) {
                return file;
            }
        } catch (Exception e) {
            throw new PdfReaderException(e);
        }
        return null;
    }

    public static File getFileFromLocation(FileUploadDetailsVO fileUpldDtlsVo,
                                           boolean isSftp) throws PdfReaderException {

        //downloading file from Amazon S3
        if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
            AmazonS3StorageUtil.downloadFromAmazonS3Storage(FileHelper.getDynamicS3FilePath(fileUpldDtlsVo.getPan(), fileUpldDtlsVo.getFileType(), isSftp),
                    fileUpldDtlsVo.getFileName(), fileUpldDtlsVo.getFileLoc());
        }

        try {
            File file = new File(fileUpldDtlsVo.getFileLoc());
            if (file.exists()) {
                return file;
            }
        } catch (Exception e) {
            throw new PdfReaderException(e);
        }
        return null;
    }

    public static FileInputStream prepareExcelFileStream(ExportReportsManagerVO fileDetails, boolean isSftp) throws PdfReaderException {

        //downloading file from Amazon S3
        if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
            AmazonS3StorageUtil.downloadFromAmazonS3Storage(FileHelper.getDynamicS3FilePath(fileDetails.getPan(),
                fileDetails.getReportType(), isSftp), fileDetails.getReportName(), fileDetails.getReportFilLoc());
        }

        try {
            File file = new File(fileDetails.getReportFilLoc());
            if (file.exists()) {
                return new FileInputStream(file);
            }
        } catch (Exception e) {
            throw new PdfReaderException(e);
        }
        return null;
    }

    public static FileInputStream prepareExcelFileStream(File file) throws PdfReaderException {

        //downloading file from Amazon S3
        try {
            if (file.exists()) {
                return new FileInputStream(file);
            }
        } catch (Exception e) {
            throw new PdfReaderException(e);
        }
        return null;
    }

    public static boolean deleteFileFromStorage(FileUploadDetailsVO fileDetails, boolean isSftp) throws PdfReaderException {
        boolean deleteStatus = true;
        //downloading file from Amazon S3
        if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
            AmazonS3StorageUtil.deleteFileFromAmazonS3Stoorage(FileHelper.getDynamicS3FilePath(fileDetails.getPan(),
                            fileDetails.getFileType(), isSftp),
                    fileDetails.getFileName());
        }else {
            try {
                File file = new File(fileDetails.getFileLoc());
                if (file.exists()) {
                    deleteStatus = file.delete();
                }
            } catch (Exception e) {
                throw new PdfReaderException(e);
            }
        }

        return deleteStatus;
    }

}
