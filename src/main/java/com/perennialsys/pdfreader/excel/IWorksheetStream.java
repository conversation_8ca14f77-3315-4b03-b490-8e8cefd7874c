package com.perennialsys.pdfreader.excel;

import com.perennialsys.pdfreader.exception.PdfReaderException;

import java.util.List;

public interface IWorksheetStream {

    int rowNum(); // current row number!

    RowStreamData getHeader() throws PdfReaderException;

    List<RowStreamData> readRows(int batchSize) throws PdfReaderException;

    void skipRows(int batchSize) throws PdfReaderException;
}
