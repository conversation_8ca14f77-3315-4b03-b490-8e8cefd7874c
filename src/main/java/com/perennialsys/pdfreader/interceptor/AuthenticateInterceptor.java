package com.perennialsys.pdfreader.interceptor;

import com.perennialsys.pdfreader.subscriptionService.dto.UserDetailsDto;
import com.perennialsys.pdfreader.subscriptionService.exception.SubscriptionException;
import com.perennialsys.pdfreader.subscriptionService.handler.IAuthHandler;
import com.perennialsys.pdfreader.bean.TenantStore;
import com.perennialsys.pdfreader.constants.PdfReaderApiHeaders;
import com.perennialsys.pdfreader.constants.PdfReaderApiParams;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.enums.PdfReaderApiActions;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class AuthenticateInterceptor implements HandlerInterceptor {

    private static final Logger LOGGER = Logger.getLogger(AuthenticateInterceptor.class);

    private final IAuthHandler authHandler;

    private final TenantStore tenantStore;

    @Autowired
    AuthenticateInterceptor(IAuthHandler authHandler, TenantStore tenantStore){
        this.authHandler = authHandler;
        this.tenantStore = tenantStore;
    }

    /**
     * Pre handling the request
     *
     * @param request Http Request
     * @param response Http Response
     * @param handler Handler
     * @return Returns if the request is valid or not
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws PdfReaderException, SubscriptionException {
        // pre handle Request here
        LOGGER.debug("pre handle Request here");
        String action = request.getParameter(PdfReaderApiParams.ACTION);
        String pan = request.getHeader(PdfReaderApiHeaders.PAN);
        String email = request.getHeader(PdfReaderApiHeaders.EMAIL);
        String sessionToken = request.getHeader(PdfReaderApiHeaders.SESSION_TOKEN);

      if (StringUtils.isBlank(action) || !PdfReaderApiActions.contains(action)) {
            LOGGER.error("ERROR >> CLASS >> AuthenticateInterceptor >> METHOD >> preHandle >> " + ResponseMessage.INVALID_ACTION);
            throw new PdfReaderException(ResponseCode.INVALID_ACTION, ResponseMessage.INVALID_ACTION);
        }

        if (StringUtils.isBlank(pan) || !validatePan(pan)) {
            LOGGER.error("ERROR >> CLASS >> AuthenticateInterceptor >> METHOD >> preHandle >> " + ResponseMessage.INVALID_PAN);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_PAN);
        }

        if (StringUtils.isBlank(sessionToken) || !validatePan(sessionToken)) {
            LOGGER.error("ERROR >> CLASS >> AuthenticateInterceptor >> METHOD >> preHandle >> " + ResponseMessage.MISSING_SESSION_TOKEN);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_SESSION_TOKEN);
        }

        //UserDetailsDto userDetails = authHandler.validateSessionToken(sessionToken, action, pan);
        UserDetailsDto userDetails = authHandler.validateSessionToken(sessionToken, action, "**********");

        tenantStore.setDtls("user-details", userDetails.toString());
        tenantStore.setDtls(PdfReaderApiHeaders.SESSION_TOKEN, sessionToken);
        tenantStore.setDtls(PdfReaderApiParams.ACTION, action);

        return true;
    }

    /**
     * <AUTHOR> Nagare
     * @since 07/06/2023
     * This method check if the PAN is valid or not
     * @param pan PAN
     * @return boolean
     */
    private boolean validatePan(String pan) {
        return true;
    }

    /**
     * Post handling of request
     *
     * @param request HTTP Request
     * @param response HTTP Response
     * @param arg2 Arg2
     * @param modelView Model View
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object arg2,
                           ModelAndView modelView) {
        LOGGER.debug("post handle Request here");
        // post handle Request here
    }

}
