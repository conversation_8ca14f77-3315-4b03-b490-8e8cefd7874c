package com.perennialsys.pdfreader.bean;

import com.perennialsys.pdfreader.util.NumberUtil;
import lombok.Data;
import org.json.JSONObject;

import java.util.Comparator;

@Data
public class PdfDivBean {
    private String divClass;
    private String id;
    private double top;
    private double left;
    private double lineHeight;
    private String fontWeight;
    private String value;
    private double width;
    private String colorCode;

    public void setTop(double top) {
        this.top = NumberUtil.formatDecimal(top, NumberUtil.THREE_DECIMAL_PRECESSION_FORMAT);
    }

    public static class PdfDivBeanLeftAttrComparator implements Comparator<PdfDivBean> {
        @Override
        public int compare(PdfDivBean pdfDivBean, PdfDivBean t1) {
            return Double.compare(pdfDivBean.getLeft(), t1.getLeft());
        }
    }

    public static class PdfDivBeanTopAttrComparator implements Comparator<PdfDivBean> {
        @Override
        public int compare(PdfDivBean pdfDivBean, PdfDivBean t1) {
            return Double.compare(pdfDivBean.getTop(), t1.getTop());
        }
    }

    @Override
    public String toString(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("divClass", this.divClass);
        jsonObject.put("id", this.id);
        jsonObject.put("top", this.top);
        jsonObject.put("left", this.left);
        jsonObject.put("lineHeight", this.lineHeight);
        jsonObject.put("fontWeight", this.fontWeight);
        jsonObject.put("value", this.value);
        jsonObject.put("width", this.width);
        jsonObject.put("colorCode", this.colorCode);

        return jsonObject.toString();
    }
}
