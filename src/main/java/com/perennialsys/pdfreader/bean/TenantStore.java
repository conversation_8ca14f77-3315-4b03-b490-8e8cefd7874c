package com.perennialsys.pdfreader.bean;

import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.Data;

import java.util.Map;

@Data
public class TenantStore {

    private Map<String, String> tenantDtlsMap;

    public void clear(){
        this.tenantDtlsMap = null;
    }

    public String getDtls(String key) throws PdfReaderException {
        if(!tenantDtlsMap.containsKey(key)){
            throw new PdfReaderException(ResponseMessage.INVALID_DATA, "Invalid Key for tenant Details.");
        }
        return tenantDtlsMap.get(key);
    }

    public void setDtls(String key, String value){
        tenantDtlsMap.put(key, value);
    }

}
