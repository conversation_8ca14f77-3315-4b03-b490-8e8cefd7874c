package com.perennialsys.pdfreader.helper;

import com.perennialsys.pdfreader.constants.AppConstants;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.excel.StreamHelper;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.AmazonS3StorageUtil;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.PdfReaderConfig;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.ExportReportTemplateMappingVO;
import com.perennialsys.pdfreader.vo.ExportReportsManagerVO;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.extensions.XSSFCellBorder;

import java.awt.*;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class FileHelper {

    private static final Logger LOG = Logger.getLogger(FileHelper.class);

    private static final Map<String, CellStyle> cellStyleMap = new HashMap<>();
    public static final String FILE_NAME_SEPERATOR = "_";
    public static final String EXPORT_REPORT = "export_report";

    public static final String FILE_UPLOAD_OPERATION = "upload_file";
    public static final String HTML_FILE_OPERATION = "html_file";
    public static final String ZIP_FILE_NAME = "zip_file";

    public static String exportAndUploadFile(String pan, Workbook workbook, String fileNameWithExtension,
                                             String folderPathToStoreFile, String fileType, boolean isSftp) throws IOException, PdfReaderException {
        File dir = new File(folderPathToStoreFile);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String filePathToExport = dir.getAbsolutePath() + File.separator + fileNameWithExtension;
        createFileToPath(filePathToExport, workbook);

        // Saving file to Amazon S3
        if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
            AmazonS3StorageUtil.uploadFileToAmazonS3Storage(getDynamicS3FilePath(pan, fileType, isSftp), filePathToExport,
                    fileNameWithExtension);
        }
        if (!AppConstants.LOCAL_STORAGE.equals(PdfReaderConfig.getInstance().getStorageAccountType())
                && PdfReaderConfig.getInstance().isRemoveInvalidDataFiles()) {
            removeLocalCopy(Collections.singletonList(filePathToExport));
        }
        return filePathToExport;
    }

    public static String getDynamicS3FilePath(String pan, String fileType, boolean isSftp) {
        return PdfReaderConfig.getInstance().getS3RootFolderName() + (isSftp ? "/sftp/" : "/")
            + PdfReaderConfig.getInstance().getS3UploadFolderName()+ "/" + pan + "/" + fileType+ "/";
    }

    public static void removeLocalCopy(List<String> fileList) {
        try {
            for (String filePath : fileList) {
                File file = new File(filePath);
                if (file.exists()) {
                    file.delete();
                }
            }
        } catch (Exception exception) {
            LOG.error("Exception occur while deleting local file copy", exception);
        }
    }

    public static void createFileToPath(String filePath, Workbook workbook) throws IOException {
        FileOutputStream fos;// create file in specified location
        fos = new FileOutputStream(filePath);
        workbook.write(fos);
        fos.close();
        workbook.close();
    }

    public static String getFileNameWithExtension(String fileName, String pan) {
        String dateInString = DateFormatUtil.formatDateToString(new Date(), "yyyy-MM-dd-HHmmssSSS");
        String fileNmWoExt = FilenameUtils.removeExtension(fileName);
        return fileNmWoExt + FILE_NAME_SEPERATOR + pan + FILE_NAME_SEPERATOR + dateInString + "." + FilenameUtils.getExtension(fileName);
    }

    public static String getFileNameWithExtension(String extension, String pan, String fileTypeNmWoExt) {
        String dateInString = DateFormatUtil.formatDateToString(new Date(), "yyyy-MM-dd-HHmmssSSS");

        return fileTypeNmWoExt + FILE_NAME_SEPERATOR + pan + FILE_NAME_SEPERATOR + dateInString + "." + extension;
    }

    public static Workbook getWorkbookForFile(String fileName) throws PdfReaderException {
        try {
            URL fileURI = Thread.currentThread().getContextClassLoader().getResource(fileName);
            File file = new File(fileURI.toURI());
            try (FileInputStream fis = new FileInputStream(file)) {
                XSSFWorkbook xssfWorkbook = new XSSFWorkbook(fis);
                // create workbook
                //	Workbook workbook = new XSSFWorkbook();
                xssfWorkbook.setActiveSheet(0);
                return xssfWorkbook;
            }
        } catch (URISyntaxException | IOException e) {
            throw new PdfReaderException(e);
        }
    }

    public static Workbook getSXSSFWorkbookForFile(String fileName) throws PdfReaderException {
        try {
            URL fileURI = Thread.currentThread().getContextClassLoader().getResource(fileName);
            File file = new File(fileURI.toURI());
            try (FileInputStream fis = new FileInputStream(file)) {
                SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(new XSSFWorkbook(fis));
                // create workbook
                //	Workbook workbook = new XSSFWorkbook();
                sxssfWorkbook.setActiveSheet(0);
                return sxssfWorkbook;
            }
        } catch (URISyntaxException | IOException e) {
            throw new PdfReaderException(e);
        }
    }

    public static void writeDataIntoCell(Cell cell, Object obj) {
        if (CellType.FORMULA.equals(cell.getCellTypeEnum())) {
            cell.setCellValue(obj.toString());
        } else if (obj instanceof String) {
            cell.setCellValue(obj.toString());
            cell.setCellType(CellType.STRING);
        } else if (obj instanceof Double) {
            cell.setCellValue((Double) obj);
            cell.setCellType(CellType.NUMERIC);
        } else if (obj instanceof Boolean) {
            cell.setCellValue((Boolean) obj);
            cell.setCellType(CellType.BOOLEAN);
        } else if (obj instanceof Long) {
            cell.setCellValue((Long) obj);
            cell.setCellType(CellType.NUMERIC);
        } else if (obj instanceof Integer) {
            cell.setCellValue((Integer) obj);
            cell.setCellType(CellType.NUMERIC);
        } else if (obj instanceof Date) {
            cell.setCellValue(DateFormatUtil.formatDateToString((Date) obj, DateFormatUtil.ddMMYYYY_Slash));
            cell.setCellType(CellType.STRING);
        } else {
            cell.setCellValue("");
            cell.setCellType(CellType.STRING);
        }
    }

    public static void createCellAndWriteDataWithStyle(Object value, Row dataRow, Workbook workbook,
                                                       int cellNo, boolean isValid, String comment, boolean setBold,
                                                       Map<String, CellStyle> cellStyleMap) {
        Cell cell = dataRow.createCell(cellNo);

        setSheetStyleAndAddComment(value, cell, workbook, isValid, comment, setBold, cellStyleMap);

        writeDataIntoCell(cell, value);
    }

    public static String generateBase64FileString(ExportReportsManagerVO exportReportsManager, boolean isSftp) throws PdfReaderException {
        File file = StreamHelper.prepareExcelFile(exportReportsManager, isSftp);
        String base64String;
        try {
            if (file != null) {
                byte[] byteArr = FileUtils.readFileToByteArray(file);
                base64String = Base64.getEncoder().encodeToString(byteArr);
            } else {
                throw new PdfReaderException(ResponseCode.FILE_NOT_FOUND, ResponseMessage.FILE_NOT_FOUND);
            }
        } catch (IOException e) {
            throw new PdfReaderException(ResponseCode.FILE_NOT_FOUND, ResponseMessage.FILE_NOT_FOUND);
        }

        return base64String;
    }

    private static void addCommentsToCell(Sheet sheet, Row row, Cell cell, String commentText) {

        CreationHelper createHelper = sheet.getWorkbook().getCreationHelper();
        ClientAnchor anchor = createHelper.createClientAnchor();
        /* Let us draw a big comment box to hold lots of comment data */
        anchor.setCol1(cell.getColumnIndex());
        anchor.setRow1(row.getRowNum());
        anchor.setCol2(cell.getColumnIndex() + 2);
        anchor.setRow2(row.getRowNum() + 3);
        // Create a comment object
        Comment comment = sheet.createDrawingPatriarch().createCellComment(anchor);

        // Create some comment text as Rich Text String
        // You can also insert newline character in comments using \n
        if (comment != null) {

            RichTextString rtf1 = createHelper.createRichTextString(commentText);
            comment.setString(rtf1);
            cell.setCellComment(comment);
        }
    }

    public static String storeFileAndReturnPath(InputStream inputFileStream, String folderPathToStoreFile,
                                                String fileNameWithExtension, String blobContainerName) throws PdfReaderException {
        try {

            byte[] bytes = org.apache.commons.io.IOUtils.toByteArray(inputFileStream);
            File dir = new File(folderPathToStoreFile);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String completeFilePath = dir.getAbsolutePath() + File.separator + fileNameWithExtension;
            completeFilePath.concat("");

            File fileToStore = new File(completeFilePath);

            try (BufferedOutputStream stream = new BufferedOutputStream(Files.newOutputStream(fileToStore.toPath()))) {
                stream.write(bytes);
            }
            // Saving file to Amazon S3
            if (AppConstants.AMAZON_S3_STORAGE_ACCOUNT.equals(PdfReaderConfig.getInstance().getStorageAccountType())) {
                AmazonS3StorageUtil.uploadFileToAmazonS3Storage(blobContainerName, completeFilePath,
                        fileNameWithExtension);
            }
            if (!AppConstants.LOCAL_STORAGE.equals(PdfReaderConfig.getInstance().getStorageAccountType())
                    && PdfReaderConfig.getInstance().isRemoveInvalidDataFiles()) {
                removeLocalCopy(Collections.singletonList(completeFilePath));
            }

            return completeFilePath;
        } catch (Exception e) {
            LOG.error("CLASS >> EximHandler >> storeFileAndReturnPath >> Error while storing file on to disk", e);
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_UPLOADING_FILE, ResponseMessage.ERROR_WHILE_UPLOADING_FILE);
        }
    }

    public static String getLocalFolderPath(String pan, String operationType, String fileType, boolean isSftp) {
//        String rootPath = PdfReaderConfig.getInstance().getResourcesRootPath();

        String rootPath = "D:\\PDF\\"; // For local testing, change this to your local path
        return rootPath+ (isSftp ? "sftp"+ File.separator : "") + operationType + File.separator+ pan + File.separator
                + (StringUtils.isNotBlank(fileType) ? fileType + File.separator : "");
    }

    public static boolean checkAndCreateDirectory(String directoryPath){
        File dir = new File(directoryPath);
        boolean result = false;
        if (!dir.exists()) {
            result = dir.mkdirs();
        }

        return result;
    }

    public static void setDataToWorkBookCell(ExportReportTemplateMappingVO template, Row dataRow,
                                             Object value, Workbook workbook, String comment, boolean setBold,
                                             boolean isValid, Map<String, CellStyle> cellStyleMap) {
        Cell cell = dataRow.createCell(template.getSequenceNumber() - 1);

        setSheetStyleAndAddComment(value, cell, workbook, isValid, comment, setBold, cellStyleMap);

        writeDataIntoCell(cell, value);
    }


    private static void setSheetStyleAndAddComment(Object value, Cell cell, Workbook workbook, boolean isValid,
                                                   String comment, boolean setBold, Map<String, CellStyle> cellStyleMap){

        setSheetCellStyle(cellStyleMap, value, cell, workbook, isValid, setBold);

        if(StringUtils.isNotBlank(comment)){
            addCommentsToCell(cell.getSheet(), cell.getRow(), cell, comment);
        }
    }

    private static void setSheetCellStyle(Map<String, CellStyle> cellStyleMap, Object value, Cell cell,
                                          Workbook workbook, boolean isValid, boolean setBold) {
        StringBuilder styleCode = new StringBuilder();

        if (value instanceof Double) {
            styleCode.append("double");

        } else if (value instanceof Date) {
            styleCode.append("date");
        }

        if(setBold){
            styleCode.append("_bold");
        }
        if (!isValid) {
            styleCode.append("_invalid");
        }

        if(!cellStyleMap.containsKey(styleCode.toString())){
            cellStyleMap.put(styleCode.toString(), createNewCellStyle(workbook, value, isValid, setBold));
        }

        cell.setCellStyle(cellStyleMap.get(styleCode.toString()));
    }

    private static CellStyle createNewCellStyle(Workbook workbook, Object value, boolean isValid, boolean setBold){
        DataFormat dataFormat = workbook.createDataFormat();
        CellStyle cellStyle = workbook.createCellStyle();

        if (value instanceof Double) {
            cellStyle.setDataFormat(dataFormat.getFormat("#,##0.00"));

        } else if (value instanceof Date) {
            //"d-mmm-yy"
            cellStyle.setDataFormat(dataFormat.getFormat("d-mmm-yyyy"));
        }

        if(setBold){
            XSSFFont font = (XSSFFont) workbook.createFont();
            /* Also make the font color to RED */
            font.setBold(true);

            cellStyle.setFont(font);
        }
        if (!isValid) {
            applyErrorCellStyle((XSSFCellStyle) cellStyle, workbook);
        }else {
            applyCellBorderStyle((XSSFCellStyle) cellStyle, workbook);
        }

        return cellStyle;
    }

    private static void applyErrorCellStyle(XSSFCellStyle style, Workbook wb) {
        CreationHelper createHelper = wb.getCreationHelper();
        XSSFFont font = (XSSFFont) wb.createFont();
        /* Also make the font color to RED */
        font.setColor(new XSSFColor(Color.RED));

        style.setFont(font);
        style.setBorderTop(BorderStyle.HAIR);
        style.setBorderBottom(BorderStyle.HAIR);
        style.setBorderLeft(BorderStyle.HAIR);
        style.setBorderRight(BorderStyle.HAIR);
        style.setBorderColor(XSSFCellBorder.BorderSide.TOP, new XSSFColor(Color.RED));
        style.setBorderColor(XSSFCellBorder.BorderSide.BOTTOM, new XSSFColor(Color.RED));
        style.setBorderColor(XSSFCellBorder.BorderSide.LEFT, new XSSFColor(Color.RED));
        style.setBorderColor(XSSFCellBorder.BorderSide.RIGHT, new XSSFColor(Color.RED));
        style.setDataFormat(createHelper.createDataFormat().getFormat("@"));
    }

    private static void applyCellBorderStyle(XSSFCellStyle style, Workbook wb) {
        style.setBorderTop(BorderStyle.HAIR);
        style.setBorderBottom(BorderStyle.HAIR);
        style.setBorderLeft(BorderStyle.HAIR);
        style.setBorderRight(BorderStyle.HAIR);
        style.setBorderColor(XSSFCellBorder.BorderSide.TOP, new XSSFColor(Color.BLACK));
        style.setBorderColor(XSSFCellBorder.BorderSide.BOTTOM, new XSSFColor(Color.BLACK));
        style.setBorderColor(XSSFCellBorder.BorderSide.LEFT, new XSSFColor(Color.BLACK));
        style.setBorderColor(XSSFCellBorder.BorderSide.RIGHT, new XSSFColor(Color.BLACK));
    }

    public static void createZipFile(String zipFilePath, List<String> fileList) throws IOException {

        byte[] buffer = new byte[1024];
        try (FileOutputStream fos = new FileOutputStream(zipFilePath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            for (String exlsfilePath : fileList) {
                File actualFile = new File(exlsfilePath);
                ZipEntry ze = new ZipEntry(actualFile.getName());
                zos.putNextEntry(ze);
                try (FileInputStream in = new FileInputStream(exlsfilePath)) {
                    int len;
                    while ((len = in.read(buffer)) > 0) {
                        zos.write(buffer, 0, len);
                    }
                } catch (Exception e) {
                    throw e;
                }
            }
        } catch (IOException ex) {
            throw new IOException(ex);
        }
    }

    /**
     * <AUTHOR> Nagare
     * @since 07-26-2024
     * @param fileNameWithExtension File file name with extension.
     * @return Returns file Extension
     */
    public static String getFileExtension(String fileNameWithExtension){
        LOG.info("START >> FileHelper >> METHOD >> getFileExtension >> FILE_NAME_WITH_EXTENSION >> "+fileNameWithExtension);
        String fileExtension = null;
        String subStringAfterDot = fileNameWithExtension;

        int dotIndex;
        do{
            dotIndex = subStringAfterDot.indexOf(".");
            subStringAfterDot = subStringAfterDot.substring(dotIndex);
            //Check if the there is number just after dot
            String firstCharAfterDot = subStringAfterDot.substring(1, 2);
            boolean isNumberAfterDot = true;
            try{
                Integer.valueOf(firstCharAfterDot);
            } catch (NumberFormatException e){
                //There is no number just after the dot
                isNumberAfterDot = false;
            }

            //If there is no number just after the dot then check if it is valid extension or not.
            if(!isNumberAfterDot){
                fileExtension = subStringAfterDot.substring(1);
                break;
            }
            subStringAfterDot = subStringAfterDot.substring(1);
        }while (StringUtils.isNotBlank(subStringAfterDot) || dotIndex != subStringAfterDot.lastIndexOf("."));


        LOG.info("END >> FileHelper >> METHOD >> getFileExtension >> FILE_NAME_WITH_EXTENSION >> "+fileNameWithExtension);
        return fileExtension;
    }
}
