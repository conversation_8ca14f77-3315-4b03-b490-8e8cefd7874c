package com.perennialsys.pdfreader.helper;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.StringUtils;

/**
 * <AUTHOR>
 * @since 04/07/2023
 * Enums for Field Identification Termination Policies
 */
public enum FieldIdentificationTerminationPolicies {
    LENGTH,
    SPECIAL_CHAR,
    SPACE;

    /**
     * <AUTHOR>
     * @since 06/07/2023
     * This Method validated the Termination policy with respect to the strategy.
     *
     * @param termination  Termination Policy to validate.
     * @param strategy  Strategy for which the termination policy needs to be validated.
     * @return  Returns if the termination policy is valid or not
     * @throws PdfReaderException  Throws PDF Reader Exception.
     */
    public static boolean validateTermination(String termination, String strategy) throws PdfReaderException {

        if (StringUtils.isBlank(strategy) && FieldIdentificationStrategies.validateStrategy(strategy) &&
                (FieldIdentificationStrategies.WITH_PREFIX.name().equals(strategy)
                        || FieldIdentificationStrategies.WITH_SUFFIX.name().equals(strategy))
                && StringUtils.isBlank(termination)) {

            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_TERMINATION_POLICY);

        } else if (StringUtils.isNotBlank(termination)) {
            try {
                FieldIdentificationTerminationPolicies.valueOf(termination);
            } catch (IllegalArgumentException e) {
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_TERMINATION_POLICY);
            }
        }

        return true;
    }
}
