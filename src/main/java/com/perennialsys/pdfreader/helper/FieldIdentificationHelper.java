package com.perennialsys.pdfreader.helper;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.FieldIdentificationStrategyVO;
import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * @since 04/07/2023
 * This class will have the method to identify the field based on the identification strategy present in the database
 * for the file type and field
 */
public final class FieldIdentificationHelper {

    private static final Logger LOG = Logger.getLogger(FieldIdentificationHelper.class);

    /**
     * @param strategyVO       Identification Strategy VO
     * @param sourceFieldValue Source Field value from which the result has to be extracted.
     * @return - Returns the String value
     * @throws PdfReaderException - Throws PDF Reader Exception
     * <AUTHOR>
     * @since 04/07/2023
     * <p>
     * This Method gets the source field value and finds the result based on the identification strategy and termination policy.
     */
    public static String getFieldValueUsingStrategy(FieldIdentificationStrategyVO strategyVO, String sourceFieldValue)
            throws PdfReaderException {
        String result;
        LOG.info("START >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldValueUsingStrategy >> PAN >> "
            +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
            +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());
        switch (FieldIdentificationStrategies.valueOf(strategyVO.getStrategy())) {
            case WITH_PREFIX: {
                result = getFieldWithPrefix(sourceFieldValue, strategyVO);
                break;
            }
            case WITH_SUFFIX: {
                result = getFieldWithSuffix(sourceFieldValue, strategyVO);
                break;
            }
            case FIRST_WORD: {
                result = getFirstWord(sourceFieldValue);
                break;
            }
            case LAST_WORD: {
                result = getLastWord(sourceFieldValue);
                break;
            }
            default: {
                LOG.error("ERROR >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldValueUsingStrategy >> PAN >> "
                    +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
                    +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_FIELD_IDENTIFICATION_STRATEG);
            }
        }
        LOG.info("END >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldValueUsingStrategy >> PAN >> "
            +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
            +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());
        return result;
    }

    /**
     * @param sourceFieldValue Source field value from which the result has to be extracted.
     * @param strategyVO       Identification Strategy VO
     * @return - Returns String Value.
     * <AUTHOR> Nagare
     * @since 04/07/2023
     */
    private static String getFieldWithPrefix(String sourceFieldValue, FieldIdentificationStrategyVO strategyVO)
        throws PdfReaderException {

        LOG.info("START >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldWithPrefix >> PAN >> "
            +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
            +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());

        String[] sourceValArr = sourceFieldValue.split(" ");
        String targetedSrcFieldVal = null;
        boolean sourceFieldFound = false;
        for (String sourceValSplit : sourceValArr) {
            if (!sourceValSplit.equals(strategyVO.getPrefixSuffix()) && sourceValSplit.startsWith(strategyVO.getPrefixSuffix())) {
                targetedSrcFieldVal = sourceValSplit;
                sourceFieldFound = true;
                break;
            }
        }

        if (!sourceFieldFound && sourceFieldValue.contains(strategyVO.getPrefixSuffix())){
            for(int i = 0; i <= sourceValArr.length ; i++){
                String sourceValSplit = sourceValArr[i];
                if (sourceValSplit.equals(strategyVO.getPrefixSuffix())) {
                    targetedSrcFieldVal = sourceValArr[i + 1 <= sourceValArr.length ? i + 1 : i];
                    sourceFieldFound = true;
                    break;
                }
            }
        }

        if (!sourceFieldFound) {
            LOG.error("ERROR >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldWithPrefix >> PAN >> "
                +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
                +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField()+" >> Source field not found.");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.ERROR_WHILE_IDENTIFYING_FIELD_VALUE);
        }

        LOG.info("END >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldWithPrefix >> PAN >> "
            +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
            +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());

        return applyTerminationPolicyAndGetValue(targetedSrcFieldVal, strategyVO);
    }

    /**
     * @param sourceFieldValue Source field value from which the result has to be extracted.
     * @param strategyVO       Identification Strategy VO
     * @return - Returns String Value
     * <AUTHOR> Nagare
     * @since 04/07/2023
     */
    private static String getFieldWithSuffix(String sourceFieldValue, FieldIdentificationStrategyVO strategyVO) throws PdfReaderException {

        LOG.info("START >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldWithSuffix >> PAN >> "
            +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
            +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());

        String[] sourceValArr = sourceFieldValue.split(" ");
        String targetedSrcFieldVal = null;
        boolean sourceFieldFound = false;
        for (String sourceValSplit : sourceValArr) {
            if (!sourceValSplit.equals(strategyVO.getPrefixSuffix()) && sourceValSplit.endsWith(strategyVO.getPrefixSuffix())) {
                targetedSrcFieldVal = sourceValSplit;
                sourceFieldFound = true;
                break;
            }
        }

        if (!sourceFieldFound && sourceFieldValue.contains(strategyVO.getPrefixSuffix())){
            for(int i = 0; i <= sourceValArr.length ; i++){
                String sourceValSplit = sourceValArr[i];
                if (sourceValSplit.equals(strategyVO.getPrefixSuffix())) {
                    targetedSrcFieldVal = sourceValArr[i == 0 ? i : i-1];
                    sourceFieldFound = true;
                    break;
                }
            }
        }

        if (!sourceFieldFound) {
            LOG.error("ERROR >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldWithSuffix >> PAN >> "
                    + strategyVO.getPan() + " >> FILE_TYPE >> " + strategyVO.getFileType() + " >> TARGET_FIELD >> "
                    + strategyVO.getTargetField() + " >> SOURCE_FIELD >> " + strategyVO.getSourceField() + " >> Source Field not found.");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.ERROR_WHILE_IDENTIFYING_FIELD_VALUE);
        }

        LOG.info("END >> CLASS >> FieldIdentificationHelper >> METHOD >> getFieldWithSuffix >> PAN >> "
            +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
            +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());

        return applyTerminationPolicyAndGetValue(targetedSrcFieldVal, strategyVO);
    }

    /**
     * @param sourceFieldValue Source field value from which the result has to be extracted.
     * @return - Returns String Value
     * <AUTHOR> Nagare
     * @since 04/07/2023
     */
    private static String getFirstWord(String sourceFieldValue) {
        String[] sourceValArr = sourceFieldValue.split(" ");
        return sourceValArr[0];
    }

    /**
     * @param sourceFieldValue Source field value from which the result has to be extracted.
     * @return - Returns String Value
     * <AUTHOR> Nagare
     * @since 04/07/2023
     */
    private static String getLastWord(String sourceFieldValue) {
        String[] sourceValArr = sourceFieldValue.split(" ");
        return sourceValArr[sourceValArr.length - 1];
    }

    /**
     * @param targetedSrcFieldVal  Section of Source field value to be considered for applying termination policy
     * @param strategyVO           Identification Strategy VO
     * @return - Returns String Value
     * @throws PdfReaderException  Throws PDF Reader Exception
     * <AUTHOR> Nagare
     * @since 04/07/2023
     * This method applies the termination policy and returns the value from the targeted source field value
     */
    private static String applyTerminationPolicyAndGetValue(String targetedSrcFieldVal, FieldIdentificationStrategyVO strategyVO) throws PdfReaderException {
        LOG.info("START >> CLASS >> FieldIdentificationHelper >> METHOD >> applyTerminationPolicyAndGetValue >> PAN >> "
            +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
            +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());

        String result;
        FieldIdentificationTerminationPolicies terminationPolicies = StringUtils.isNotBlank(strategyVO.getTermination()) ?
                FieldIdentificationTerminationPolicies.valueOf(strategyVO.getTermination()) : FieldIdentificationTerminationPolicies.SPACE;

        switch (terminationPolicies) {
            case LENGTH: {
                if (strategyVO.getLength() <= 0) {
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_TERMINATION_LENGTH);
                }
                result = targetedSrcFieldVal.substring(0, strategyVO.getLength() > targetedSrcFieldVal.length() ? targetedSrcFieldVal.length() : strategyVO.getLength());
                break;
            }
            case SPECIAL_CHAR: {
                if (StringUtils.isBlank(strategyVO.getSpecialChar())) {
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_TERMINATION_SPECIAL_CHAR);
                }
                int charIndex = targetedSrcFieldVal.indexOf(strategyVO.getSpecialChar());
                result = targetedSrcFieldVal.substring(0, charIndex > 0 ?
                    targetedSrcFieldVal.lastIndexOf(strategyVO.getSpecialChar()) : targetedSrcFieldVal.length());
                break;
            }
            default: {
                String[] valArr = targetedSrcFieldVal.split(" ");
                result = valArr[valArr.length - 1];
                break;
            }
        }

        LOG.info("END >> CLASS >> FieldIdentificationHelper >> METHOD >> applyTerminationPolicyAndGetValue >> PAN >> "
            +strategyVO.getPan()+" >> FILE_TYPE >> "+strategyVO.getFileType()+" >> TARGET_FIELD >> "
            +strategyVO.getTargetField()+" >> SOURCE_FIELD >> "+strategyVO.getSourceField());

        return result;
    }
}