package com.perennialsys.pdfreader.helper;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.StringUtils;

/**
 * <AUTHOR>
 * @since 04/07/2023
 * Enums for Field Identification Strategies
 */
public enum FieldIdentificationStrategies {
    WITH_PREFIX,
    WITH_SUFFIX,
    FIRST_WORD,
    LAST_WORD;

    /**
     * <AUTHOR>
     * @since 06/07/2023
     * This method validates if the field identification strategy is correct or not
     *
     * @param strategy  Field Identification Strategy.
     * @return  Returns if the Field identification strategy is correct or not.
     * @throws PdfReaderException  Throws PDF Reader Exception.
     */
    public static boolean validateStrategy(String strategy) throws PdfReaderException {
        if(StringUtils.isBlank(strategy)){
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_STRATEGY);
        }

        try {
            FieldIdentificationStrategies.valueOf(strategy);
        }catch (IllegalArgumentException e){
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_STRATEGY);
        }

        return true;
    }
}
