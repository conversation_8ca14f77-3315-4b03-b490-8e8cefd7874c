package com.perennialsys.pdfreader.helper;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.excel.StreamHelper;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class ResponseHelper {

    public static final String STATUS_SUCCESS = "SUCCESS";
    public static final String STATUS_ERROR = "ERROR";

    public static Map<String, Object> success(Object data) {
        Map<String, Object> map = new HashMap<>();
        map.put("statusCode", HttpStatus.OK.value());
        map.put("status", STATUS_SUCCESS);
        map.put("data", data);
        return map;
    }

    public static Map<String, Object> success(Object data, String successMessage) {
        Map<String, Object> map = new HashMap<>();
        map.put("statusCode", HttpStatus.OK.value());
        map.put("status", STATUS_SUCCESS);
        map.put("data", data);
        map.put("msg", successMessage);
        return map;
    }

    public static Map<String, Object> error(Object data) {
        Map<String, Object> map = new HashMap<>();
        map.put("status", STATUS_ERROR);
        map.put("data", data);
        return map;
    }

    public static Map<String, Object> error(String errCode, String errMsg) {
        Map<String, Object> map = new HashMap<>();
        map.put("statusCode", errCode);
        map.put("status", STATUS_ERROR);
        map.put("data", errMsg);
        return map;
    }

    public static Map<String, Object> customError(String errMsg) {
        Map<String, Object> map = new HashMap<>();
        map.put("status", STATUS_ERROR);
        map.put("message", errMsg);
        map.put("data", errMsg);
        return map;
    }

    public static ResponseEntity<?> handleFileResponse(File file, boolean returnFileStream) throws PdfReaderException{
        ResponseEntity<?> responseEntity;
        if (null != file) {
            if (returnFileStream) {
                InputStreamResource resource = new InputStreamResource(StreamHelper.prepareExcelFileStream(file));
                String headerValue = "attachment; filename=\"" + file.getName() + "\"";
                responseEntity = ResponseEntity
                        .ok()
                        .contentLength(file.length())
                        .header(HttpHeaders.CONTENT_DISPOSITION, headerValue)
                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
                        .body(resource);
            } else {
                try {
                    Map<String, Object> result = new HashMap<>();
                    result.put("file-name", file.getName());
                    result.put("file-data", Base64.getEncoder().encodeToString(Files.readAllBytes(file.toPath())));
                    responseEntity = ResponseEntity
                            .ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(ResponseHelper.success(result));
                } catch (IOException e) {
                    throw new PdfReaderException(ResponseCode.FILE_NOT_FOUND, ResponseMessage.FILE_NOT_FOUND);
                }
            }
        }else {
            throw new PdfReaderException(ResponseCode.FILE_NOT_FOUND, ResponseMessage.FILE_NOT_FOUND);
        }

        return responseEntity;
    }

}
