package com.perennialsys.pdfreader.helper;


import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.perennialsys.pdfreader.bean.SftpConnectionBean;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.enums.SftpAuthType;
import com.perennialsys.pdfreader.enums.SftpOperationStatus;
import com.perennialsys.pdfreader.enums.SftpOperations;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.vo.SftpDetailsVO;
import com.perennialsys.pdfreader.vo.SftpOperationAuditLogVO;
import org.apache.commons.io.FilenameUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Vector;

@Component
public class SftpHandler {

	private static final Logger LOG = Logger.getLogger(SftpHandler.class);

	private final IService service;

	@Autowired
	SftpHandler(IService service){
		this.service = service;
	}

	public SftpConnectionBean connectToSftp(SftpDetailsVO sftpDetails)
			throws PdfReaderException {
		LOG.info("START >> CLASS >> SftpHelper >> METHOD >> connectToSftp >> PAN >> "+sftpDetails.getPan()+" >> HOST_IP >> "
			+sftpDetails.getHostIp()+" >> AUTH_TYPE >> "+sftpDetails.getAuthType());
		JSch jsch = new JSch();
		ChannelSftp sftpChannel;
		Session session;
		SftpOperationAuditLogVO auditLogVO = createAuditLogEntry(SftpOperations.CONNECT.name(),
				SftpOperationStatus.CONNECT_IP.name(), sftpDetails);
		try {
			session = jsch.getSession(sftpDetails.getUserName(), sftpDetails.getHostIp(), sftpDetails.getPortNumber());

			if(SftpAuthType.PASSWORD.name().equals(sftpDetails.getAuthType())) {
				// password authentication
				session.setPassword(sftpDetails.getPassword());
			}else {
				// ppk authentication
				jsch.addIdentity(sftpDetails.getPpkFilePath());
			}

			session.setConfig("StrictHostKeyChecking", "no");
			session.connect();
			Channel channel = session.openChannel("sftp");
			channel.connect();
			LOG.debug("************** Connection Established ***********************");
			sftpChannel = (ChannelSftp) channel;
		} catch (JSchException e) {
			LOG.error("ERROR >> CLASS >> SftpHelper >> METHOD >> connectToSftp >> " +
				"Error while connecting to SFTP >> ",e);

			updateAuditLogEntry(auditLogVO, SftpOperationStatus.CONNECT_FAILED.name(), e.getLocalizedMessage());

			throw new PdfReaderException(ResponseCode.CONNECTION_FAILED, ResponseMessage.SFTP_CONNECTION_FAILED);
		}
		LOG.info("END >> CLASS >> SftpHelper >> METHOD >> connectToSftp >> PAN >> "+sftpDetails.getPan()+" >> HOST_IP >> "
			+sftpDetails.getHostIp()+" >> AUTH_TYPE >> "+sftpDetails.getAuthType());

		updateAuditLogEntry(auditLogVO, SftpOperationStatus.CONNECT_SUCCESS.name(), null);
		return new SftpConnectionBean(sftpChannel, session);
	}

	public void closeSftpConnection(SftpConnectionBean conectionBean){
		if(null != conectionBean.getSftpChannel()){
			exitSFTP(conectionBean.getSftpChannel());
		}

		if(null != conectionBean.getSession()){
			disconnectSession(conectionBean.getSession());
		}
	}

	private void disconnectSession(Session session) {
		LOG.info("CLASS >> SftpHelper >> METHOD >> disconnectSession");
		if (session != null) {
			session.disconnect();
		}
	}

	private void exitSFTP(ChannelSftp sftpChannel) {
		LOG.info("CLASS >> SftpHelper >> METHOD >> exitSFTP");
		if (sftpChannel != null) {
			sftpChannel.exit();
		}
	}

	public List<String> readDirectory(ChannelSftp sftpChannel, String dirPath, SftpDetailsVO sftpDetails) throws PdfReaderException {
		LOG.info("START >> CLASS >> SftpHelper >> METHOD >> readDirectory");
		List<String> files = new ArrayList<>();
		SftpOperationAuditLogVO auditLogVO = createAuditLogEntry(SftpOperations.READ_DIR.name(),
				SftpOperationStatus.READ_DIR_IP.name(), sftpDetails);
		try {
			if (Objects.nonNull(dirPath)) {
				Vector list = sftpChannel.ls(dirPath);
				for (Object object : list) {
					ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) object;
					System.out.println(entry.getLongname());
					if (!(".".equals(entry.getFilename()) || "..".equals(entry.getFilename()))) {
						if (FilenameUtils.isExtension(entry.getFilename(), "pdf")) {
							files.add(entry.getFilename());
						}
					}
				}
			}
		} catch (SftpException e) {
			LOG.error("ERROR >> CLASS >> SftpHelper >> METHOD >> readDirectory >> " +
					"Error reading SFTP directories >> ",e);

			updateAuditLogEntry(auditLogVO, SftpOperationStatus.READ_DIR_FAILED.name(), e.getLocalizedMessage());

			throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.SFTP_ERROR_WHILE_READING_DIRECTORY);
		}

		updateAuditLogEntry(auditLogVO, SftpOperationStatus.READ_DIR_SUCCESS.name(), null);
		LOG.info("END >> CLASS >> SftpHelper >> METHOD >> readDirectory");
		return files;
	}

	public boolean checkIfFilesExist(ChannelSftp sftpChannel, String dirPath, SftpDetailsVO sftpDetails) throws PdfReaderException {
		LOG.info("START >> CLASS >> SftpHelper >> METHOD >> checkIfFilesExist");
		SftpOperationAuditLogVO auditLogVO = createAuditLogEntry(SftpOperations.READ_DIR.name(),
				SftpOperationStatus.READ_DIR_IP.name(), sftpDetails);

		boolean fileExists = false;
		try {
			if (Objects.nonNull(dirPath)) {
				Vector list = sftpChannel.ls(dirPath);
				for (Object object : list) {
					ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) object;
					System.out.println(entry.getLongname());
					if (!(".".equals(entry.getFilename()) || "..".equals(entry.getFilename()))) {
						if (FilenameUtils.isExtension(entry.getFilename(), "pdf")) {
							fileExists = true;
							break;
						}
					}
				}
			}
		} catch (SftpException e) {
			LOG.error("ERROR >> CLASS >> SftpHelper >> METHOD >> checkIfFilesExist >> " +
					"Error reading SFTP directories >> ",e);
			updateAuditLogEntry(auditLogVO, SftpOperationStatus.READ_DIR_FAILED.name(), e.getLocalizedMessage());

			throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.SFTP_ERROR_WHILE_READING_DIRECTORY);
		}

		updateAuditLogEntry(auditLogVO, SftpOperationStatus.READ_DIR_SUCCESS.name(), null);

		LOG.info("END >> CLASS >> SftpHelper >> METHOD >> checkIfFilesExist");
		return fileExists;
	}

	public void downloadFile(ChannelSftp sftpChannel, String srcPath, String dstPath, SftpDetailsVO sftpDetails) throws PdfReaderException {
		LOG.info("START >> CLASS >> SftpHelper >> METHOD >> downloadFile");
		SftpOperationAuditLogVO auditLogVO = createAuditLogEntry(SftpOperations.DOWNLOAD_FILE.name(),
				SftpOperationStatus.DOWNLOAD_FILE_IP.name(), sftpDetails);
		try {
			sftpChannel.get(srcPath, dstPath);
		} catch (SftpException e) {
			LOG.error("ERROR >> CLASS >> SftpHelper >> METHOD >> downloadFile >> " +
					"Error Downloading file from SFTP >> ",e);

			updateAuditLogEntry(auditLogVO, SftpOperationStatus.DOWNLOAD_FILE_FAILED.name(), e.getLocalizedMessage());

			throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.SFTP_ERROR_WHILE_DOWNLOADING_FILE);
		}

		updateAuditLogEntry(auditLogVO, SftpOperationStatus.DOWNLOAD_FILE_SUCCESS.name(), null);
		LOG.info("END >> CLASS >> SftpHelper >> METHOD >> downloadFile");
	}

	public void uploadFile(ChannelSftp sftpChannel, String srcPath, String dstnPath, SftpDetailsVO sftpDetails) throws PdfReaderException {
		LOG.info("START >> CLASS >> SftpHelper >> METHOD >> uploadFile");
		SftpOperationAuditLogVO auditLogVO = createAuditLogEntry(SftpOperations.UPLOAD_FILE.name(),
				SftpOperationStatus.UPLOAD_FILE_IP.name(), sftpDetails);
		try {
			sftpChannel.put(srcPath, dstnPath);
		} catch (SftpException e) {
			LOG.error("ERROR >> CLASS >> SftpHelper >> METHOD >> moveFile >> " +
				"Error while uploading file in SFTP >> ",e);

			updateAuditLogEntry(auditLogVO, SftpOperationStatus.UPLOAD_FILE_FAILED.name(), e.getLocalizedMessage());

			throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.SFTP_ERROR_WHILE_UPLOADING_FILE);
		}

		updateAuditLogEntry(auditLogVO, SftpOperationStatus.UPLOAD_FILE_SUCCESS.name(), null);
		LOG.info("END >> CLASS >> SftpHelper >> METHOD >> uploadFile");
	}

	public void moveFile(ChannelSftp sftpChannel, String sftpFilePath, String destinationPath, SftpDetailsVO sftpDetails) throws PdfReaderException {
		LOG.info("START >> CLASS >> SftpHelper >> METHOD >> moveFile");
		SftpOperationAuditLogVO auditLogVO = createAuditLogEntry(SftpOperations.MOVE_FILE.name(),
				SftpOperationStatus.MOVE_FILE_IP.name(), sftpDetails);
		try {
			sftpChannel.rename(sftpFilePath, destinationPath);
		} catch (SftpException e) {
			LOG.error("ERROR >> CLASS >> SftpHelper >> METHOD >> moveFile >> " +
					"Error while moving file in SFTP >> ",e);

			updateAuditLogEntry(auditLogVO, SftpOperationStatus.MOVE_FILE_FAILED.name(), e.getLocalizedMessage());

			throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.SFTP_ERROR_WHILE_MOVING_FILE);
		}

		updateAuditLogEntry(auditLogVO, SftpOperationStatus.MOVE_FILE_SUCCESS.name(), null);
		LOG.info("END >> CLASS >> SftpHelper >> METHOD >> moveFile");
	}

	private SftpOperationAuditLogVO createAuditLogEntry(String operation, String status, SftpDetailsVO sftpDetails){

		SftpOperationAuditLogVO auditLog = SftpOperationAuditLogVO.builder()
			.pan(sftpDetails.getPan())
			.hostIp(sftpDetails.getHostIp())
			.operation(operation)
			.status(status)
			.createdAt(new Date())
			.build();
		return service.getSftpOperationAuditLogRepo().save(auditLog);
	}

	private void updateAuditLogEntry(SftpOperationAuditLogVO auditLog, String status, String otherDetails){
		auditLog.setStatus(status);
		auditLog.setOtherDetails(otherDetails);
		service.getSftpOperationAuditLogRepo().save(auditLog);
	}
}
