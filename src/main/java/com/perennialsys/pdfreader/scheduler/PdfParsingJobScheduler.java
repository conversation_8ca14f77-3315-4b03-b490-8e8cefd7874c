package com.perennialsys.pdfreader.scheduler;

import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.enums.TransactionType;
import com.perennialsys.pdfreader.handler.impl.PdfParsingJobSchedulerHandler;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.util.AppConfig;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;

// TODO For now we are using Spring scheduler later we will use quartz scheduler.

/**
 * This class is used to start scheduled based job based on job_maintainer table
 */
@Component
@EnableScheduling
public class PdfParsingJobScheduler {

    /**
     * The Constant LOGGER.
     */
    private static final Logger LOG = Logger.getLogger(PdfParsingJobScheduler.class);

    private static final long FIXED_DELAY_5MIN = 1000 * 60 * 5;
    private static final long FIXED_DELAY_1MIN = 1000 * 60;

    private static final long INIT_DELAY = 1000 * 60;

    private final IService service;
    private final PdfParsingJobSchedulerHandler jobSchedulerHandler;

    @Autowired
    public PdfParsingJobScheduler(IService service, PdfParsingJobSchedulerHandler jobSchedulerHandler) {
        this.service = service;
        this.jobSchedulerHandler = jobSchedulerHandler;
    }

    /**
     * Task scheduler.
     *
     * @return the task scheduler
     */
    @Bean
    public TaskScheduler taskScheduler() {
        // single threaded by default
        return new ConcurrentTaskScheduler();
    }

    /**
     * <AUTHOR> Nagare
     * @since 18-11-2022
     * <p>
     * steps -
     * 1. Check how many PDF Files are being parsed
     *  a. If the count is greater than or equals to the maximum allowed count then do not process.
     * 2. Start the PDF Parsing process.
     */
    @Scheduled(fixedDelay = FIXED_DELAY_1MIN, initialDelay = 0)
    public void triggerPdfParsingTask() {
        LOG.debug("START >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerDataCreationTask");
        //1. Check how many PDF Files are being parsed
        int ipFiles = service.getFileUploadDetailsRepo().countByStatusIn(Collections.singletonList(TransactionStatus.PDF_PARSING_IN_PROGRESS.name()));
        int maxAllowedFiles = Integer.parseInt(AppConfig.getAppProperty("max.allowed.parsing.files", "5"));

        if (ipFiles >= maxAllowedFiles) {
            LOG.info("INTERMEDIATE >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerDataCreationTask >> " +
                "Skipping file parsing as there are already transaction in progress ");
            return;
        }

        jobSchedulerHandler.startPdfProcessing();

        LOG.debug("END >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerDataCreationTask");
    }

    /**
     * <AUTHOR> Nagare
     * @since 18-11-2022
     * <p>
     * steps -
     * 1. Check how many PDF Files are being parsed
     *  a. If the count is greater than or equals to the maximum allowed count then do not process.
     * 2. Start the PDF Parsing process for SFTP files.
     */
    @Scheduled(fixedDelay = FIXED_DELAY_5MIN, initialDelay = 0)
    public void triggerSftpPdfParsingTask() {
        LOG.debug("START >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerSftpPdfParsingTask");
        //1. Check how many PDF Files are being parsed
        int ipFiles = service.getTransactionCountByStatus(TransactionStatus.PDF_PARSING_IN_PROGRESS.name(),
                Arrays.asList(TransactionType.PDF_FILE_PARSING.name(), TransactionType.SFTP_PDF_FILE_PARSING.name()));
        int maxAllowedFiles = Integer.parseInt(AppConfig.getAppProperty("max.allowed.parsing.files", "5"));

        if (ipFiles >= maxAllowedFiles) {
            LOG.info("INTERMEDIATE >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerSftpPdfParsingTask >>" +
                " Skipping file parsing as there are already transaction in progress ");
            return;
        }

        jobSchedulerHandler.startSftpPdfProcessing();

        LOG.debug("END >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerSftpPdfParsingTask");
    }

    /**
     * <AUTHOR> Nagare
     * @since       15-11-2023
     * @description </p>This scheduler will pick all the report generation requests which are in
     *              REPORT_READY_TO_GENERATION status and start the report generation based on the type.
     */
    @Scheduled(fixedDelay = FIXED_DELAY_1MIN, initialDelay = 0)
    public void triggerGenerateReportTask() {
        LOG.debug("START >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerGenerateReportTask");
        //1. Check how many reports are being generated
        int ipFiles = service.getReportsManagerRepo().countAllByStatus(TransactionStatus.REPORT_GENERATION_IN_PROGRESS.name());

        int maxAllowedFiles = Integer.parseInt(AppConfig.getAppProperty("max.allowed.processing.export.files", "5"));

        if (ipFiles >= maxAllowedFiles) {
            LOG.info("INTERMEDIATE >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerGenerateReportTask >>" +
                    " Skipping file parsing as there already "+ ipFiles +" transactions in progress.");
            return;
        }

        //2. Start the report generation process for the REPORT_READY_TO_GENERATION requests.
        jobSchedulerHandler.startGenerateReportProcessing();

        LOG.debug("END >> CLASS >> PdfParsingJobScheduler >> METHOD >> triggerGenerateReportTask");
    }

}
