package com.perennialsys.pdfreader.scheduler;

import com.perennialsys.pdfreader.enums.TransactionStatus;
import com.perennialsys.pdfreader.enums.TransactionType;
import com.perennialsys.pdfreader.handler.impl.SftpJobSchedulerHandler;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.util.AppConfig;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * This class is used to start scheduled based job based on job_maintainer table
 */
@Component
@EnableScheduling
public class SftpJobScheduler {

    /**
     * The Constant LOGGER.
     */
    private static final Logger LOG = Logger.getLogger(SftpJobScheduler.class);

    private static final long FIXED_DELAY = 1000 * 60 * 5;

    private static final long INIT_DELAY = 1000 * 60;

    private final IService service;
    private final SftpJobSchedulerHandler sftpJobSchedulerHandler;

    @Autowired
    public SftpJobScheduler(IService service, SftpJobSchedulerHandler sftpJobSchedulerHandler) {
        this.service = service;
        this.sftpJobSchedulerHandler = sftpJobSchedulerHandler;
    }

    /**
     * Task scheduler.
     *
     * @return the task scheduler
     */
    @Bean
    public TaskScheduler sftpTaskScheduler() {
        //Single threaded by default
        return new ConcurrentTaskScheduler();
    }

    /**
     * <AUTHOR> Nagare
     * @since 18-11-2022
     * </p>
     * steps -
     * 1. Check how many SFTP file downloads are in progress
     *  a. If the count is greater than or equals to the maximum allowed count then do not process.
     * 2. Start the SFTP file download process.
     */
    //@Scheduled(fixedDelay = FIXED_DELAY, initialDelay = 0)
    public void triggerSftpFileDownloadTask() {
        LOG.debug("START >> CLASS >> SftpJobScheduler >> METHOD >> triggerSftpFileDownloadTask");
        //Check how many File downloads are in progress
        int ipFiles = service.getTransactionCountByStatus(TransactionStatus.SFTP_FILE_DOWNLOAD_IP.name(),
                Collections.singletonList(TransactionType.SFTP_PDF_FILE_PROCESSING.name()));
        int maxAllowedFiles = Integer.parseInt(AppConfig.getAppProperty("max.allowed.parsing.files", "5"));

        if (ipFiles >= maxAllowedFiles) {
            LOG.info("INTERMEDIATE >> CLASS >> SftpJobScheduler >> METHOD >> triggerSftpFileDownloadTask >> " +
                    "Skipping file parsing as there are already transaction in progress ");
            return;
        }else {
            sftpJobSchedulerHandler.checkAndStartSftpFileDownloadJob(maxAllowedFiles - ipFiles);
        }
        LOG.debug("END >> CLASS >> SftpJobScheduler >> METHOD >> triggerSftpFileDownloadTask");
    }

}
