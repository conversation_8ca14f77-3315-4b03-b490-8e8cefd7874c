package com.perennialsys.pdfreader.exception;

public class PdfReaderException extends Exception {

    private String errorMessage;

    private String errorCode;


    public PdfReaderException(String errorCode, String errorMessage) {
        super();
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public PdfReaderException(String s, String errorMessage, String errorCode) {
        super(s);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public PdfReaderException(String s, Throwable throwable, String errorMessage, String errorCode) {
        super(s, throwable);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public PdfReaderException(Throwable throwable, String errorMessage, String errorCode) {
        super(throwable);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public PdfReaderException(Throwable e) {
        super(e);
    }

    public PdfReaderException(String errorMessage, Throwable e) {
        super(e);
        this.errorMessage = errorMessage;
    }


    public String getErrorMessge() {
        return errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }
}
