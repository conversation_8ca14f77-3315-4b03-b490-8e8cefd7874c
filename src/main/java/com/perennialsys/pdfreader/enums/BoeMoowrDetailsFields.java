package com.perennialsys.pdfreader.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;

@Getter
@AllArgsConstructor
public enum BoeMoowrDetailsFields {
    SR_NO("", "sr_no"),
    BOE_NO("boeNo", "bill_of_entry_no"),
    BOE_DATE("boeDate", "bill_of_entry_date"),
    PORT_OF_LOADING("portOfLoading", "port_of_loading"),
    BOND_NO("bondNo", "bond_no"),
    ITEM_DESC("itemDesc", "desc_technical_characteristics"),
    INV_NO("invNo", "purchase_inv_no"),
    INV_DATE("invDate", "purchase_inv_date"),
    IMPORTED_QTY("importedQty", "imported_qty"),
    UQC("uqc", "uqc"),
    ITEM_ASSESSABLE_VALUE("assessableValueAsPerBoe", "assessable_value_as_per_boe"),
    BCD_RATE("bcdRate", "bcd_rate"),
    IGST_RATE("igstRate", "igst_rate"),
    CUSTOM_CESS_RATE("customCessRate", "customs_cess_rate");


    private final String value;

    private final String excelMappingKey;

    private static final Logger LOG = Logger.getLogger(BoeMoowrDetailsFields.class);

    public static String getValueFromExcelMappingKey(String excelMappingKey){
        String value = null;
        for (BoeMoowrDetailsFields sheetField : values()) {
            if (sheetField.getExcelMappingKey().equals(excelMappingKey)) {
                value = sheetField.getValue();
            }
        }

        return value;
    }

}