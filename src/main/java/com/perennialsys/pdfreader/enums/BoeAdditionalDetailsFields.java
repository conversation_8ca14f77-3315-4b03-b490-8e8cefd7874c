package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;

@Getter
@AllArgsConstructor
public enum BoeAdditionalDetailsFields {
    SR_NO("", "sr_no"),
    TXN_ID("txnId", "NA"),
    SUB_TXN_ID("subTxnId", "NA"),
    PAN("pan", "NA"),
    ITEM_SN("itemSN", "item_sn"),
    INV_SN("invSN", "inv_sn"),
    SINGLE_WINDOW_DETAILS("singleWindowDetails", "NA"),
    ADDITIONAL_CERTIFICATE_DETAILS("additionalCertificateDetails", "NA"),
    ADDITIONAL_HSS_DETAILS("additionalHssDetails", "NA"),
    SINGLE_WINDOW_CONST_DETAILS("singleWindowConstDetails", "NA"),
    SINGLE_WINDOW_CONTROL_DETAILS("singleWindowControlDetails", "NA"),
    SUPPORTING_DOCS_DETAILS("supportingDocsDetails", "NA"),
    ADDITIONAL_CONTAINER_DETAILS("additionalContainerDetails", "NA"),
    BOE_BOND_DETAILS("boeBondDetails", "NA"),
    BOE_WAREHOUSE_DETAILS("boeWarehouseDetails", "NA"),
    BOE_PAYMENT_DETAILS("boePaymentDetails", "NA"),
    SINGLE_WINDOW_TYPE("singleWindowType", "single_window_type"),
    SINGLE_WINDOW_QUALIFIER("singleWindowQualifier", "single_window_qualifier"),
    SINGLE_WINDOW_INFO_CODE("singleWindowInfoCode", "single_window_info_code"),
    SINGLE_WINDOW_INFO_TEXT("singleWindowInfoText", "single_window_info_text"),
    SINGLE_WINDOW_INFO_MSR("singleWindowInfoMsr", "single_window_info_msr"),
    SINGLE_WINDOW_UQC("singleWindowUqc", "single_window_uqc"),
    SINGLE_WINDOW_CONST_CS_NO("singleWindowConstCsNo", "single_window_const_cs_no"),
    SINGLE_WINDOW_CONST_NAME("singleWindowConstName", "single_window_const_name"),
    SINGLE_WINDOW_CONST_CODE("singleWindowConstCode", "single_window_const_code"),
    SINGLE_WINDOW_CONST_PERCENTAGE("singleWindowConstPercentage", "single_window_const_percentage"),
    SINGLE_WINDOW_CONST_YIELD_PCT("singleWindowConstYieldPct", "single_window_const_yield_pct"),
    SINGLE_WINDOW_CONST_ING("singleWindowConstIng", "single_window_const_ing"),
    SINGLE_WINDOW_CONTROL_TYPE("singleWindowControlType", "single_window_control_type"),
    SINGLE_WINDOW_CONTROL_LOCATION("singleWindowControlLocation", "single_window_control_location"),
    SINGLE_WINDOW_CONTROL_START_DT("singleWindowControlStartDt", "single_window_control_start_dt"),
    SINGLE_WINDOW_CONTROL_END_DT("singleWindowControlEndDt", "single_window_control_end_dt"),
    SINGLE_WINDOW_CONTROL_RES_CD("singleWindowControlResCd", "single_window_control_res_cd"),
    SINGLE_WINDOW_CONTROL_RES_TEXT("singleWindowControlResText", "single_window_control_res_text"),
    SUPPORTING_DOCS_ICE_GATE_ID("supportingDocsIceGateId", "supporting_docs_ice_gate_id"),
    SUPPORTING_DOCS_TYPE("supportingDocsType", "supporting_docs_type"),
    SUPPORTING_DOCI_RN("supportingDocIrn", "supporting_doci_rn"),
    SUPPORTING_DOCS_CD("supportingDocsCd", "supporting_docs_cd"),
    SUPPORTING_DOCS_ISSUE_PLACE("supportingDocsIssuePlace", "supporting_docs_issue_place"),
    SUPPORTING_DOCS_ISSUE_DT("supportingDocsIssueDt", "supporting_docs_issue_dt"),
    SUPPORTING_DOCS_EXPORT_DT("supportingDocsExportDt", "supporting_docs_export_dt"),
    ADDITIONAL_CONTAINER_NO("additionalContainerNo", "additional_container_no"),
    ADDITIONAL_CONTAINER_TRUCK_NO("additionalContainerTruckNo", "additional_container_truck_no"),
    ADDITIONAL_CONTAINER_SEAL_NO("additionalContainerSealNo", "additional_container_seal_no"),
    ADDITIONAL_CONTAINER_FCL_LCL("additionalContainerFclLcL", "additional_container_fcl_lcl"),
    BOND_NO("bondNo", "bond_no"),
    BOND_PORT("bondPort", "bond_port"),
    BOND_CODE("bondCode", "bond_code"),
    BOND_DEBT_AMT("bondDebtAmt", "bond_debt_amt"),
    BOND_BG_AMT("bondBgAmt", "bond_bg_amt"),
    PAYMENT_CHALLAN_NO("paymentChallanNo", "payment_challan_no"),
    PAYMENT_PAID_NO("paymentPaidNo", "payment_paid_no"),
    PAYMENT_AMOUNT("paymentAmount", "payment_amount"),
    WH_WBE_NO("whWbeNo", "wh_wbe_no"),
    WH_DATE("whDate", "wh_date"),
    WH_WBE_SITE("whWbeSite", "wh_wbe_site"),
    WH_CODE("whCode", "wh_code");


    private final String value;

    private final String excelMappingKey;

    private static final Logger LOG = Logger.getLogger(BoeAdditionalDetailsFields.class);

    public static BoeAdditionalDetailsFields getFieldEnum(String value) throws PdfReaderException {
        BoeAdditionalDetailsFields boeFieldToReturn = null;
        for (BoeAdditionalDetailsFields sheetField : values()) {
            if (sheetField.getValue().equals(value)) {
                boeFieldToReturn = sheetField;
            }
        }

        if (boeFieldToReturn == null) {
            LOG.error("ERROR >> ImportSheetFields >> getFieldEnum >> VALUE >> " + value + " >> No enum found for the provided value");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.NO_ENUM_FOUND + "BoeAdditionalDetailsFields." + value);
        }

        return boeFieldToReturn;
    }

}