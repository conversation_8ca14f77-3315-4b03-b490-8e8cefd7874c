package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@AllArgsConstructor
@Getter
public enum ExportFormats {

    DEFAULT(Arrays.asList(PdfFileType.BILL_OF_ENTRY.name(), PdfFileType.SHIPPING_BILL.name()), "", null),
    DUTY_DRAWBACK(Collections.singletonList(PdfFileType.BILL_OF_ENTRY.name()), "", PdfFileType.BILL_OF_ENTRY_DUTY_DRAWBACK.name()),
    MOOWR(Collections.singletonList(PdfFileType.BILL_OF_ENTRY.name()), "", PdfFileType.BILL_OF_ENTRY_MOOWR.name());

    private final List<String> fileTypes;
    private final String displayName;
    private final String exportFileType;

    public static ExportFormats validateExportFormat(String fileType, String exportFormat) throws PdfReaderException {
        ExportFormats exportFormatEnum;
        try{
            if(StringUtils.isNotBlank(fileType) && StringUtils.isNotBlank(exportFormat)) {
                exportFormatEnum = ExportFormats.valueOf(exportFormat);

                if(!exportFormatEnum.getFileTypes().contains(fileType)){
                    throw new PdfReaderException(ResponseCode.INVALID_DATA,
                            ResponseMessage.INVALID_EXPORT_FORMAT_FOR_REPORT);
                }
            }else {
                throw new PdfReaderException(ResponseCode.INVALID_DATA,
                        ResponseMessage.MISSING_EXPORT__FORMAT_OR_REPORT_TYPE);
            }
        }catch (IllegalArgumentException e){
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_EXPORT_FORMAT);
        }

        return exportFormatEnum;
    }

    public static String getExportFileType(String fileType, String exportFormat) throws PdfReaderException{
        String exportFileType = null;
        ExportFormats exportFormatsEnum = validateExportFormat(fileType, exportFormat);

        if(null != exportFormatsEnum){
            if(DEFAULT.equals(exportFormatsEnum)){
                exportFileType = PdfFileType.valueOf(fileType).name();
            }else {
                exportFileType = exportFormatsEnum.getExportFileType();
            }
        }

        return exportFileType;
    }
}
