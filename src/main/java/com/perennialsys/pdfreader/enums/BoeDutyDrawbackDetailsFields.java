package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;

@Getter
@AllArgsConstructor
public enum BoeDutyDrawbackDetailsFields {
    SR_NO("", "sr_no"),
    ITEM_CODE("itemCode", "item_code"),
    ITEM_DESC("itemDesc", "desc_technical_characteristics"),
    BOE_NO("boeNo", "bill_of_entry_no"),
    BOE_DATE("boeDate", "bill_of_entry_date"),
    INV_NO("invNo", "purchase_inv_no"),
    INV_DATE("invDate", "purchase_inv_date"),
    CUSTOM_HOUSE_NAME("customHouse", "name_of_customs_house"),
    HSN("hsn", "hsn"),
    IMPORTED_QTY("importedQty", "imported_qty"),
    AVAILABLE_QTY("availableQty", "available_qty"),
    UTILIZED_QTY("utilizedQty", "utilized_uty"),
    UQC("uqc", "uqc"),
    ITEM_ASSESSABLE_VALUE("assessableValueAsPerBoe", "assessable_value_as_per_boe"),
    BCD_RATE("bcdRate", "bcd_rate"),
    CUSTOM_CESS_RATE("customCessRate", "customs_cess_rate"),
    IMPORTED_COUNTRY("importedCountry", "imported_country_from"),
    SUPPLIER_NAME("supplierName", "supplier_name"),
    IS_FINAL_ASSESMENT("isFinalAssessment", "is_assessment_final"),
    FOREIGN_MATERIAL_SUP_NAME("foreignMaterialsSupName", "foreign_material_details");


    private final String value;

    private final String excelMappingKey;

    private static final Logger LOG = Logger.getLogger(BoeDutyDrawbackDetailsFields.class);

    public static String getValueFromExcelMappingKey(String excelMappingKey){
        String value = null;
        for (BoeDutyDrawbackDetailsFields sheetField : values()) {
            if (sheetField.getExcelMappingKey().equals(excelMappingKey)) {
                value = sheetField.getValue();
            }
        }

        return value;
    }

}