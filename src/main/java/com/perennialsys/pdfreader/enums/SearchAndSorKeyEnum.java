package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SearchAndSorKeyEnum {
    LAST_UPDATED_AT("last-updated-date", "UPDATED_AT", true),
    STATUS("processing-status", "STATUS", false),
    UPLOADED_BY("last-updated-by", "USER_NAME", false),

    FILE_NAME("file-name", "fileDisplayName", false),

    SB_NO("sb-no", "shippingBillNo", false),
    SB_DATE("sb-date", "shippingBillDate", true),
    BOE_NO("boe-no", "boeNo", false),
    BOE_DATE("boe-date", "boeDate", true),
    BE_TYPE("be-type", "beType", false),

    EXPORT_DATE_TIME("export-time", "createdAt", true),
    EXPORTED_BY("exported-by", "userName", false),
    DESCRIPTION("remark", "remark", false);

    private final String displayKey;
    private final String dbKey;
    private final boolean isDateType;

    public static boolean validateKey(String displayKey, String type) throws PdfReaderException {
        switch (type){
            case "UPLOAD_FILE_HISTORY" : {
                if(!displayKey.equals(LAST_UPDATED_AT.getDisplayKey()) &&
                    !displayKey.equals(STATUS.getDisplayKey()) &&
                    !displayKey.equals(UPLOADED_BY.getDisplayKey())){
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
                }
                break;
            }case "UPLOAD_FILE_DETAILS" : {
                if(!displayKey.equals(FILE_NAME.getDisplayKey())){
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
                }
                break;
            }case "PROCESSED_FILE_DETAILS" : {
                if(!displayKey.equals(FILE_NAME.getDisplayKey()) &&
                    !displayKey.equals(SB_NO.getDisplayKey()) &&
                    !displayKey.equals(SB_DATE.getDisplayKey()) &&
                    !displayKey.equals(BOE_NO.getDisplayKey()) &&
                    !displayKey.equals(BOE_DATE.getDisplayKey()) &&
                    !displayKey.equals(BE_TYPE.getDisplayKey())){
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
                }
                break;
            }case "EXPORT_HISTORY" : {
                if(!displayKey.equals(EXPORT_DATE_TIME.getDisplayKey()) &&
                    !displayKey.equals(EXPORTED_BY.getDisplayKey()) &&
                    !displayKey.equals(DESCRIPTION.getDisplayKey())){
                    throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
                }
                break;
            } default: {
                throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
            }
        }

        return true;
    }

    public static SearchAndSorKeyEnum getEnumFromDisplayKey(String displayKey) throws PdfReaderException{
        for (SearchAndSorKeyEnum searchAndSorKeyEnum : values()) {
            if(searchAndSorKeyEnum.getDisplayKey().equals(displayKey)){
                return searchAndSorKeyEnum;
            }
        }

        throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_SEARCH_KEY);
    }
}
