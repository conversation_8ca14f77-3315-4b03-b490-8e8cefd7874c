package com.perennialsys.pdfreader.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransactionType {
    PDF_FILE_PARSING("File Parsing", "Transaction for PDF file parsing for file uploaded through API."),
    SFTP_PDF_FILE_PROCESSING("SFTP File Processing", "Transaction for PDF file import, parse and export from and to SFTP."),
    SFTP_PDF_FILE_PARSING("SFTP File Parsing", "Transaction for PDF file parsing for the files imported from SFTP.");

    private final String displayName;
    private final String description;
}
