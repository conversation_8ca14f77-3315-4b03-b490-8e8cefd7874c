package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum PdfFileType {
    SHIPPING_BILL("Shipping Bill", "SB_Details_Records.xlsx", "SB_Details_Records.pdf",
            NoSqlDBTables.SB_DETAILS_STATEMENT, 1),
    ALL("NA", "NA", "NA", "NA", 0),
    BILL_OF_ENTRY("Bill Of Entry", "BOE_Details_Records.xlsx", "BOE_Details_Records.pdf",
            NoSqlDBTables.BOE_DETAILS_STATEMENT, 2),
    BILL_OF_ENTRY_DUTY_DRAWBACK("Duty Drawback BOEs", "BOE_duty_drawback.xlsx", "NA",
            NoSqlDBTables.BOE_DETAILS_STATEMENT, 1),
    BILL_OF_ENTRY_MOOWR("MOOWR BOEs", "BOE_moowr.xlsx", "NA",
            NoSqlDBTables.BOE_DETAILS_STATEMENT, 1);

    private final String displayName;
    private final String excelFileName;
    private final String pdfFileName;
    private final String mongoCollection;
    private final int lastRowIndex;

    public static PdfFileType validateExportFileType(String fileType) throws PdfReaderException {
        try {
            return valueOf(fileType);
        } catch (IllegalArgumentException e) {
            throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_REPORT_TYPE);
        }
    }

    public static void validatePdfFileType(String fileType) throws PdfReaderException {
        if (!SHIPPING_BILL.name().equals(fileType) && !BILL_OF_ENTRY.name().equals(fileType)) {
            throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_FILE_TYPE);
        }
    }
}