package com.perennialsys.pdfreader.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum PdfReaderApiActions {

    PDF_FILE_UPLOAD, EXPORT_REPORT, FILE_PROCESSING_DTLS,
    CONVERT_PDF_TO_HTML, FILE_UPLOAD_HISTORY, SET_FIELD_IDENTIFICATION_STRATEGY, GET_FIELD_IDENTIFICATION_STRATEGY,
    DELETE_FIELD_IDENTIFICATION_STRATEGY, DOWNLOAD_PDF_FILES, FILE_UPLOAD_DETAILS, GET_PDF_FILE, DELETE_PDF_FILE,
    GET_PROCESSED_FILES, ADVANCE_EXPORT, GET_FILE_EXPORT_HISTORY, SHIPPING_BILL_DETAILS,

    LOGIN, SIGNUP;

    public static boolean contains(String action) {
        return Arrays.stream(values()).anyMatch(apiAction -> apiAction.name().equals(action));
    }

    public static List<String> getNonGeneralActions(){
        List<String> generalActions = getGeneralActions();

        return Arrays.stream(values()).map(Enum::name).filter(name -> !generalActions.contains(name)).collect(Collectors.toList());
    }

    public static List<String> getGeneralActions() {
        return Arrays.asList(LOGIN.name(), SIGNUP.name());
    }

}
