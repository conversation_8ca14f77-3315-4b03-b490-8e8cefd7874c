package com.perennialsys.pdfreader.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PDFSections {

    SHIPPING_BILL_DETAILS("shipping_bill_details"),
    INVOICE_DETAILS("invoice_details"),
    ITEM_DETAILS("item_details"),
    DRAWBACK_AND_ROSL_CLAIM("drawback_and_rosl_claim"),
    RODTEP_DETAILS("rodtep_details"),
    INVOICE_DETAILS_SUB_SECTION("invoice_details_sub_section"),
    BILL_OF_ENTRY_SUMMARY("bill_of_entry_summary"),
    BILL_OF_ENTRY_SUMMARY_BOND_DETAILS("bill_of_entry_summary_bond_details"),
    BILL_OF_ENTRY_SUMMARY_WAREHOUSE_DETAILS("bill_of_entry_summary_warehouse_details"),
    BILL_OF_ENTRY_SUMMARY_PAYMENT_DETAILS("bill_of_entry_summary_payment_details"),
    INVOICE_AND_VALUATION_DETAILS("invoice_and_valuation_details"),
    INVOICE_AND_VALUATION_DETAILS_ITEM_DETAILS("invoice_and_valuation_details_item_details"),
    DUTIES("duties"),
    ADDITIONAL_SVB_DETAILS("additional_svb_details"),
    ADDITIONAL_PREV_BOE_DETAILS("additional_prev_boe_details"),
    ADDITIONAL_RE_IMPORT_DETAILS("additional_re_import_details"),
    ADDITIONAL_ITEM_MANUFACTURER_DETAILS("additional_item_manufacturer_details"),
    ADDITIONAL_ACCESSORY_STATUS_DETAILS("additional_accessory_status_details"),
    ADDITIONAL_LICENCE_DETAILS("additional_licence_details"),
    ADDITIONAL_CERTIFICATE_DETAILS("additional_certificate_details"),
    ADDITIONAL_HSS_DETAILS("additional_hss_details"),
    ADDITIONAL_SINGLE_WINDOW_DECLARATION_DETAILS("additional_single_window_declaration_details"),
    ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONSTITUENTS_DETAILS("additional_single_window_declaration_constituents_details"),
    ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONTROL_DETAILS("additional_single_window_declaration_control_details"),
    ADDITIONAL_SUPPORTING_DOC_DETAILS("additional_supporting_doc_details"),
    ADDITIONAL_CONTAINER_DETAILS("additional_container_details"),
    ADDITIONAL_INVOICE_DETAILS("additional_invoice_details"),
    OTHER_COMPLIANCE("other_compliances");

    private final String sectionKey;

}
