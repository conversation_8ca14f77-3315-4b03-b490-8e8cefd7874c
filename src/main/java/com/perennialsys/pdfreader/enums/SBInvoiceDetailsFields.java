package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;

@AllArgsConstructor
@Getter
public enum SBInvoiceDetailsFields {

    SR_NO("", "sr_no"),
    S_NO("invSerialNo", "NA"),
    TXN_ID("txnId", "NA"),
    SUB_TXN_ID("subTxnId", "NA"),
    PAN("pan", "NA"),
    IEC("iec", "iec_code"),
    APPLICANT_TYPE("applicantType", "applicant_type"),
    SHIPPING_BILL_NO("shippingBillNo", "shipping_bill_no"),
    SHIPPING_BILL_DATE("shippingBillDate", "shipping_bill_date"),
    INV_NO_AND_DATE("invNoAndDate", "NA"),
    INV_NO("invNo", "invoice_no"),
    INV_DATE("invDate", "invoice_date"),
    LEO_DATE("leoDate", "leo_date"),
    PRODUCTS("products", "NA"),
    INV_VAL_EXC_TAX("invValExcTax", "invoice_value_excluding_tax"),
    INV_FOB_VAL("invFobVal", "fob_value_as_per_invoice"),
    INV_CURRENCY("invCurrency", "inv_currency"),
    INV_VAL("invVal", "inv_val"),
    INV_TERM("invTerm", "inv_term"),
    FREIGHT("freight", "freight"),
    INSURANCE("insurance", "insurance"),
    COMMISSION("commission", "commission"),
    DISCOUNT("discount", "discount"),
    OTHER_DEDUCTIONS("otherDeductions", "other_deductions"),
    SB_INSURANCE("sbInsurance", "sb_insurance"),
    SB_COMMISSION("sbCommission", "sb_commission"),
    SB_DISCOUNT("sbDiscount", "sb_discount"),
    SB_OTHER_DEDUCTIONS("sbOtherDeductions", "sb_other_deductions"),
    EXCHANGE_RATE("exchangeRate", "exchange_rate"),
    CUSTOM_HOUSE_NAME("customHouseName", "custom_house_name"),
    PORT_CODE("portCode", "port_code"),
    GSTIN("gstin", "gstin"),
    GSTIN_TYPE("gstinType", "gstin_type"),
    MODE("mode", "mode"),
    EXIM("exim", "exim"),
    MEIS("meis", "meis"),
    DBK("dbk", "dbk"),
    DEEC_DFIA("deecDfia", "deec_dfia"),
    DFRC("dfrc", "dfrc"),
    RE_EXP("reExp", "re_exp"),
    LUT("lut", "lut"),
    STATE_OF_ORIGIN("stateOfOrigin", "state_of_origin"),
    COUNTRY_OF_FINAL_DESTINATION("countryOdFinalDestination", "country_of_final_destination"),
    PORT_OF_FINAL_DESTINATION("portOfFinalDestination", "port_of_final_destination"),
    COUNTRY_OF_DISCHARGE("countryOfDischarge", "country_of_discharge"),
    EXPORTER_NAME_ADDRESS("exportorsNameAndAddress", "NA"),
    EXPORTER_NAME("exportorName", "exporter_name"),
    EXPORTER_ADDRESS("exportorAddress", "exporter_address"),
    CONSIGNEE_NAME_ADDRESS("consigneeNameAndAddress", "NA"),
    CONSIGNEE_NAME("consigneeName", "consignee_name"),
    CONSIGNEE_ADDRESS("consigneeAddress", "consignee_address"),
    THIRD_PARTY_NAME_ADDRESS("thirdPartyNameAndAddress", "NA"),
    THIRD_PARTY_NAME("thirdPartyName", "third_party_name"),
    THIRD_PARTY_ADDRESS("thirdPartyAddress", "third_party_address"),
    AD_CODE("adCode", "ad_code"),
    RBI_WAVIER_NO_AND_DATE("rbiWaiverNoAndDate", "NA"),
    RBI_WAVIER_NO("rbiWaiverNo", "rbi_wavier_no"),
    RBI_WAVIER_DATE("rbiWaiverDate", "rbi_wavier_date"),
    CB_NAME("cbName", "cb_name"),
    AEO("aeo", "aeo"),
    IFSC_NO("ifscNo", "ifsc_no"),
    DBK_CLAIM("dbkClaim", "dbk_claim"),
    IGST_AMT("igstAmt", "igst_amt"),
    CESS_AMT("cessAmt", "cess_amt"),
    RODTEP_AMT("invRodtepAmt", "inv_rodtep_amt"),
    LEO_NO("leoNo", "leo_no"),
    BRC_REALISATION_DATE("brcRealisationDate", "brc_realisation_date"),
    EGM_NO("egmNo", "egm_no"),
    EGM_DATE("egmDt", "egm_dt"),
    ROSCTL_AMT("invRosctlAmt", "inv_rosctl_amt"),
    RODTP("rodtp", "rodtp"),
    PROCESSING_STATUS("processingStatus", "NA"),
    CREATED_AT("createdAt", "NA"),
    UPDATED_AT("updatedAt", "NA");

    private final String value;

    private final String excelMappingKey;

    private static final Logger LOG = Logger.getLogger(SBInvoiceDetailsFields.class);

    public static SBInvoiceDetailsFields getFieldEnum(String value) throws PdfReaderException {
        SBInvoiceDetailsFields sbInvFieldToReturn = null;
        for (SBInvoiceDetailsFields sheetField : values()) {
            if (sheetField.getValue().equals(value)) {
                sbInvFieldToReturn = sheetField;
            }
        }

        if (sbInvFieldToReturn == null) {
            LOG.error("ERROR >> SBInvoiceDetailsFields >> getFieldEnum >> VALUE >> " + value + " >> No enum found for the provided value");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.NO_ENUM_FOUND + "SBInvoiceDetailsFields." + value);
        }

        return sbInvFieldToReturn;
    }

    public static String getValueFromExcelMappingKey(String excelMappingKey){
        String value = null;
        for (SBInvoiceDetailsFields sheetField : values()) {
            if (sheetField.getExcelMappingKey().equals(excelMappingKey)) {
                value = sheetField.getValue();
            }
        }

        return value;
    }
}
