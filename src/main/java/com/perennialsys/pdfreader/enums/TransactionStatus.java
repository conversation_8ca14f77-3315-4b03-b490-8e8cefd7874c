package com.perennialsys.pdfreader.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum TransactionStatus {
    READY_TO_UPLOAD("In Progress", "in-progress"),
    UPLOADED("In Progress", "in-progress"),
    DELETED("Deleted", "deleted"),

    REPORT_READY_FOR_GENERATION("In Progress", "in-progress"),
    REPORT_GENERATION_IN_PROGRESS("In Progress", "in-progress"),
    REPORT_GENERATION_COMPLETED("Completed", "completed"),
    REPORT_GENERATION_FAILED("Failed", "failed"),

    PDF_PARSING_IN_PROGRESS("In Progress", "in-progress"),
    PDF_PARSING_COMPLETE("Completed", "completed"),
    PDF_PARSING_FAILED("Failed", "failed"),

    SFTP_FILE_DOWNLOAD_IP("In Progress", "in-progress"),
    SFTP_FILE_DOWNLOAD_DONE("Completed", "completed"),
    SFTP_FILE_DOWNLOAD_FAILED("Failed", "failed"),

    SFTP_FILE_UPLOAD_IP("In Progress", "in-progress"),
    SFTP_FILE_UPLOAD_DONE("Completed", "completed"),
    SFTP_FILE_UPLOAD_FAILED("Failed", "failed");

    private final String displayName;
    private final String displayKey;

    public static List<String> getErrorStatusList(){
        return Arrays.asList(REPORT_GENERATION_FAILED.name(), PDF_PARSING_FAILED.name(),
                SFTP_FILE_DOWNLOAD_FAILED.name(), SFTP_FILE_UPLOAD_FAILED.name());
    }

    public static List<String> getByDisplayName(String displayName){
        return Arrays.stream(values())
                .filter(status -> status.getDisplayName().equalsIgnoreCase(displayName))
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    public static boolean isDeletedStatus(String value){
        List<String> statusList = getByDisplayName(value);

        return !statusList.isEmpty() && statusList.contains(TransactionStatus.DELETED.name());
    }
}
