package com.perennialsys.pdfreader.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@AllArgsConstructor
@Getter
public enum BoeType {

    H("Home Consumption", Arrays.asList(BoeDetailsFields.PAYMENT_CHALLAN_NO,
            BoeDetailsFields.PAYMENT_AMOUNT)),
    X("Ex-Bond BOE", Arrays.asList(BoeDetailsFields.PAYMENT_CHALLAN_NO,
            BoeDetailsFields.PAYMENT_AMOUNT,BoeDetailsFields.WH_WBE_NO, BoeDetailsFields.WH_DATE,
            BoeDetailsFields.WH_WBE_SITE,BoeDetailsFields.WH_CODE)),
    W("In-Bond / Warehouse BOE", Arrays.asList(BoeDetailsFields.BOND_NO, BoeDetailsFields.BOND_PORT,
            BoeDetailsFields.BOND_CODE, BoeDetailsFields.BOND_DEBT_AMT, BoeDetailsFields.BOND_BG_AMT));

    private final String description;

    private final List<BoeDetailsFields> requiredFields;


    public static String getByDescription(String description){
        for (BoeType type : values()){
            if(type.getDescription().toUpperCase().contains(description.toUpperCase())){
                return type.name();
            }
        }
        return null;
    }
}