package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;

@Getter
@AllArgsConstructor
public enum ExportSheetProductFields {

    PROD_CODE("prodCode", "product_code", true),
    PROD_DESC("prodDesc", "product_desc", true),
    QTY_AS_PER_SHIPING_BILL("qtyAsPerShippingBill", "qty_as_per_invoice", true),
    UQC("uqc", "uqc", true),
    AVAILABLE_STOCK("availableStock", "available_stock", true),
    QTY_CONSUMED_IN_CLAIM("qtyConsumedInClaim", "consumed_claimed_qty", true),
    QTY_CONSIDERED_IN_CLAIM("qtyConsideredInClaim", "NA", false),
    INV_SN("invSerialNo", "inv_sr_no", false),
    SR_NO_DBK("srNoDbk", "sr_no_dbk", false),
    ITM_SN("itemSerialNo", "item_sr_no", false),
    RATE("rate", "rate", false),
    ITM_VALUE_FC("itemValFc", "item_val_fc", false),
    ITEM_HSN_CODE("itemHsnCode", "item_hsn_code", false),
    ASSESSABLE_VALUE("assessableValue", "assessable_value", false),
    FTA_BENEFIT_AVAILED("ftaBenefitAvailed", "fta_benefit_availed", false),
    REWARD_BENEFIT("rewardBenefit", "reward_benefit", false),
    ITE_FOB("itemFob", "item_fob", false),
    PMV("pmv", "pmv", false),
    IGST_STAT("igstStat", "igst_stat", false),
    ITM_IGST_AMT("itmIgstAmt", "item_igst_amt", false),
    SCH_CODE("schCode", "sch_code", false),
    SCHEMA_DESCRIPTION("schemaDescription", "schema_desc", false),
    SQC_MSR("sqcMsr", "sqc_msr", false),
    PT_ABROAD("ptAbroad", "pt_abroad", false),
    COMP_CESS("compCess", "comp_cess", false),
    END_USE("endUse", "end_user", false),
    THIRD_PARTY_ITEM("thirdPartyItem", "third_party_item", false),
    DBK_INV_SNO("invSno", "dnk_inv_sno", false),
    DNK_ITEM_SNO("itemSno", "dbk_item_sno", false),
    DBK_SNO("dbkSrNo", "dnk_sno", false),
    DBK_VAL("dbkValue", "dbk_val", false),
    SBK_RATE("dbkRate", "dbk_rate", false),
    DBK_AMT("dbkAmt", "dbk_amt", false),
    ROSCTL_AMT("rosctlAmt", "rosctl_amt", false),
    RODTEP_INV_SNO_ITEM_SNO("rosctlInvSnItmSn", "rosctl_inv_sn_itm_sn", false),
    RODTEP_INV_SNO("rodtepInvSn", "rodtep_inv_sn", false),
    RODTEP_ITM_SNO("rodtepItmSn", "rodtep_itm_no", false),
    RODTEP_AMT("rodtepAmt", "rodtep_amt", false),
    CUST_1("cust1", "custom_1", false),
    CUST_2("cust2", "custom_2", false),
    CUST_3("cust3", "custom_3", false),
    //    STATUS("status", "NA", false),
    PROCESSING_STATUS("processingStatus", "NA", false);

    private final String value;

    private final String excelMappingKey;

    private final boolean hasValidation;

    private static final Logger LOG = Logger.getLogger(ExportSheetProductFields.class);

    public static ExportSheetProductFields getExportSheetProductFieldsEnum(String value) throws PdfReaderException {
        ExportSheetProductFields exportSheetFieldToReturn = null;
        for (ExportSheetProductFields sheetField : values()) {
            if (sheetField.getValue().equals(value)) {
                exportSheetFieldToReturn = sheetField;
            }
        }

        if (exportSheetFieldToReturn == null) {
            LOG.error("ERROR >> ExportSheetProductFields >> getFieldEnum >> VALUE >> " + value + " >> No enum found for the provided value");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.NO_ENUM_FOUND + "ExportSheetFields." + value);
        }

        return exportSheetFieldToReturn;
    }
}
