package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;

@Getter
@AllArgsConstructor
public enum BoeDetailsFields {
    SR_NO("", "sr_no"),
    TXN_ID("txnId", "NA"),
    SUB_TXN_ID("subTxnId", "NA"),
    PAN("pan", "NA"),
    ITEM_CODE("itemCode", "item_code"),
    ITEM_DESC("itemDesc", "desc_technical_characteristics"),
    BOE_NO("boeNo", "bill_of_entry_no"),
    BOE_DATE("boeDate", "bill_of_entry_date"),
    CUSTOM_HOUSE_NAME("customHouse", "name_of_customs_house"),
    HSN("hsn", "hsn"),
    IMPORTED_QTY("importedQty", "imported_qty"),
    AVAILABLE_QTY("availableQty", "available_qty"),
    UQC("uqc", "uqc"),
    UTILIZED_QTY("utilizedQty", "utilized_uty"),
    ASSESS("assess", "assess"),
    BCD_RATE("bcdRate", "bcd_rate"),
    CUSTOM_CESS_RATE("customCessRate", "customs_cess_rate"),
    IMPORTED_COUNTRY("importedCountry", "imported_country_from"),
    SUPPLIER_NAME("supplierName", "supplier_name"),
    IS_FINAL_ASSESMENT("isFinalAssessment", "is_assessment_final"),
    FOREIGN_MATERIAL_SUP_NAME("foreignMaterialsSupName", "foreign_material_details"),
    CUST_1("cust1", "custom_1"),
    CUST_2("cust2", "custom_2"),
    CUST_3("cust3", "custom_2"),
    PROCESSING_STATUS("processingStatus", "NA"),
    CREATED_AT("createdAt", "NA"),
    UPDATED_AT("updatedAt", "NA"),

    SUPPLIER_NAME_AND_ADDRESS("supplierNameAndAddress", "NA"),
    SUPPLIER_ADDRESS("supplierAddress", "supplier_address"),
    IMPORTERS_NAME_AND_ADDRESS("importersNameAndAddress", "NA"),
    IMPORTERS_NAME("importersName", "importers_name"),
    IMPORTERS_ADDRESS("importersAddress", "importers_address"),
    INV_NO_AND_DATE("invoiceNoAndDate", "NA"),
    INV_NO("invNo", "inv_no"),
    INV_DATE("invDate", "inv_dt"),
    IGM_NO("igmNo", "manifiest_igm_no"),
    HSS("hss", "hss"),
    HIGHSEAS("highseas", "highseas"),
    PROV_FINAL("provFinal", "prov_final"),
    PORT_OF_LOADING("portOfLoading", "port_of_loading"),
    IGM_DATE("igmDate", "manifiest_igm_date"),
    MODE("mode", "mode"),
    INW_DATE("inwDate", "manifiest_inw_date"),
    GIGMNO("gigmNo", "manifiest_gig_mno"),
    GIGMDT("gigmDt", "manifiest_gig_mdt"),
    MAWB_NO("maqbNo", "manifiest_mawb_no"),
    DATE("date", "manifiest_date"),
    HAWB_NO("hawbNo", "manifiest_hawb_no"),
    MANIFEST_DATE("manifiestDate", "manifiest_m_date"),
    PKG("pkg", "manifiest_pkg"),
    GW("gw", "manifiest_gw"),
    SEC_48("sec48", "sec_48"),
    RE_IMP("reImp", "re_imp"),
    ADV_BE("advBe", "adv"),
    EXAM("exam", "exam"),
    FIRST_CHECK("firstCheck", "first_check"),
    AD_CODE("adCode", "ad_code"),
    TRANSACTING_AD_CODE("transactingAdCode", "transacting_parties_ad_code"),
    BE_TYPE("beType", "be_type"),
    EXCHANGE_RATE("exchangeRate", "exchange_rate"),
    GSTIN("gstin", "gstin"),
    GSTIN_TYPE("gstinType", "gstin_type"),
    IEC("iec", "iec"),
    PORT_CODE("portCode", "port_code"),
    BCD_AMOUNT("bcdAmount", "bcd_amount"),
    UNIT_PROCE("unitPrice", "unit_price"),
    QUANTITY("quantity", "quantity"),
    AMOUNT("amount", "amount"),
    ACD_RATE("acdRate", "acd_rate"),
    ACD_AMOUNT("acdAmount", "acd_amount"),
    SWS_RATE("swsRate", "sws_rate"),
    SWS_AMOUNT("swsAmount", "sws_amount"),
    ITEM_ASSESSABLE_VALUE("assessableValueAsPerBoe", "item_assessable_value"),
    TOTAL_DUTY("totalDuty", "total_duty"),
    SAD_RATE("sadRate", "sad_rate"),
    SAD_AMOUNT("sadAmount", "sad_amount"),
    IGST_RATE("igstRate", "igst_rate"),
    IGST_AMOUNT("igstAmount", "igst_amount"),
    CESS_AMOUNT("cessAmount", "cess_amount"),
    ADD_RATE("addRate", "add_rate"),
    ADD_AMOUNT("addAmount", "add_amount"),
    CVD_RATE("cvdRate", "cvd_rate"),
    CVD_AMOUNT("cvdAmount", "cvd_amount"),
    INV_VALUE("invValue", "inv_value"),
    INV_CURRENCY("invCurrency", "inv_currency"),
    VALUATION_INV_CURRENCY("invCurrency", "valuation_inv_currency"),
    TERM("term", "term"),
    FREIGHT("freight", "freight"),
    ITEM_VALUE("itemValue", "item_value"),
    OOC_DATE("oocDate", "ooc_date"),
    OOC_NO("oocNo", "ooc_no"),
    ITEM_SN("itemSN", "item_sn"),
    INV_SN("invSN", "inv_sn"),
    BOND_NO("bondNo", "bond_no"),
    BOND_PORT("bondPort", "bond_port"),
    BOND_CODE("bondCode", "bond_code"),
    BOND_DEBT_AMT("bondDebtAmt", "bond_debt_amt"),
    BOND_BG_AMT("bondBgAmt", "bond_bg_amt"),
    PAYMENT_CHALLAN_NO("paymentChallanNo", "payment_challan_no"),
    PAYMENT_PAID_NO("paymentPaidNo", "payment_paid_no"),
    PAYMENT_AMOUNT("paymentAmount", "payment_amount"),
    WH_WBE_NO("whWbeNo", "wh_wbe_no"),
    WH_DATE("whDate", "wh_date"),
    WH_WBE_SITE("whWbeSite", "wh_wbe_site"),
    WH_CODE("whCode", "wh_code"),
    LIC_UQC("licUqc", "lic_uqc"),
    LIC_DEBIT_DUTY("licDebitDuty", "lic_debit_duty"),
    LIC_SL_NO("licSlNo", "lic_sl_no"),
    LIC_NO("licNo", "lic_no"),
    LIC_DATE("licDate", "lic_date"),
    LIC_CODE("licCode", "lic_code"),
    LIC_PORT("licPort", "lic_port"),
    LIC_DEBIT_VALUE("licDebitValue", "lic_debit_value"),
    LIC_QTY("licQty", "lic_qty"),
    SVB_REF_NO("svbRefNo", "svb_ref_no"),
    SVB_REF_DATE("svbRefDate", "svb_ref_date"),
    SVB_PART_CODE("svbPartCode", "svb_part_code"),
    SVB_LAB("svbLab", "svb_lab"),
    SVB_PF1("svbPf1", "svb_pf1"),
    SVB_LOAD_DATE("svbLoadDate", "svb_load_date"),
    SVB_PF2("svbPf2", "svb_pf2"),
    PREV_BOE_NO("prevBoeNo", "prev_boe_no"),
    PREV_BOE_DATE("prevBoeDate", "prev_boe_date"),
    PREV_BOE_PART_CODE("prevBoePartCode", "prev_boe_part_code"),
    PREV_BOE_UNIT_PRICE("prevBoeUnitPrice", "prev_boe_unit_price"),
    PREV_BOE_CURRENCY_CODE("prevBoeCurrencyCode", "prev_boe_currency_code"),
    RE_IMP_NOTE_NO("reImpNoteNo", "re_imp_note_no"),
    RE_IMP_SL_NO("reImpSlNo", "re_imp_sl_no"),
    RE_IMP_FRT("reImpFrt", "re_imp_frt"),
    RE_IMP_UNIT_INS("reImpUnitIns", "re_imp_unit_ins"),
    RE_IMP_DUTY("reImpDuty", "re_imp_duty"),
    RE_IMP_SB_NO("reImpSbNo", "re_imp_sb_no"),
    RE_IMP_SB_DATE("reImpSbDate", "re_imp_sb_date"),
    RE_IMP_PORT_CD("reImpPortCd", "re_imp_port_cd"),
    RE_IMP_SINV("reImpSinv", "re_imp_sinv"),
    RE_IMP_SITEM_N("reImpSitemN", "re_imp_sitem_n"),
    ITEM_MANU_TYPE("itemManuType", "item_manu_type"),
    ITEM_MANU_MANUFACTURER_CODE("itemManuManufacturerCode", "item_manu_manufacturer_code"),
    ITEM_MANU_SORCE_CY("itemManuSorceCy", "item_manu_sorce_cy"),
    ITEM_MANU_TRANS_CY("itemManuTransCy", "item_manu_trans_cy"),
    ITEM_MANU_ADDRESS("itemManuAddress", "item_manu_address"),
    ACCESSORY_ITM_DTLS("accessoryItmDtls", "accessory_itm_dtls"),
    SINGLE_WINDOW_TYPE("singleWindowType", "single_window_type"),
    SINGLE_WINDOW_QUALIFIER("singleWindowQualifier", "single_window_qualifier"),
    SINGLE_WINDOW_INFO_CODE("singleWindowInfoCode", "single_window_info_code"),
    SINGLE_WINDOW_INFO_TEXT("singleWindowInfoText", "single_window_info_text"),
    SINGLE_WINDOW_INFO_MSR("singleWindowInfoMsr", "single_window_info_msr"),
    SINGLE_WINDOW_UQC("singleWindowUqc", "single_window_uqc"),
    SINGLE_WINDOW_CONST_CS_NO("singleWindowConstCsNo", "single_window_const_cs_no"),
    SINGLE_WINDOW_CONST_NAME("singleWindowConstName", "single_window_const_name"),
    SINGLE_WINDOW_CONST_CODE("singleWindowConstCode", "single_window_const_code"),
    SINGLE_WINDOW_CONST_PERCENTAGE("singleWindowConstPercentage", "single_window_const_percentage"),
    SINGLE_WINDOW_CONST_YIELD_PCT("singleWindowConstYieldPct", "single_window_const_yield_pct"),
    SINGLE_WINDOW_CONST_ING("singleWindowConstIng", "single_window_const_ing"),
    SINGLE_WINDOW_CONTROL_TYPE("singleWindowControlType", "single_window_control_type"),
    SINGLE_WINDOW_CONTROL_LOCATION("singleWindowControlLocation", "single_window_control_location"),
    SINGLE_WINDOW_CONTROL_START_DT("singleWindowControlStartDt", "single_window_control_start_dt"),
    SINGLE_WINDOW_CONTROL_END_DT("singleWindowControlEndDt", "single_window_control_end_dt"),
    SINGLE_WINDOW_CONTROL_RES_CD("singleWindowControlResCd", "single_window_control_res_cd"),
    SINGLE_WINDOW_CONTROL_RES_TEXT("singleWindowControlResText", "single_window_control_res_text"),
    SUPPORTING_DOCS_ICE_GATE_ID("supportingDocsIceGateId", "supporting_docs_ice_gate_id"),
    SUPPORTING_DOCS_TYPE("supportingDocsType", "supporting_docs_type"),
    SUPPORTING_DOCI_RN("supportingDocIrn", "supporting_doci_rn"),
    SUPPORTING_DOCS_CD("supportingDocsCd", "supporting_docs_cd"),
    SUPPORTING_DOCS_ISSUE_PLACE("supportingDocsIssuePlace", "supporting_docs_issue_place"),
    SUPPORTING_DOCS_ISSUE_DT("supportingDocsIssueDt", "supporting_docs_issue_dt"),
    SUPPORTING_DOCS_EXPORT_DT("supportingDocsExportDt", "supporting_docs_export_dt"),
    ADDITIONAL_CONTAINER_NO("additionalContainerNo", "additional_container_no"),
    ADDITIONAL_CONTAINER_TRUCK_NO("additionalContainerTruckNo", "additional_container_truck_no"),
    ADDITIONAL_CONTAINER_SEAL_NO("additionalContainerSealNo", "additional_container_seal_no"),
    ADDITIONAL_CONTAINER_FCL_LCL("additionalContainerFclLcL", "additional_container_fcl_lcl"),
    ADDITIONAL_INV_NO("additionalInvNo", "additional_inv_no"),
    ADDITIONAL_INV_AMT("additionalInvAmt", "additional_inv_amt"),
    ADDITIONAL_INV_CURRENCY("additionalInvCurrency", "additional_inv_currency"),

    BE_STATUS("beStatus", "be_status"),
    DEF_BE("defBe", "def_be"),
    KACHA("kacha", "kacha"),
    COUNTRY_OF_CONSIGNMENT("countryOfConsignment", "country_of_consignment"),
    PORT_OF_SHIPMENT("portOfShipment", "port_of_shipment"),
    CB_NAME("cbName", "cb_name"),
    AEO("aeo", "aeo"),
    UCR("ucr", "ucr"),
    BCD("bcd", "bcd"),
    ACD("acd", "acd"),
    SWS("sws", "sws"),
    NCCD("nccd", "nccd"),
    ADD("add", "add"),
    CVD("cvd", "cvd"),
    IGST("igst", "igst"),
    CESS("cess", "cess"),
    TOTAL_ASS_VALUE("totalAssValue", "total_ass_value"),
    SG("sg", "sg"),
    AED("aed", "aed"),
    GSIA("gsia", "gsia"),
    TTA("tta", "tta"),
    HEALTH("health", "health"),
    DUTY_SUMMARY_TOTAL_DUTY("dutySummaryTotalDuty", "duty_summary_total_duty"),
    INT("int", "int"),
    PENALTY("penalty", "penalty"),
    FINE("fine", "fine"),
    TOTAL_AMOUNT("totalAmount", "total_amount"),
    PUR_ORDE_NO_AND_DATE("purOrdeNoAndDate", "NA"),
    PUR_ORDE_NO("purOrdeNo", "pur_orde_no"),
    PUR_ORDER_DATE("purOrderDate", "pur_order_date"),
    LC_NO_AND_DATE("lcNoAndDate", "NA"),
    LC_NO("lcNo", "lc_no"),
    LC_DATE("lcDate", "lc_date"),
    CONTRAC_NO_AND_DATE("contracNoAndDate", "NA"),
    CONTRAC_NO("contracNo", "contrac_no"),
    CONTRACT_DATE("contractDate", "contract_date"),
    BUYERS_NAME_AND_ADDRESS("buyersNameAndAddress", "NA"),
    BUYERS_NAME("buyersName", "buyers_name"),
    BUYERS_ADDRESS("buyersAddress", "buyers_address"),
    SELLERS_NAME_AND_ADDRESS("sellersNameAndAddress", "NA"),
    SELLERS_NAME("sellersName", "sellers_name"),
    SELLERS_ADDRESS("sellersAddress", "sellers_address"),
    THIRD_PARTY_AND_ADDRESS("thirdPartyNameAndAddress", "NA"),
    THIRD_PARTY_NAME("thirdPartyName", "third_party_name"),
    THIRD_PARTY_ADDRESS("thirdPartyAddress", "third_party_address"),
    TRANSACTING_PARTIES_AEO("transactingPartiesAeo", "transacting_parties_aeo"),
    INSURANCE("insurance", "insurance"),
    VALUATION_HSS("valuationHss", "hss"),
    LOADING("loading", "loading"),
    COMMN("commn", "commn"),
    PAY_TERMS("payTerms", "pay_terms"),
    VALUATION_TERMS("valTerms", "valuation_terms"),
    RELTD("reltd", "reltd"),
    SVB_CH("svbCh", "svb_ch"),
    SVB_NO("svbNo", "svb_no"),
    VALUATION_DATE("valuationDate", "valuation_date"),
    VALUATION_INV_VALUE("invValue", "valuation_inv_value"),
    LOA("loa", "loa"),
    C_B("cb", "c_b"),
    COC("coc", "coc"),
    COP("cop", "cop"),
    HIND_CHG("hindChg", "hind_chg"),
    G_S("gs", "g_s"),
    DOC_CH("docCh", "doc_ch"),
    COO("coo", "coo"),
    R_LF("rLf", "r_lf"),
    OTH_COST("othCost", "oth_cost"),
    LD_ULD("ldUld", "ld_uld"),
    WS("ws", "ws"),
    OTC("otc", "otc"),
    MISC_CHG("miscChg", "misc_chg"),
    ASS_VALUE("assValue", "ass_value"),
    FS("fs", "fs"),
    PQ("pq", "pq"),
    DC("dc", "dc"),
    WC("wc", "wc"),
    AQ("aq", "aq"),
    UPI("upi", "upi"),
    ITEM_DTLS_COO("itemDtlsCoo", "item_dtls_coo"),
    C_QTY("cQty", "c_qty"),
    C_UQC("cUqc", "c_uqc"),
    S_QTY("sQty", "s_qty"),
    S_UQC("sUqc", "s_uqc"),
    SCH("sch", "sch"),
    STND_PR("stndPr", "stnd_pr"),
    RSP("rsp", "rsp"),
    REIMP("reimp", "reimp"),
    PROV("prov", "prov"),
    END_USE("endUse", "end_use"),
    PRODN_CNTRL("prodnCntrl", "na"),
    PRODN("prodn", "prodn"),
    CNTRL("cntrl", "cntrl"),
    QUALFR("qualfr", "qualfr"),
    CONTNT("contnt", "contnt"),
    STMNT("stmnt", "stmnt"),
    SUP_DOCS("supDocs", "sup_docs"),
    NOTN_NO("notnNo", "notn_no"),
    NOTN_SR_NO("notnSrNo", "notn_sr_no"),
    BCD_DUTY_FG("bcdDutyFg", "bcd_duty_fg"),
    ACD_DUTY_NOTN_NO("acdDutyNotnNo", "acd_duty_notn_no"),
    ACD_DUTY_NOTN_SR_NO("acdDutyNotnSrNo", "acd_duty_notn_sr_no"),
    ACD_DUTY_FG("acdDutyFg", "acd_duty_fg"),
    SWS_DUTY_NOTN_NO("swsDutyNotnNo", "sws_duty_notn_no"),
    SWS_DUTY_NOTN_SR_NO("swsDutyNotnSrNo", "sws_duty_notn_sr_no"),
    SWS_DUTY_FG("swsDutyFg", "sws_duty_fg"),
    SAD_DUTY_NOTN_NO("sadDutyNotnNo", "sad_duty_notn_no"),
    SAD_DUTY_NOTN_SR_NO("sadDutyNotnSrNo", "sad_duty_notn_sr_no"),
    SAD_DUTY_FG("sadDutyFg", "sad_duty_fg"),
    IGST_NOTN_NO("igstNotnNo", "igst_notn_no"),
    IGST_NOTN_SR_NO("igstNotnSrNo", "igst_notn_sr_no"),
    IGST_DUTY_FG("igstDutyFg", "igst_duty_fg"),
    CESS_NOTN_NO("cessNotnNo", "cess_notn_no"),
    CESS_NOTN_SR_NO("cessNotnSrNo", "cess_notn_sr_no"),
    CESS_DUTY_FG("cessDutyFg", "cess_duty_fg"),
    ADD_DUTY_NOTN_NO("addDutyNotnNo", "add_duty_fg"),
    ADD_DUTY_NOTN_SR_NO("addDutyNotnSrNo", "add_duty_fg"),
    ADD_DUTY_FG("addDutyFg", "add_duty_fg"),
    CVD_DUTY_NOTN_NO("cvdDutyNotnNo", "cvd_duty_fg"),
    CVD_DUTY_NOTN_SR_NO("cvdDutyNotnSrNo", "cvd_duty_fg"),
    CVD_DUTY_FG("cvdDutyFg", "cvd_duty_fg"),
    SG_NOTN_NO("sgNotnNo", "sg_notn_no"),
    SG_NOTN_SR_NO("sgNotnSrNo", "sg_notn_sr_no"),
    SG_RATE("sgRate", "sg_rate"),
    SG_AMT("sgAmt", "sg_amt"),
    SG_DUTY_FG("sgDutyFg", "sg_duty_fg"),
    SP_EXD_NOTN_NO("spExdNotn_no", "sp_exd_notn_no"),
    SP_EXD_NOTN_SR_NO("spExdNotnSrNo", "sp_exd_notn_sr_no"),
    SP_EXD_RATE("spExdRate", "sp_exd_rate"),
    SP_EXD_AMT("spExdAmt", "sp_exd_amt"),
    SP_EXD_DUTY_FG("spExdDutyFg", "sp_exd_duty_fg"),
    CH_CESS_NOTN_NO("chCessNotnNo", "ch_cess_notn_no"),
    CH_CESS_NOTN_SR_NO("chCessNotnSrNo", "ch_cess_notn_sr_no"),
    CH_CESS_RATE("chCessRate", "ch_cess_rate"),
    CH_CESS_AMT("chCessAmt", "ch_cess_amt"),
    CH_CESS_DUTY_FG("chCessDutyFg", "ch_cess_duty_fg"),
    TTA_NOTN_NO("ttaNotnNo", "tta_notn_no"),
    TTA_NOTN_SR_NO("ttaNotnSrNo", "tta_notn_sr_no"),
    TTA_RATE("ttaRate", "tta_rate"),
    TTA_AMT("ttaAmt", "tta_amt"),
    TTA_DUTY_FG("ttaDutyFg", "tta_duty_fg"),
    OTH_DUTY_CESS_NOTN_NO("othDutyCessNotnNo", "oth_duty_ess_notn_no"),
    OTH_DUTY_CESS_NOTN_SR_NO("othDutyCessNotnSrNo", "oth_duty_cess_notn_sr_no"),
    OTH_DUTY_CESS_RATE("othDutyCessRate", "oth_duty_cess_rate"),
    OTH_DUTY_CESS_AMT("othDutyCessAmt", "oth_duty_cess_amt"),
    OTH_DUTY_CESS_DUTY_FG("othDutyCessDutyFg", "oth_duty_cess_duty_fg"),
    CVD_EDC_NOTN_NO("cvdEdcNotnNo", "cvd_edc_notn_no"),
    CVD_EDC_NOTN_SR_NO("cvdEdcNotnSrNo", "cvd_edc_notn_sr_no"),
    CVD_EDC_RATE("cvdEdcRate", "cvd_edc_rate"),
    CVD_EDC_AMT("cvdEdcAmt", "cvd_edc_amt"),
    CVD_EDC_DUTY_FG("cvdEdcDutyFg", "cvd_edc_duty_fg"),
    CVD_HEC_NOTN_NO("cvdHecNotnNo", "cvd_hec_notn_no"),
    CVD_HEC_NOTN_SR_NO("cvdHecNotnSrNo", "cvd_hec_notn_sr_no"),
    CVD_HEC_RATE("cvdHecRate", "cvd_hec_rate"),
    CVD_HEC_AMT("cvdHecAmt", "cvd_hec_amt"),
    CVD_HEC_DUTY_FG("cvdHecDutyFg", "cvd_hec_duty_fg"),
    CUS_EDC_NOTN_NO("cusEdcNotnNo", "cus_edc_notn_no"),
    CUS_EDC_NOTN_SR_NO("cusEdcNotnSrNo", "cus_edc_notn_sr_no"),
    CUS_EDC_RATE("cusEdcRate", "cus_edc_rate"),
    CUS_EDC_AMT("cusEdcAmt", "cus_edc_amt"),
    CUS_EDC_DUTY_FG("cusEdcDutyFg", "cus_edc_duty_fg"),
    CUS_HEC_NOTN_NO("cusHecNotnNo", "cus_hec_notn_no"),
    CUS_HEC_NOTN_SR_NO("cusHecNotnSrNo", "cus_hec_notn_sr_no"),
    CUS_HEC_RATE("cusHecRate", "cus_hec_rate"),
    CUS_HEC_AMT("cusHecAmt", "cus_hec_amt"),
    CUS_HEC_DUTY_FG("cusHecDutyFg", "cus_hec_duty_fg"),
    NCD_NOTN_NO("ncdNotnNo", "ncd_notn_no"),
    NCD_NOTN_SR_NO("ncdNotnSrNo", "ncd_notn_sr_no"),
    NCD_RATE("ncdRate", "ncd_rate"),
    NCD_AMT("ncdAmt", "ncd_amt"),
    NCD_DUTY_FG("ncdDutyFg", "ncd_duty_fg"),
    AGGR_NOTN_NO("aggrNotnNo", "aggr_notn_no"),
    AGGR_NOTN_SR_NO("aggrNotnSrNo", "aggr_notn_sr_no"),
    AGGR_RATE("aggrRate", "aggr_rate"),
    AGGR_AMT("aggrAmt", "aggr_amt"),
    AGGR_DUTY_FG("aggrDutyFg", "aggr_duty_fg"),
    BILL_OF_ENTRY_SUBMISSION("billOfEntrySubmission", "NA"),
    BILL_OF_ENTRY_SUBMISSION_DATE("billOfEntrySubmissionDate", "bill_of_entry_submission_date"),
    BILL_OF_ENTRY_SUBMISSION_TIME("billOfEntrySubmissionTime", "bill_of_entry_submission_time"),
    BILL_OF_ENTRY_ASSESSMENT("billOfEntryAssessment", "NA"),
    BILL_OF_ENTRY_ASSESSMENT_DATE("billOfEntryAssessmentDate", "bill_of_entry_assessment_date"),
    BILL_OF_ENTRY_ASSESSMENT_TIME("billOfEntryAssessmentTime", "bill_of_entry_assessment_time"),
    BILL_OF_ENTRY_EXAMINATION("billOfEntryExamination", "NA"),
    BILL_OF_ENTRY_EXAMINATION_DATE("billOfEntryExaminationDate", "bill_of_entry_examination_date"),
    BILL_OF_ENTRY_EXAMINATION_TIME("billOfEntryExaminationTime", "bill_of_entry_examination_time"),
    BILL_OF_ENTRY_OOC("billOfEntryOoc", "NA"),
    BILL_OF_ENTRY_OOC_DATE("billOfEntryOocDate", "bill_of_entry_ooc_date"),
    BILL_OF_ENTRY_OOC_TIME("billOfEntryOocTime", "bill_of_entry_ooc_time");


    private final String value;

    private final String excelMappingKey;

    private static final Logger LOG = Logger.getLogger(BoeDetailsFields.class);

    public static BoeDetailsFields getFieldEnum(String value) throws PdfReaderException {
        BoeDetailsFields boeFieldToReturn = null;
        for (BoeDetailsFields sheetField : values()) {
            if (sheetField.getValue().equals(value)) {
                boeFieldToReturn = sheetField;
            }
        }

        if (boeFieldToReturn == null) {
            LOG.error("ERROR >> ImportSheetFields >> getFieldEnum >> VALUE >> " + value + " >> No enum found for the provided value");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.NO_ENUM_FOUND + "BoeDetailsFields." + value);
        }

        return boeFieldToReturn;
    }

    public static String getValueFromExcelMappingKey(String excelMappingKey){
        String value = null;
        for (BoeDetailsFields sheetField : values()) {
            if (sheetField.getExcelMappingKey().equals(excelMappingKey)) {
                value = sheetField.getValue();
            }
        }

        return value;
    }

}