package com.perennialsys.pdfreader.enums;

import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;

@AllArgsConstructor
@Getter
public enum SBItemDetailsFields {

    PROD_CODE("prodCode", "product_code"),
    PROD_DESC("prodDesc", "product_desc"),
    QTY_AS_PER_SHIPING_BILL("qtyAsPerShippingBill", "qty_as_per_invoice"),
    UQC("uqc", "uqc"),
    AVAILABLE_STOCK("availableStock", "available_stock"),
    QTY_CONSUMED_IN_CLAIM("qtyConsumedInClaim", "consumed_claimed_qty"),
    INV_SN("invSerialNo", "inv_sr_no"),
    ITM_SN("itemSerialNo", "item_sr_no"),
    RATE("rate", "rate"),
    ITM_VALUE_FC("itemValFc", "item_val_fc"),
    ITEM_HSN_CODE("itemHsnCode", "item_hsn_code"),
    ASSESSABLE_VALUE("assessableValue", "assessable_value"),
    FTA_BENEFIT_AVAILED("ftaBenefitAvailed", "fta_benefit_availed"),
    REWARD_BENEFIT("rewardBenefit", "reward_benefit"),
    ITE_FOB("itemFob", "item_fob"),
    PMV("pmv", "pmv"),
    IGST_STAT("igstStat", "igst_stat"),
    ITM_IGST_AMT("itmIgstAmt", "item_igst_amt"),
    SCH_CODE("schCode", "sch_code"),
    SCHEMA_DESCRIPTION("schemaDescription", "schema_desc"),
    SQC_MSR("sqcMsr", "sqc_msr"),
    PT_ABROAD("ptAbroad", "pt_abroad"),
    COMP_CESS("compCess", "comp_cess"),
    END_USE("endUse", "end_user"),
    THIRD_PARTY_ITEM("thirdPartyItem", "third_party_item"),
    DBK_INV_SNO("invSno", "dnk_inv_sno"),
    DNK_ITEM_SNO("itemSno", "dbk_item_sno"),
    DBK_SNO("dbkSrNo", "dnk_sno"),
    DBK_VAL("dbkValue", "dbk_val"),
    SBK_RATE("dbkRate", "dbk_rate"),
    DBK_AMT("dbkAmt", "dbk_amt"),
    ROSCTL_AMT("rosctlAmt", "rosctl_amt"),
    RODTEP_INV_SNO_ITEM_SNO("rosctlInvSnItmSn", "rosctl_inv_sn_itm_sn"),
    RODTEP_INV_SNO("rodtepInvSn", "rodtep_inv_sn"),
    RODTEP_ITM_SNO("rodtepItmSn", "rodtep_itm_no"),
    RODTEP_AMT("rodtepAmt", "rodtep_amt"),
    CUST_1("cust1", "custom_1"),
    CUST_2("cust2", "custom_2"),
    CUST_3("cust3", "custom_3"),
    //    STATUS("status", "NA),
    PROCESSING_STATUS("processingStatus", "NA");

    private final String value;

    private final String excelMappingKey;

    private static final Logger LOG = Logger.getLogger(SBItemDetailsFields.class);

    public static SBItemDetailsFields getFieldEnum(String value) throws PdfReaderException {
        SBItemDetailsFields sbItemFieldToReturn = null;
        for (SBItemDetailsFields sheetField : values()) {
            if (sheetField.getValue().equals(value)) {
                sbItemFieldToReturn = sheetField;
            }
        }

        if (sbItemFieldToReturn == null) {
            LOG.error("ERROR >> ExportSheetProductFields >> getFieldEnum >> VALUE >> " + value + " >> No enum found for the provided value");
            throw new PdfReaderException(ResponseCode.UNABLE_TO_PROCESS_REQUEST, ResponseMessage.NO_ENUM_FOUND + "SBItemDetailsFields." + value);
        }

        return sbItemFieldToReturn;
    }

    public static String getValueFromExcelMappingKey(String excelMappingKey){
        String value = null;
        for (SBItemDetailsFields sheetField : values()) {
            if (sheetField.getExcelMappingKey().equals(excelMappingKey)) {
                value = sheetField.getValue();
            }
        }

        return value;
    }
}
