package com.perennialsys.pdfreader.enums;

/**
 * <AUTHOR>
 * This enum contains template code and ref id
 */
public enum TemplateCode {

    SHIPPING_BILL(-1), BILL_OF_ENTRY(-2),
    SINGLE_WINDOW_DETAILS(-3), SINGLE_WINDOW_CONST_DETAILS(-4),
    SINGLE_WINDOW_CONTROL_DETAILS(-5), SUPPORTING_DOCS_DETAILS(-6),
    ADDITIONAL_CONTAINER_DETAILS(-7),
    BOE_WAREHOUSE_DETAILS(-8),
    BOE_PAYMENT_DETAILS(-9),
    BOE_BOND_DETAILS(-10),
    BILL_OF_ENTRY_DUTY_DRAWBACK(-11),
    BILL_OF_ENTRY_MOOWR(-12);

    private final long id;

    TemplateCode(long id) {
        this.id = id;
    }

    public long getId() {
        return id;
    }

    public static long getTemplateIdFromName(String templateName) {
        for (TemplateCode template : TemplateCode.values()) {
            if (template.name().equals(templateName)) {
                return template.getId();
            }
        }
        return 0;
    }
}
