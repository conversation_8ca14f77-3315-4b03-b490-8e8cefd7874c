package com.perennialsys.pdfreader.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApplicantType {

    INTERMEDIARY_PARTY("Intermediary"),
    EXPORT_PARTY("Exporter");

    private final String value;

    public static ApplicantType getApplicantType(String value) {
        ApplicantType apTypeToReturn = null;
        for (ApplicantType apType : values()) {
            if (apType.getValue().equals(value)) {
                apTypeToReturn = apType;
                break;
            }
        }

        if (apTypeToReturn == null) {
            throw new IllegalArgumentException("No enum constant com.perennialsys.exim.enums.ApplicantType." + value);
        }

        return apTypeToReturn;
    }

}
