package com.perennialsys.pdfreader.pdfParser;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.client.model.UpdateManyModel;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.WriteModel;
import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import com.perennialsys.pdfreader.constants.QueryOperators;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.db.mongo.MongoDao;
import com.perennialsys.pdfreader.enums.ApplicantType;
import com.perennialsys.pdfreader.enums.PDFSections;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.enums.SBItemDetailsFields;
import com.perennialsys.pdfreader.excel.StreamHelper;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.helper.FieldIdentificationHelper;
import com.perennialsys.pdfreader.helper.FileHelper;
import com.perennialsys.pdfreader.pdfParser.strategyParsers.ValueBelowFieldTableStrategyParser;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.util.PdfUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.validator.Normalizer;
import com.perennialsys.pdfreader.vo.DataNormalizerVO;
import com.perennialsys.pdfreader.vo.FieldIdentificationStrategyVO;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import com.perennialsys.pdfreader.vo.PdfSectionDetailsVO;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class ShippingBillIPdfParser{

    private static final Logger LOG = Logger.getLogger(ShippingBillIPdfParser.class);

    private final IService service;

    private final MongoDao mongoDao;

    private final List<DataNormalizerVO> dataNormalizarVoList;

    public ShippingBillIPdfParser(IService service, MongoDao mongoDao) {
        this.service = service;
        this.mongoDao = mongoDao;
        dataNormalizarVoList = service.getDataNormalizerList();
    }

    /**
     * @param fileUploadDetailsVo - File Upload details table entry.
     * <AUTHOR> Nagare
     * @since 26/12/2022
     * Steps -
     * 1. Convert the PDF file into HTML file save.
     * 2. Save it to amazon or to the local based on the property file value.
     * 3. Get the converted HTML file form the storage.
     * 4. Parse the HTML file to get the details.
     * 5. Save the data into mongo db.
     */
    public long parsePdf(FileUploadDetailsVO fileUploadDetailsVo, boolean isSftp) throws PdfReaderException, IOException {
        File pdfFile = StreamHelper.getFileFromLocation(fileUploadDetailsVo.getPan(), fileUploadDetailsVo.getFileType(),
                fileUploadDetailsVo.getFileName(), fileUploadDetailsVo.getFileLoc(), isSftp);

        String htmlFilePath = PdfUtil.parseAndStorePDFAsHtml(pdfFile, fileUploadDetailsVo.getPan(), fileUploadDetailsVo.getTxnId());

        RandomAccessFile fileReader = PdfUtil.getPdfHtmlFile(htmlFilePath);

        try{
            if(!PdfParsingHelper.ifSectionPresent(service.getPdfSectionDetails(PdfFileType.SHIPPING_BILL.name(), PDFSections.SHIPPING_BILL_DETAILS.getSectionKey()),
                    fileReader)){
                LOG.error("ERROR >> ShippingBillIPdfParser >> parsePdf >> TXN_ID >> " + fileUploadDetailsVo.getTxnId()
                        + " >> PAN >> " + fileUploadDetailsVo.getPan() + " >> FILE_NAME >> " + fileUploadDetailsVo.getFileName()
                        + " >> Invalid PDF File SB Details not found not found.");
                throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.INVALID_PDF_FILE);
            }

            Map<String, Long> pageDtlsMap = new HashMap<>();
            Map<String, Object> sbSummaryDetailsMap = fetchShippingBillSummarySectionData(fileReader, false, true, pageDtlsMap);

            if(null != sbSummaryDetailsMap && !sbSummaryDetailsMap.isEmpty()
                    && sbSummaryDetailsMap.containsKey(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue())){

                /*MongoCollection<SbDetailsDocument> sbDetailsCollection =
                        mongoDao.getMongoCollection(NoSqlDBTables.SB_DETAILS_STATEMENT, SbDetailsDocument.class);
                //Check if same BOE is present in the system or not
                if (!ValidationProcessor.checkUniqueSbNo(sbSummaryDetailsMap.get(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue()),
                        sbSummaryDetailsMap.get(SBInvoiceDetailsFields.SHIPPING_BILL_DATE.getValue()),
                        fileUploadDetailsVo.getPan(), sbDetailsCollection)) {
                    LOG.error("ERROR >> BillOfEntryPdfParser >> parsePdf >> TXN_ID >> " + fileUploadDetailsVo.getTxnId()
                            + " >> PAN >> " + fileUploadDetailsVo.getPan() + " >> FILE_NAME >> " + fileUploadDetailsVo.getFileName()
                            + ">> SB_NO >> " + sbSummaryDetailsMap.get(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue())
                            + " >> SB No Already present in the system.");
                    throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.BOE_NO_MUST_BE_UNIQUE);
                }*/


                List<Map<String, Object>> invoiceDetailList = fetchRepeatingSectionDetails(fileReader,
                        PDFSections.INVOICE_DETAILS.getSectionKey(), false, true, pageDtlsMap, true);

                List<Map<String, Object>> itemDetailList = fetchRepeatingSectionDetails(fileReader,
                        PDFSections.ITEM_DETAILS.getSectionKey(), false, true, pageDtlsMap, true);

                List<Map<String, Object>> dbkDetails = fetchDbkDetailsSectionData(fileReader,
                        PDFSections.DRAWBACK_AND_ROSL_CLAIM.getSectionKey(), false, true, pageDtlsMap, true);

                List<Map<String, Object>> invoiceCurrencyDetails = fetchDbkDetailsSectionData(fileReader,
                        PDFSections.INVOICE_DETAILS_SUB_SECTION.getSectionKey(), false, true, pageDtlsMap, true);

                List<Map<String, Object>> rodtepDetails = fetchDbkDetailsSectionData(fileReader,
                        PDFSections.RODTEP_DETAILS.getSectionKey(), false, true, pageDtlsMap, true);

                putDataIntoMongoDb(sbSummaryDetailsMap, invoiceDetailList, invoiceCurrencyDetails, itemDetailList, dbkDetails,
                        rodtepDetails, fileUploadDetailsVo.getTxnId(), fileUploadDetailsVo.getSubTxnId(), fileUploadDetailsVo.getPan());
            } else {
                LOG.error("ERROR >> ShippingBillIPdfParser >> parsePdf >> TXN_ID >> " + fileUploadDetailsVo.getTxnId()
                        + " >> PAN >> " + fileUploadDetailsVo.getPan() + " >> FILE_NAME >> " + fileUploadDetailsVo.getFileName() + " >> SB number not found.");
                throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.INVALID_PDF_FILE);
            }
        }finally {
            FileHelper.removeLocalCopy(Collections.singletonList(htmlFilePath));
        }

        return PdfUtil.getTotalPageCount(fileReader);
    }

    public Map<String, Object> fetchShippingBillSummarySectionData(RandomAccessFile fileReader, boolean startFromStart,
                                                                   boolean startFromPageStart,
                                                                   Map<String, Long> pageDtlsMap
    ) throws PdfReaderException {
        LOG.info("START >> PdfParsingProcessorImpl >> processShippingBillDetailsFields >> FILE_TYPE >> "
                + PdfFileType.SHIPPING_BILL.name());
        Map<String, Object> dataMap = new HashMap<>();
        //Get all the Shipping Bill Details level fields for the file.
        List<PdfFieldStrategyMappingVO> pdfFieldsList = service.getPdfFieldStrategyMapping("shipping_bill_details",
                PdfFileType.SHIPPING_BILL.name());

        if (null != pdfFieldsList && !pdfFieldsList.isEmpty()) {
            int pageCount = !startFromStart && pageDtlsMap.containsKey("current-page-count")
                    ? pageDtlsMap.get("current-page-count").intValue() : 0;
            long pageStartPointer = pageDtlsMap.containsKey("current-page-start-pointer")
                    ? pageDtlsMap.get("current-page-start-pointer").intValue() : 0L;

            //Traverse over the fields list and get the data for the fields.
            for (PdfFieldStrategyMappingVO pdfFieldStrategyMapping : pdfFieldsList) {
                if (!pdfFieldStrategyMapping.getStrategy().isFieldSpecific()) {
                    //Get all the data div from the page.
                    List<PdfDivBean> pageDivList = new ArrayList<>();
                    List<PdfDivBean> otherDivList = new ArrayList<>();

                    pageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, pageCount, true, pageDivList,
                            otherDivList, startFromPageStart, pageStartPointer);
                    double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

                    //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
                    List<PdfDivBean> fieldNameDivList = PdfUtil.getAllFieldNameDivs(pageDivList);

                    PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
                    fieldDivMetadata.setPageWidth(pageWidth);

                    boolean fieldFound = PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldStrategyMapping.getFieldName(), fieldNameDivList, fieldDivMetadata);
                    if (fieldFound) {
                        PdfParsingHelper.checkAndSetFieldWrapperDiv(fieldDivMetadata, otherDivList);
                        dataMap.put(pdfFieldStrategyMapping.getMappingKey(), PdfParsingHelper.fetchFieldValueBasedOnStrategy(pdfFieldStrategyMapping, pageDivList, fieldDivMetadata));

                        if (pdfFieldStrategyMapping.getMappingKey().equals(SBInvoiceDetailsFields.SHIPPING_BILL_NO.getValue())) {
                            //set the Applicant type based on if the field value is present or not.
                            if (dataMap.containsKey(pdfFieldStrategyMapping.getMappingKey())
                                    && dataMap.get(pdfFieldStrategyMapping.getMappingKey()) != null) {
                                dataMap.put(SBInvoiceDetailsFields.APPLICANT_TYPE.getValue(), ApplicantType.EXPORT_PARTY.getValue());
                            } else {
                                dataMap.put(SBInvoiceDetailsFields.APPLICANT_TYPE.getValue(), ApplicantType.INTERMEDIARY_PARTY.getValue());
                            }
                        }
                    }

                } else {
                    PdfParsingHelper.fetchValueFromFieldSpecificStrategy(pdfFieldStrategyMapping, fileReader, dataMap, null, pageCount);
                }
            }

            pageDtlsMap.put("current-page-count", (long) ++ pageCount);
            pageDtlsMap.put("current-page-start-pointer", pageStartPointer);
        } else {
            LOG.error("ERROR >> PdfParsingProcessorImpl >> processShippingBillDetailsFields >> FILE_TYPE >> "
                    + PdfFileType.SHIPPING_BILL.name() + " >> Field Strategy Mapping not found");
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }
        LOG.info("END >> PdfParsingProcessorImpl >> processShippingBillDetailsFields >> FILE_TYPE >> " + PdfFileType.SHIPPING_BILL.name());

        return dataMap;
    }

    /**
     * <AUTHOR> Nagare
     * @since 23/12/2022
     * </p>
     * Steps -
     * 1. Get to the invoice details page.
     * 2. Iterate over all the pages with Invoice details heading.
     * 3. Get the details for each invoice and store it into a map. (This will be used to map the items to the invoice in the further processing)
     * 4. Get to the item details section page.
     * 5. Iterate over all the pages with item details heading starting from the page after invoice details page.
     */
    public List<Map<String, Object>> fetchRepeatingSectionDetails(RandomAccessFile fileReader, String sectionKey,
                                                                  boolean startFromStart, boolean startFromPageStart,
                                                                  Map<String, Long> pageDtlsMap, boolean checkTillEnd
    ) throws PdfReaderException {
        int pageCount = !startFromStart && pageDtlsMap.containsKey("current-page-count") ? pageDtlsMap.get("current-page-count").intValue() : 0;
        long pageStartPointer = pageDtlsMap.containsKey("current-page-start-pointer") ? pageDtlsMap.get("current-page-start-pointer").intValue() : 0L;

        List<Map<String, Object>> dataList = new ArrayList<>();
        PdfSectionDetailsVO sectionDetailsVO = service.getPdfSectionDetails(PdfFileType.SHIPPING_BILL.name(), sectionKey);
        List<PdfDivBean> targetedFieldNameDivList = null;
        List<PdfDivBean> pageDivList = new ArrayList<>();
        List<PdfDivBean> otherDivList = new ArrayList<>();

        pageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, pageCount, startFromStart, pageDivList, otherDivList,
                startFromPageStart, pageStartPointer);

        double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

        PdfFieldDivMetadata sectionDivMetadata = new PdfFieldDivMetadata();
        //Get the first page where we get the Section heading has found.
        long tempPageStartPointer = pageStartPointer;
        int tempPageCount = pageCount;
        int sectionFoundCount = 0;

        List<PdfFieldStrategyMappingVO> pdfFieldMappingList = null;

        while (true) {
            try {

                //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
                targetedFieldNameDivList = pageDivList.stream()
                        .filter(pageDiv -> null != pageDiv.getFontWeight() && pageDiv.getFontWeight().equals("bold"))
                        .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator())
                        .collect(Collectors.toList());

                boolean sectionNameFound = PdfParsingHelper.searchFieldAndGetMetadata(sectionDetailsVO.getSectionDisplayName(), targetedFieldNameDivList, sectionDivMetadata);
                if (sectionNameFound) {
                    sectionFoundCount++;
                    pageStartPointer = tempPageStartPointer;
                    pageCount = tempPageCount;

                    //Start reading the invoice details form the page
                    if (!targetedFieldNameDivList.isEmpty()) {

                        PdfDivBean firstFieldDiv;

                        //Get the fields which are below the section heading div row.
                        targetedFieldNameDivList = targetedFieldNameDivList.stream()
                                .filter(pdfDiv -> pdfDiv.getTop() > sectionDivMetadata.getTop())
                                .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                                .collect(Collectors.toList());

                        //Get the first row of the section below section heading.
                        double topOfNextRow = PdfUtil.calculateTopOfNextRow(sectionDivMetadata.getFieldStartDiv(), sectionDetailsVO.getBufferValue());
                        Optional<PdfDivBean> sectionStartRowDiv = targetedFieldNameDivList.stream().sorted(new PdfDivBean.PdfDivBeanTopAttrComparator()).filter(pdfDiv -> pdfDiv.getTop() == topOfNextRow).findFirst();
                        if (sectionStartRowDiv.isPresent()) {
                            firstFieldDiv = sectionStartRowDiv.get();
                        } else {
                            LOG.error("ERROR >> PdfParsingProcessorImpl >> processItemLevelFields >> FILE_TYPE >> " + PdfFileType.SHIPPING_BILL.name()
                                    + " >> SECTION_KEY >> invoice_details >> Error while getting the start of section");
                            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                        }

                        //Get all the Invoice Details level fields for the file.
                        if(null == pdfFieldMappingList || pdfFieldMappingList.isEmpty()) {
                            pdfFieldMappingList = service.getPdfFieldStrategyMapping(sectionDetailsVO.getSectionKey(),
                                    PdfFileType.SHIPPING_BILL.name());
                        }

                        boolean repeatedSectionFound;
                        do {
                            repeatedSectionFound = false;
                            PdfDivBean finalFirstFieldDiv = firstFieldDiv;
                            targetedFieldNameDivList = targetedFieldNameDivList.stream().filter(pageDiv -> pageDiv.getTop() >= finalFirstFieldDiv.getTop()).collect(Collectors.toList());
                            Map<String, Object> dataMap = new HashMap<>();

                            //Start iterating for the fields
                            for (PdfFieldStrategyMappingVO pdfFieldStrategyMapping : pdfFieldMappingList) {
                                PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
                                fieldDivMetadata.setPageWidth(pageWidth);
                                boolean fieldFound = PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldStrategyMapping.getFieldName(), targetedFieldNameDivList, fieldDivMetadata);

                                if (fieldFound) {
                                    PdfParsingHelper.checkAndSetFieldWrapperDiv(fieldDivMetadata, otherDivList);
                                    if (pdfFieldStrategyMapping.getStrategy().isFieldSpecific()) {
                                        PdfParsingHelper.fetchValueFromFieldSpecificStrategy(pdfFieldStrategyMapping, fileReader, dataMap, pageDivList, pageCount);
                                    } else {
                                        Object value = PdfParsingHelper.fetchFieldValueBasedOnStrategy(pdfFieldStrategyMapping, pageDivList, fieldDivMetadata);
                                        dataMap.put(pdfFieldStrategyMapping.getMappingKey(), value);
                                    }
                                }
                            }

                            dataList.add(dataMap);

                            //Get to the next repeated section.
                            for (PdfDivBean pageDiv : targetedFieldNameDivList) {
                                if (pageDiv.getTop() > firstFieldDiv.getTop() && pageDiv.getValue().equals(firstFieldDiv.getValue())) {
                                    firstFieldDiv = pageDiv;
                                    repeatedSectionFound = true;
                                    break;
                                }
                            }
                        } while (repeatedSectionFound);
                    }
                }else if(!checkTillEnd && sectionFoundCount > 0){
                    break;
                }

                tempPageCount++;

                //Get all the data div from the page.
                pageDivList.clear();
                otherDivList.clear();
                tempPageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, tempPageCount, false, pageDivList, otherDivList, false, tempPageStartPointer);
                pageWidth = PdfUtil.fetchPageWidth(pageDivList);
            } catch (PdfReaderException e) {
                if (e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                    break;
                } else {
                    throw e;
                }
            }
        }

        pageDtlsMap.put("current-page-count", (long) ++ pageCount);
        pageDtlsMap.put("current-page-start-pointer", pageStartPointer);

        return dataList;
    }

    public List<Map<String, Object>> fetchDbkDetailsSectionData(RandomAccessFile fileReader, String sectionKey,
                                                                boolean startFromStart, boolean startFromPageStart,
                                                                Map<String, Long> pageDtlsMap, boolean checkTillEnd
    ) throws PdfReaderException {

        List<Map<String, Object>> dataList = new ArrayList<>();
        PdfSectionDetailsVO sectionDetailsVO = service.getPdfSectionDetails(PdfFileType.SHIPPING_BILL.name(), sectionKey);

        int pageCount = !startFromStart && pageDtlsMap.containsKey("current-page-count") ? pageDtlsMap.get("current-page-count").intValue() : 0;
        long pageStartPointer = pageDtlsMap.containsKey("current-page-start-pointer") ? pageDtlsMap.get("current-page-start-pointer").intValue() : 0L;
        PdfFieldDivMetadata sectionDivMetadata = new PdfFieldDivMetadata();

        List<PdfDivBean> targetedFieldNameDivList;
        List<PdfDivBean> pageDivList = new ArrayList<>();
        List<PdfDivBean> otherDivList = new ArrayList<>();

        pageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, pageCount, startFromStart, pageDivList, otherDivList,
                startFromPageStart, pageStartPointer);
        double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

        //Get the first page where we get the Section heading has found.
        long tempPageStartPointer = pageStartPointer;
        int tempPageCount = pageCount;
        int sectionFoundCount = 0;
        List<PdfFieldStrategyMappingVO> pdfFieldMappingList = null;
        while (true) {
            try {
                //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
                targetedFieldNameDivList = pageDivList.stream()
                        .filter(pageDiv -> null != pageDiv.getFontWeight() && pageDiv.getFontWeight().equals("bold"))
                        .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator())
                        .collect(Collectors.toList());

                if (!targetedFieldNameDivList.isEmpty()) {
                    boolean sectionNameFound = PdfParsingHelper.searchFieldAndGetMetadata(sectionDetailsVO.getSectionDisplayName(), targetedFieldNameDivList, sectionDivMetadata);
                    if (sectionNameFound) {

                        sectionFoundCount++;
                        pageStartPointer = tempPageStartPointer;
                        pageCount = tempPageCount;

                        //Get all the Invoice Details level fields for the file.
                        if(null == pdfFieldMappingList || pdfFieldMappingList.isEmpty()) {
                            pdfFieldMappingList = service.getPdfFieldStrategyMapping(sectionDetailsVO.getSectionKey(),
                                    PdfFileType.SHIPPING_BILL.name());
                        }

                        targetedFieldNameDivList = targetedFieldNameDivList.stream()
                                .filter(pageDiv -> pageDiv.getTop() > sectionDivMetadata.getFieldStartDiv().getTop())
                                .collect(Collectors.toList());

                        List<PdfFieldDivMetadata> fieldDivMetadataList = new ArrayList<>();
                        for (PdfFieldStrategyMappingVO pdfFieldMapping : pdfFieldMappingList) {
                            //Find the field and create the metadata for each field.
                            PdfFieldDivMetadata fieldMetadata = new PdfFieldDivMetadata();
                            fieldMetadata.setPageWidth(pageWidth);
                            fieldMetadata.setFieldMappingKey(pdfFieldMapping.getMappingKey());

                            if (PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldMapping.getFieldName(), targetedFieldNameDivList, fieldMetadata)) {
                                PdfParsingHelper.checkAndSetFieldWrapperDiv(fieldMetadata, otherDivList);
                                fieldDivMetadataList.add(fieldMetadata);
                            } else if (!pdfFieldMapping.isOptionalField()) {
                                LOG.error("ERROR >> fetchDbkDetailsSectionData >> FIELD_NAME >> " + pdfFieldMapping.getFieldName() + " >> NOT_FOUND");
                                throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                            }
                        }

                        dataList.addAll(ValueBelowFieldTableStrategyParser.fetchTableRowData(pageDivList, fieldDivMetadataList, pdfFieldMappingList.get(0).getBufferValue(), PdfFileType.SHIPPING_BILL.name(), sectionKey, "", null));
                    }else if(!checkTillEnd && sectionFoundCount > 0){
                        break;
                    }
                }

                tempPageCount++;

                //Get all the data div from the page.
                pageDivList.clear();
                otherDivList.clear();
                tempPageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, tempPageCount, false, pageDivList, otherDivList, false, tempPageStartPointer);
                pageWidth = PdfUtil.fetchPageWidth(pageDivList);
            } catch (PdfReaderException e) {
                if (e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                    break;
                } else {
                    throw e;
                }
            }
        }

        pageDtlsMap.put("current-page-count", (long) pageCount);
        pageDtlsMap.put("current-page-start-pointer", pageStartPointer);

        return dataList;
    }


    private void putDataIntoMongoDb(Map<String, Object> sbSummaryMap, List<Map<String, Object>> invDetailsList,
                                    List<Map<String, Object>> invoiceCurrencyDetails, List<Map<String, Object>> itemDetailsList,
                                    List<Map<String, Object>> dbkDetailsList, List<Map<String, Object>> rodtepDetails,
                                    String txnId, String subTxnId, String pan) throws PdfReaderException {
        LOG.info("START >> PdfParsingProcessorImpl >> putDataIntoMongoDb >> FILE_TYPE >> " + PdfFileType.SHIPPING_BILL.name());
        List<WriteModel<BasicDBObject>> bulkDocumentList = new ArrayList<>();
        final List<FieldIdentificationStrategyVO> fieldIdentificationStrategyVoList =
                service.getFieldIdentificationStrategyRepo().findAllByPanAndFileTypeAndIsActive(pan,
                        PdfFileType.SHIPPING_BILL.name(), true);
        //Set invoice level fields in setter.
        for (Map<String, Object> invDetailsMap : invDetailsList) {

            Object invSNo = invDetailsMap.get(SBInvoiceDetailsFields.S_NO.getValue());
            BasicDBList andList = new BasicDBList();
            andList.add(new BasicDBObject(SBInvoiceDetailsFields.TXN_ID.getValue(), txnId));
            andList.add(new BasicDBObject(SBInvoiceDetailsFields.PAN.getValue(), pan));
            andList.add(new BasicDBObject(SBInvoiceDetailsFields.INV_NO.getValue(),
                Normalizer.normalizeAndGetValue(SBInvoiceDetailsFields.INV_NO.getValue(),
                    invDetailsMap.get(SBInvoiceDetailsFields.INV_NO.getValue()), dataNormalizarVoList)));
            andList.add(new BasicDBObject(SBInvoiceDetailsFields.INV_DATE.getValue(),
                Normalizer.normalizeAndGetValue(SBInvoiceDetailsFields.INV_DATE.getValue(),
                    invDetailsMap.get(SBInvoiceDetailsFields.INV_DATE.getValue()), dataNormalizarVoList)));

            BasicDBObject query = new BasicDBObject(QueryOperators.$and, andList);

            BasicDBObject invoiceSetter = new BasicDBObject(SBInvoiceDetailsFields.TXN_ID.getValue(), txnId)
                    .append(SBInvoiceDetailsFields.PAN.getValue(), pan);

            if(StringUtils.isNotBlank(subTxnId)){
                invoiceSetter.append(SBInvoiceDetailsFields.SUB_TXN_ID.getValue(), subTxnId);
            }

            BasicDBObject update = new BasicDBObject().append(QueryOperators.$setOnInsert,
                    new BasicDBObject(SBInvoiceDetailsFields.CREATED_AT.getValue(), new Date()));

            LOG.info("PdfParsingProcessorImpl >> putDataIntoMongoDb >> Setting Invoice details in Sb Details");
            for (Map.Entry<String, Object> entry : invDetailsMap.entrySet()) {
                SBInvoiceDetailsFields sheetFieldEnum = SBInvoiceDetailsFields.getFieldEnum(entry.getKey());
                Object value = Normalizer.normalizeAndGetValue(sheetFieldEnum.getValue(), entry.getValue(), dataNormalizarVoList);

                invoiceSetter.append(sheetFieldEnum.getValue(), value);

            }

            LOG.info("PdfParsingProcessorImpl >> putDataIntoMongoDb >> Setting SB Summary Details in Sb Details");
            if (sbSummaryMap != null && !sbSummaryMap.isEmpty()) {
                for (Map.Entry<String, Object> entry : sbSummaryMap.entrySet()) {
                    SBInvoiceDetailsFields sheetFieldEnum = SBInvoiceDetailsFields.getFieldEnum(entry.getKey());
                    Object value = Normalizer.normalizeAndGetValue(sheetFieldEnum.getValue(), entry.getValue(), dataNormalizarVoList);

                    invoiceSetter.append(sheetFieldEnum.getValue(), value);
                }
            }

            LOG.info("PdfParsingProcessorImpl >> putDataIntoMongoDb >> Setting currency details in SB Details");
            if (invoiceCurrencyDetails != null && !invoiceCurrencyDetails.isEmpty()) {
                invoiceCurrencyDetails.stream()
                        .filter(invCurrencyDtl -> invCurrencyDtl.get(SBInvoiceDetailsFields.S_NO.getValue()).equals(invSNo))
                        .findFirst()
                        .ifPresent(
                                invCurrencyDtl -> invoiceSetter.append(SBInvoiceDetailsFields.INV_CURRENCY.getValue(), invCurrencyDtl.get(SBInvoiceDetailsFields.INV_CURRENCY.getValue()))
                        );
            }

            update.append(QueryOperators.$set, invoiceSetter);

            LOG.info("PdfParsingProcessorImpl >> putDataIntoMongoDb >> Setting Item details in SB details");
            if (itemDetailsList != null && !itemDetailsList.isEmpty()) {
                BasicDBObject arrayPushSetter = new BasicDBObject();

                List<Map<String, Object>> invoiceDbkDetails = (dbkDetailsList != null && !dbkDetailsList.isEmpty()) ? dbkDetailsList.stream()
                        .filter(dbkDetail -> dbkDetail.get(SBItemDetailsFields.DBK_INV_SNO.getValue())
                                .equals(invSNo))
                        .collect(Collectors.toList()) : null;

                List<Map<String, Object>> invoiceRodtepDetails = (rodtepDetails != null && !rodtepDetails.isEmpty()) ? rodtepDetails.stream()
                        .filter(dbkDetail -> dbkDetail.get(SBItemDetailsFields.RODTEP_INV_SNO.getValue())
                                .equals(invSNo))
                        .collect(Collectors.toList()) : null;

                arrayPushSetter.append(SBInvoiceDetailsFields.PRODUCTS.getValue(),
                        new BasicDBObject(QueryOperators.$each, createItemList(itemDetailsList, invoiceDbkDetails,
                                invoiceRodtepDetails, invSNo, fieldIdentificationStrategyVoList)));

                update.append(QueryOperators.$push, arrayPushSetter);
            }

            bulkDocumentList.add(new UpdateManyModel<>(query, update, new UpdateOptions().upsert(true)));

            if (bulkDocumentList.size() >= 1000) {
                LOG.info("PdfParsingProcessorImpl >> putDataIntoMongoDb >> COLLECTION_NAME >> " + NoSqlDBTables.SB_DETAILS_STATEMENT + " >>  Putting Data into Mongo db" + bulkDocumentList);
                mongoDao.bulkUpsert(bulkDocumentList, NoSqlDBTables.SB_DETAILS_STATEMENT);
                bulkDocumentList.clear();
            }

        }

        if (!bulkDocumentList.isEmpty()) {
            LOG.info("PdfParsingProcessorImpl >> putDataIntoMongoDb >> COLLECTION_NAME >> " + NoSqlDBTables.SB_DETAILS_STATEMENT + " >>  Putting Data into Mongo db" + bulkDocumentList);
            mongoDao.bulkUpsert(bulkDocumentList, NoSqlDBTables.SB_DETAILS_STATEMENT);
            bulkDocumentList.clear();
        }
        LOG.info("END >> PdfParsingProcessorImpl >> putDataIntoMongoDb >> FILE_TYPE >> " + PdfFileType.SHIPPING_BILL.name());
    }

    private List<BasicDBObject> createItemList(List<Map<String, Object>> itemDetailsList,
                                               List<Map<String, Object>> dbkDetailsList,
                                               List<Map<String, Object>> invoiceRodtepDetails,
                                               Object invSrNo,
                                               List<FieldIdentificationStrategyVO> fieldIdentificationStrategyVoList) throws PdfReaderException {
        List<Map<String, Object>> invItemDetailsList = itemDetailsList.stream()
                .filter(itemDetails -> itemDetails.get(SBItemDetailsFields.INV_SN.getValue())
                        .equals(invSrNo)).collect(Collectors.toList());

        List<BasicDBObject> itemSetterList = new ArrayList<>();
        for (Map<String, Object> itemDetailsMap : invItemDetailsList) {
            BasicDBObject itemSetter = new BasicDBObject();

            normalizeAndPutFieldValueInItems(itemDetailsMap, itemSetter, fieldIdentificationStrategyVoList);

            if (dbkDetailsList != null) {
                for (Map<String, Object> dbkDetailsMap : dbkDetailsList.stream()
                        .filter(dbkDetails -> dbkDetails.get(SBItemDetailsFields.DNK_ITEM_SNO.getValue())
                                .equals(itemDetailsMap.get(SBItemDetailsFields.ITM_SN.getValue()))).collect(Collectors.toList())) {

                    normalizeAndPutFieldValueInItems(dbkDetailsMap, itemSetter, fieldIdentificationStrategyVoList);
                }
            }

            if (invoiceRodtepDetails != null) {
                for (Map<String, Object> rodtepDetailsMap : invoiceRodtepDetails.stream()
                        .filter(dbkDetails -> dbkDetails.get(SBItemDetailsFields.RODTEP_ITM_SNO.getValue())
                                .equals(itemDetailsMap.get(SBItemDetailsFields.ITM_SN.getValue()))).collect(Collectors.toList())) {

                    normalizeAndPutFieldValueInItems(rodtepDetailsMap, itemSetter, fieldIdentificationStrategyVoList);
                }
            }

            itemSetterList.add(itemSetter);
        }
        return itemSetterList;
    }

    public void normalizeAndPutFieldValueInItems(Map<String, Object> valueDetailsMap, BasicDBObject itemSetter,
                                                 final List<FieldIdentificationStrategyVO> fieldIdentificationStrategyVoList) throws PdfReaderException {
        for (Map.Entry<String, Object> entry : valueDetailsMap.entrySet()) {

            if (!entry.getKey().equals(SBItemDetailsFields.DBK_INV_SNO.getValue())
                    && !entry.getKey().equals(SBItemDetailsFields.DNK_ITEM_SNO.getValue())
                    && !entry.getKey().equals(SBItemDetailsFields.RODTEP_INV_SNO.getValue())
                    && !entry.getKey().equals(SBItemDetailsFields.RODTEP_ITM_SNO.getValue())) {
                SBItemDetailsFields sheetFieldEnum = SBItemDetailsFields.getFieldEnum(entry.getKey());

                Object value = Normalizer.normalizeAndGetValue(sheetFieldEnum.getValue(), entry.getValue(), dataNormalizarVoList);

                itemSetter.append(sheetFieldEnum.getValue(), value);

                if (sheetFieldEnum.getValue().equals(SBItemDetailsFields.QTY_AS_PER_SHIPING_BILL.getValue())) {
                    //Available stock qty is same as the qty as per shipping bill
                    itemSetter.append(SBItemDetailsFields.AVAILABLE_STOCK.getValue(),value);
                }
            }

            checkAndIdentifyFields(entry.getKey(), entry.getValue(), fieldIdentificationStrategyVoList, itemSetter);
        }
    }

    private void checkAndIdentifyFields(String key, Object sourceValue, List<FieldIdentificationStrategyVO> fieldIdentificationStrategyVoList, BasicDBObject setter) throws PdfReaderException {

        if(null != sourceValue && StringUtils.isNotBlank(sourceValue.toString())){
            Optional<FieldIdentificationStrategyVO> strategyVoOptional = fieldIdentificationStrategyVoList.stream()
                    .filter(strategyVo -> strategyVo.getSourceField().equals(key))
                    .findFirst();

            if (strategyVoOptional.isPresent()) {
                FieldIdentificationStrategyVO strategyVO = strategyVoOptional.get();
                String targetFieldValue = FieldIdentificationHelper.getFieldValueUsingStrategy(strategyVO, sourceValue.toString());

                if (StringUtils.isNotBlank(targetFieldValue)) {
                    setter.append(strategyVO.getTargetField(), targetFieldValue);
                }

            }
        }
    }
}
