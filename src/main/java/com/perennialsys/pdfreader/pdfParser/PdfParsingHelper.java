package com.perennialsys.pdfreader.pdfParser;

import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.ExportSheetProductFields;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.PdfParsingStratergies;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.enums.SBItemDetailsFields;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.pdfParser.strategyParsers.CustomHouseNameStrategyParser;
import com.perennialsys.pdfreader.pdfParser.strategyParsers.NameAndAddressMultiLineStrategyParser;
import com.perennialsys.pdfreader.pdfParser.strategyParsers.ValueBelowFieldStrategyParser;
import com.perennialsys.pdfreader.pdfParser.strategyParsers.ValueBesideFieldStrategyParser;
import com.perennialsys.pdfreader.pdfParser.strategyParsers.ValueSeperatedBySpace;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.PdfUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import com.perennialsys.pdfreader.vo.PdfSectionDetailsVO;
import org.apache.log4j.Logger;
import org.json.JSONObject;

import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class PdfParsingHelper {
    private static final Logger LOGGER = Logger.getLogger(PdfParsingHelper.class);

    public static boolean searchFieldAndGetMetadata(String fieldName, List<PdfDivBean> fieldNameDivList,
                                                    PdfFieldDivMetadata fieldDivMetadata) throws PdfReaderException {
        LOGGER.info("START >> PdfUtil >> searchFieldAndGetMetadata >> FIELD_NAME >> " + fieldName);

        if (StringUtils.isBlank(fieldName)) {
            LOGGER.error("ERROR >> PdfUtil >> searchFieldAndGetMetadata >> Invalid Field Name Found");
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }

        boolean fieldFound = false;
        String[] fieldNameArray = fieldName.split(" ");

        Optional<PdfDivBean> fieldNamePartOptional = fieldNameDivList.parallelStream()
                .filter(pageDiv -> pageDiv.getValue().equals(fieldNameArray[0]))
                .findFirst();
        List<PdfDivBean> fieldNameDivListSameTop = null;
        //Set the field start div details
        if (fieldNamePartOptional.isPresent()) {
            PdfDivBean fieldPartDiv = fieldNamePartOptional.get();
            fieldDivMetadata.setTop(fieldPartDiv.getTop());
            fieldDivMetadata.setFieldStartDiv(fieldPartDiv);
            //Get all the field Divs with same top and arrange them based on the left value.
            fieldNameDivListSameTop = fieldNameDivList.stream()
                .filter(pageDiv -> pageDiv.getTop() == fieldPartDiv.getTop()
                        /*&& pageDiv.getLeft() >= fieldPartDiv.getLeft()*/)
                .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                .collect(Collectors.toList());
            fieldFound = true;
        } else {
            LOGGER.error("ERROR >> PdfUtil >> searchFieldAndGetMetadata >> FIELD_NAME >> " + fieldName + " >> Field Not Found");
        }

        //Set the field end div details
        if (fieldFound) {
            int count = 0;
            int i = 0;
            while (i < fieldNameArray.length) {
                if (count < fieldNameDivListSameTop.size()) {
                    if (!fieldNameDivListSameTop.get(count).getValue().equals(fieldNameArray[i])) {
                        fieldFound = false;
                        i = 0;
                    } else {
                        fieldFound = true;
                        fieldDivMetadata.setFieldEndDiv(fieldNameDivListSameTop.get(count));
                        if (i == 0 && count != 0) {
                            fieldDivMetadata.setFieldStartDiv(fieldNameDivListSameTop.get(count));
                            fieldDivMetadata.setPrevFieldDiv(fieldNameDivListSameTop.get(count - 1));
                        }
                        i++;
                    }
                } else {
                    break;
                }
                count++;
            }

            if (count < fieldNameDivListSameTop.size()) {
                fieldDivMetadata.setNextFieldDiv(fieldNameDivListSameTop.get(count));
            }

            if (!fieldFound) {
                LOGGER.error("ERROR >> PdfUtil >> searchFieldAndGetMetadata >> FIELD_NAME >> " + fieldName + " >> Field Not Found");
            }
        }

        LOGGER.info("END >> PdfUtil >> searchFieldAndGetMetadata >> FIELD_NAME >> " + fieldName + " >> FIELD_METADATA >> " + fieldDivMetadata);
        return fieldFound;
    }

    public static String fetchValue(double maxLeftAttrValue, PdfFieldDivMetadata fieldDivMetadata,
                                    List<PdfDivBean> targetedPdfDivWithSameTop, boolean checkSpaceBetween) {

        StringBuilder valueStringBuilder = new StringBuilder();

        if (!targetedPdfDivWithSameTop.isEmpty()) {
            List<PdfDivBean> finalTargetedDivList = new ArrayList<>(targetedPdfDivWithSameTop);
            if(null != fieldDivMetadata.getFieldWrapperDiv()){
                targetedPdfDivWithSameTop.clear();
                targetedPdfDivWithSameTop.addAll(finalTargetedDivList.stream()
                        .filter(pdfDiv -> pdfDiv.getLeft() >= fieldDivMetadata.getFieldWrapperDiv().getLeft())
                        .collect(Collectors.toList()));
            }else if (PdfUtil.getNearestDivToField(targetedPdfDivWithSameTop, fieldDivMetadata)) {
                //Get the Div with the nearest left attribute to the field start div.
                //Traverse to the left in the div list.
                int valueDivStartIndex = getStartingValueDivIndex(targetedPdfDivWithSameTop, fieldDivMetadata.getNearestDivIndex());

                targetedPdfDivWithSameTop.clear();
                targetedPdfDivWithSameTop.addAll(finalTargetedDivList.stream()
                        .filter(pdfDiv -> pdfDiv.getLeft() >= finalTargetedDivList.get(valueDivStartIndex).getLeft())
                        .collect(Collectors.toList()));
            }

            if(!targetedPdfDivWithSameTop.isEmpty()) {
                //Start traversing through the value of each targeted div till we reach the next field div or end of the page.
                int count = 0;
                PdfDivBean previousDivBean = null;

                for (PdfDivBean pdfDivBean : targetedPdfDivWithSameTop) {
                    if (previousDivBean == null || maxLeftAttrValue == fieldDivMetadata.getPageWidth()
                            || pdfDivBean.getLeft() < maxLeftAttrValue &&
                            (null != fieldDivMetadata.getFieldWrapperDiv()
                                    || !checkSpaceBetween || PdfUtil.checkIfDivHaveSpaceBetween(previousDivBean, pdfDivBean))) {
                        if (count != 0) {
                            valueStringBuilder.append(" ");
                        }

                        valueStringBuilder.append(pdfDivBean.getValue());
                        count++;
                    } else {
                        break;
                    }

                    previousDivBean = pdfDivBean;
                }
            }
        }

        return valueStringBuilder.toString();
    }

    public static int getStartingValueDivIndex(List<PdfDivBean> targetedValueDivs, int nearestDivIndex) {
        int startingValDivIndex = 0;
        //Get the previous div until we get a div with differance between 2 divs is not equals to the width of a space.
        for (int i = nearestDivIndex; i >= 0; i--) {
            if (i > 0) {
                PdfDivBean pdfDivBean = targetedValueDivs.get(i);
                PdfDivBean previousDiv = targetedValueDivs.get(i - 1);

                if (!PdfUtil.checkIfDivHaveSpaceBetween(previousDiv, pdfDivBean)) {
                    startingValDivIndex = i;
                    break;
                }
            }
        }

        return startingValDivIndex;
    }

    public static List<PdfDivBean> getNextRowDivs(PdfDivBean previousRowStartDiv, List<PdfDivBean> pageDivList, double maxLeftAttrValue) {
        double nextRowTop = previousRowStartDiv.getTop() + 1;
        List<PdfDivBean> divBelowPreviousRowList = pageDivList.stream()
                .filter(pageDiv -> pageDiv.getLeft() >= previousRowStartDiv.getLeft()
                        && pageDiv.getTop() > previousRowStartDiv.getTop()
                        && pageDiv.getLeft() < maxLeftAttrValue)
                .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator()).collect(Collectors.toList());

        List<PdfDivBean> targetedDivList = new ArrayList<>();

        if(!divBelowPreviousRowList.isEmpty()) {
            while (targetedDivList.isEmpty()) {
                double finalNextRowTop = nextRowTop;
                targetedDivList = divBelowPreviousRowList.stream()
                        .filter(pdfDivBean -> pdfDivBean.getTop() <= finalNextRowTop && pdfDivBean.getColorCode() == null)
                        .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                        .collect(Collectors.toList());

                nextRowTop++;
            }
        }

        return targetedDivList;
    }

    public static List<PdfDivBean> getNextRowDivs(double leftAttrVal, double topAttrVal, List<PdfDivBean> pageDivList, double maxLeftAttrValue) {
        double nextRowTop = topAttrVal + 1;
        List<PdfDivBean> divBelowPreviousRowList = pageDivList.stream()
                .filter(pageDiv -> pageDiv.getLeft() >= leftAttrVal
                        && pageDiv.getTop() > topAttrVal
                        && pageDiv.getLeft() < maxLeftAttrValue)
                .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator()).collect(Collectors.toList());

        List<PdfDivBean> targetedDivList = new ArrayList<>();
        if(!divBelowPreviousRowList.isEmpty()) {
            while (targetedDivList.isEmpty()) {
                double finalNextRowTop = nextRowTop;
                targetedDivList = divBelowPreviousRowList.stream()
                        .filter(pdfDivBean -> pdfDivBean.getTop() <= finalNextRowTop && pdfDivBean.getColorCode() == null)
                        .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                        .collect(Collectors.toList());

                nextRowTop++;
            }
        }

        return targetedDivList;
    }

    public static List<PdfDivBean> getNextRowDivs(double leftAttrVal, double topAttrVal, double lineHeight, List<PdfDivBean> pageDivList,
                                                  double maxLeftAttrValue, double bufferValue) {
        double nextRowTop = topAttrVal + bufferValue + lineHeight;
        List<PdfDivBean> divBelowPreviousRowList = pageDivList.stream()
                .filter(pageDiv -> pageDiv.getLeft() >= leftAttrVal
                        && pageDiv.getTop() > topAttrVal
                        && pageDiv.getLeft() < maxLeftAttrValue)
                .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator()).collect(Collectors.toList());

        List<PdfDivBean> targetedDivList = new ArrayList<>();
        if(!divBelowPreviousRowList.isEmpty()) {
            while (targetedDivList.isEmpty()) {
                double finalNextRowTop = nextRowTop;
                targetedDivList = divBelowPreviousRowList.stream()
                        .filter(pdfDivBean -> pdfDivBean.getTop() <= finalNextRowTop && pdfDivBean.getColorCode() == null)
                        .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                        .collect(Collectors.toList());

                nextRowTop++;
            }
        }

        return targetedDivList;
    }

    public static String fetchFieldValueBasedOnStrategy(PdfFieldStrategyMappingVO eximPdfFieldStrategyMapping, List<PdfDivBean> pageDivList,
                                                        PdfFieldDivMetadata fieldDivMetadata) throws PdfReaderException {
        LOGGER.info("START >> PdfUtil >> fetchFieldValueBasedOnStrategy >> FIELD_NAME >> "
                + eximPdfFieldStrategyMapping.getFieldName() + " >> STRATEGY >> " + eximPdfFieldStrategyMapping.getStrategy().getStrategy());
        String value = null;
        switch (PdfParsingStratergies.valueOf(eximPdfFieldStrategyMapping.getStrategy().getStrategy())) {
            case VALUE_BESIDE_FIELD_SL: {
                value = ValueBesideFieldStrategyParser.fetchSingleLineValue(pageDivList, fieldDivMetadata, true);
                break;
            }
            case VALUE_BESIDE_FIELD_ML: {
                // We have not found any case where the value beside field has multiple line so we will implement this when we get the case.
                // value = ValueBesideFieldStrategyParser.fetchSingleLineValue(pageDivList, fieldDivMetadata);
                break;
            }
            case VALUE_BELOW_FIELD_SL: {
                value = ValueBelowFieldStrategyParser.fetchSingleLineValue(pageDivList, fieldDivMetadata, eximPdfFieldStrategyMapping.getBufferValue(), true);

                if(eximPdfFieldStrategyMapping.getFileType().equals(PdfFileType.BILL_OF_ENTRY.name())
                    && StringUtils.isNotBlank(value)){
                    if(eximPdfFieldStrategyMapping.getMappingKey().equals(BoeDetailsFields.HSS.getValue())) {
                        String[] valArray = value.split(" ");
                        value = valArray[0];
                    }
                }else if(eximPdfFieldStrategyMapping.getFileType().equals(PdfFileType.SHIPPING_BILL.name())
                        && StringUtils.isNotBlank(value)){
                    if(eximPdfFieldStrategyMapping.getMappingKey().equals(SBInvoiceDetailsFields.PORT_CODE.getValue()) ||
                            eximPdfFieldStrategyMapping.getMappingKey().equals(SBInvoiceDetailsFields.GSTIN.getValue()) ||
                            eximPdfFieldStrategyMapping.getMappingKey().equals(SBItemDetailsFields.ITEM_HSN_CODE.getValue())) {
                        String[] valArray = value.split(" ");
                        value = valArray[0];
                    }
                }

                break;
            }
            case VALUE_BELOW_FIELD_ML: {
                value = ValueBelowFieldStrategyParser.fetchMultiLineLineValue(pageDivList, fieldDivMetadata);
                break;
            }
            case BOE_DUTY_NOTN_NO_VALUE: {
                value = ValueBelowFieldStrategyParser.fetchDutyRateValue(pageDivList, fieldDivMetadata, eximPdfFieldStrategyMapping, 0);
                break;
            }
            case BOE_DUTY_NOTN_SNO_VALUE: {
                value = ValueBelowFieldStrategyParser.fetchDutyRateValue(pageDivList, fieldDivMetadata, eximPdfFieldStrategyMapping, 1);
                break;
            }
            case BOE_DUTY_RATE_VALUE: {
                value = ValueBelowFieldStrategyParser.fetchDutyRateValue(pageDivList, fieldDivMetadata, eximPdfFieldStrategyMapping, 2);
                break;
            }
            case BOE_DUTY_AMOUNT_VALUE: {
                value = ValueBelowFieldStrategyParser.fetchDutyRateValue(pageDivList, fieldDivMetadata, eximPdfFieldStrategyMapping, 3);
                break;
            }
            case BOE_DUTY_FG_VALUE: {
                value = ValueBelowFieldStrategyParser.fetchDutyRateValue(pageDivList, fieldDivMetadata, eximPdfFieldStrategyMapping, 4);
                break;
            }
            default: {
                break;
            }
        }

        LOGGER.info("END >> PdfUtil >> fetchFieldValueBasedOnStrategy >> FIELD_NAME >> "
                + eximPdfFieldStrategyMapping.getFieldName() + " >> STRATEGY >> " + eximPdfFieldStrategyMapping.getStrategy().getStrategy() + " >> VALUE >> " + value);
        return value;
    }

    public static void fetchValueFromFieldSpecificStrategy(PdfFieldStrategyMappingVO pdfFieldStrategyMapping,
                                                           RandomAccessFile fileReader, Map<String, Object> dataMap,
                                                           List<PdfDivBean> pageDivList, int pageCount) throws PdfReaderException {
        String value;
        switch (PdfParsingStratergies.valueOf(pdfFieldStrategyMapping.getStrategy().getStrategy())) {
            case CUSTOM_HOUSE_NAME_SB: {
                value = CustomHouseNameStrategyParser.fetchValueFromShippingBillPdf(fileReader);
                dataMap.put(pdfFieldStrategyMapping.getMappingKey(), value);
                break;
            }
            case VALUE_BESIDE_FIELD_SEPARATED_BY_SPACE_SL: {
                String[] valueStrArr = ValueSeperatedBySpace.fetchBesideFieldValue(fileReader, pdfFieldStrategyMapping);
                if (valueStrArr != null) {
                    if (SBInvoiceDetailsFields.IEC.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(pdfFieldStrategyMapping.getMappingKey(), valueStrArr[0]);
                    }if (SBInvoiceDetailsFields.GSTIN.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(pdfFieldStrategyMapping.getMappingKey(), valueStrArr[0]);
                         if (pdfFieldStrategyMapping.getFileType().equals(PdfFileType.SHIPPING_BILL.name())) {
                             if(valueStrArr.length > 1){
                                 dataMap.put(SBInvoiceDetailsFields.GSTIN_TYPE.getValue(), valueStrArr[1]);
                             }
                         }else if (pdfFieldStrategyMapping.getFileType().equals(PdfFileType.BILL_OF_ENTRY.name())) {
                             String [] gstinStrArr = valueStrArr[0].split("/");
                             if(gstinStrArr.length > 1){
                                dataMap.put(SBInvoiceDetailsFields.GSTIN_TYPE.getValue(), gstinStrArr[1]);
                            }
                        }
                    }if (BoeDetailsFields.GSTIN.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(pdfFieldStrategyMapping.getMappingKey(), valueStrArr[0]);
                        if(valueStrArr.length > 1){
                            dataMap.put(BoeDetailsFields.GSTIN_TYPE.getValue(), valueStrArr[1]);
                        }
                        dataMap.put(pdfFieldStrategyMapping.getMappingKey(), valueStrArr[0]);
                    }else if (SBInvoiceDetailsFields.RBI_WAVIER_NO_AND_DATE.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(SBInvoiceDetailsFields.RBI_WAVIER_NO.getValue(), valueStrArr[0]);
                        if(valueStrArr.length > 1) {
                            dataMap.put(SBInvoiceDetailsFields.RBI_WAVIER_DATE.getValue(), valueStrArr[1]);
                        }
                    }
                }
                break;
            }
            case VALUE_BELOW_FIELD_SEPARATED_BY_SPACE_SL: {
                boolean checkSpaceBetween = !pdfFieldStrategyMapping.getMappingKey().equals(BoeDetailsFields.PRODN_CNTRL.getValue())
                        && !pdfFieldStrategyMapping.getMappingKey().equals(SBInvoiceDetailsFields.INSURANCE.getValue());
                String[] valueStrArr = ValueSeperatedBySpace.fetchBelowFieldValue(fileReader, pdfFieldStrategyMapping,
                        checkSpaceBetween, pageCount);
                if (null != valueStrArr) {
                    if (SBInvoiceDetailsFields.INV_NO_AND_DATE.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(SBInvoiceDetailsFields.INV_NO.getValue(), valueStrArr[0]);
                        dataMap.put(SBInvoiceDetailsFields.INV_DATE.getValue(), valueStrArr[1]);
                    }else if (pdfFieldStrategyMapping.getMappingKey().equals(BoeDetailsFields.PRODN_CNTRL.getValue())) {
                        dataMap.put(BoeDetailsFields.PRODN.getValue(), valueStrArr[0]);
                        if(valueStrArr.length > 1) {
                            dataMap.put(BoeDetailsFields.CNTRL.getValue(), valueStrArr[1]);
                        }
                    }else if (ExportSheetProductFields.COMP_CESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(pdfFieldStrategyMapping.getMappingKey(), valueStrArr[0]);
                    }else if (SBInvoiceDetailsFields.INSURANCE.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(SBInvoiceDetailsFields.INSURANCE.getValue(), valueStrArr[0]);
                        dataMap.put(SBInvoiceDetailsFields.DISCOUNT.getValue(), valueStrArr[1]);
                        dataMap.put(SBInvoiceDetailsFields.COMMISSION.getValue(), valueStrArr[2]);
                    }
                }
                break;
            }
            case VALUE_BELOW_FIELD_SEPARATED_BY_NEW_LINE_ML: {
                Object[] valueStrArr = ValueBelowFieldStrategyParser.fetchMultiLineValueSeperatedByNewLine(pageDivList, pdfFieldStrategyMapping);
                if (null != valueStrArr) {
                    if (BoeDetailsFields.INV_NO_AND_DATE.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(BoeDetailsFields.INV_NO.getValue(), valueStrArr[0]);
                        if (valueStrArr.length > 1) {
                            dataMap.put(BoeDetailsFields.INV_DATE.getValue(), valueStrArr[1]);
                        }
                    }else if (BoeDetailsFields.PUR_ORDE_NO_AND_DATE.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(BoeDetailsFields.PUR_ORDE_NO.getValue(), valueStrArr[0]);
                        if (valueStrArr.length > 1) {
                            dataMap.put(BoeDetailsFields.PUR_ORDER_DATE.getValue(), valueStrArr[1]);
                        }
                    }else if (BoeDetailsFields.LC_NO_AND_DATE.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(BoeDetailsFields.LC_NO.getValue(), valueStrArr[0]);
                        if (valueStrArr.length > 1) {
                            dataMap.put(BoeDetailsFields.LC_DATE.getValue(), valueStrArr[1]);
                        }
                    }else if (BoeDetailsFields.CONTRAC_NO_AND_DATE.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                        dataMap.put(BoeDetailsFields.CONTRAC_NO.getValue(), valueStrArr[0]);
                        if (valueStrArr.length > 1) {
                            dataMap.put(BoeDetailsFields.CONTRACT_DATE.getValue(), valueStrArr[1]);
                        }
                    }
                }
                break;
            }
            case NAME_AND_ADDRESS_ML: {
                JSONObject valueJson = NameAndAddressMultiLineStrategyParser.fetchValue(fileReader, pdfFieldStrategyMapping);

                if (SBInvoiceDetailsFields.EXPORTER_NAME_ADDRESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                    dataMap.put(SBInvoiceDetailsFields.EXPORTER_NAME.getValue(), valueJson.get("name"));
                    if(valueJson.has("address")) {
                        dataMap.put(SBInvoiceDetailsFields.EXPORTER_ADDRESS.getValue(), valueJson.getString("address").substring(0, valueJson.getString("address").lastIndexOf(" ")));
                    }
                } else if (SBInvoiceDetailsFields.CONSIGNEE_NAME_ADDRESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                    dataMap.put(SBInvoiceDetailsFields.CONSIGNEE_NAME.getValue(), valueJson.get("name"));
                    dataMap.put(SBInvoiceDetailsFields.CONSIGNEE_ADDRESS.getValue(), valueJson.get("address"));
                } else if (SBInvoiceDetailsFields.THIRD_PARTY_NAME_ADDRESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                    dataMap.put(SBInvoiceDetailsFields.THIRD_PARTY_NAME.getValue(), valueJson.get("name"));
                    dataMap.put(SBInvoiceDetailsFields.THIRD_PARTY_ADDRESS.getValue(), valueJson.get("address"));
                } else if (BoeDetailsFields.SUPPLIER_NAME_AND_ADDRESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                    dataMap.put(BoeDetailsFields.SUPPLIER_NAME.getValue(), valueJson.get("name"));
                    dataMap.put(BoeDetailsFields.SUPPLIER_ADDRESS.getValue(), valueJson.get("address"));
                } else if (BoeDetailsFields.BUYERS_NAME_AND_ADDRESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                    dataMap.put(BoeDetailsFields.BUYERS_NAME.getValue(), valueJson.get("name"));
                    dataMap.put(BoeDetailsFields.BUYERS_ADDRESS.getValue(), valueJson.get("address"));
                } else if (BoeDetailsFields.SELLERS_NAME_AND_ADDRESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                    dataMap.put(BoeDetailsFields.SELLERS_NAME.getValue(), valueJson.get("name"));
                    dataMap.put(BoeDetailsFields.SELLERS_ADDRESS.getValue(), valueJson.get("address"));
                } else if (BoeDetailsFields.THIRD_PARTY_AND_ADDRESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                    dataMap.put(BoeDetailsFields.THIRD_PARTY_NAME.getValue(), valueJson.get("name"));
                    dataMap.put(BoeDetailsFields.THIRD_PARTY_ADDRESS.getValue(), valueJson.get("address"));
                } else if (BoeDetailsFields.IMPORTERS_NAME_AND_ADDRESS.getValue().equals(pdfFieldStrategyMapping.getMappingKey())) {
                    dataMap.put(BoeDetailsFields.IMPORTERS_NAME.getValue(), valueJson.get("name"));
                    dataMap.put(BoeDetailsFields.IMPORTERS_ADDRESS.getValue(), valueJson.get("address"));
                }

                break;
            }
            case CUSTOM_HOUSE_NAME_BOE: {
                value = CustomHouseNameStrategyParser.fetchValueFromBOEPdf(fileReader, pdfFieldStrategyMapping);
                dataMap.put(pdfFieldStrategyMapping.getMappingKey(), value);
                break;
            }
            case VALUE_BESIDE_FIELD_MULTI_COLUMN: {
                String[] valueStrArr = ValueSeperatedBySpace.fetchBesideFieldValue(fileReader, pdfFieldStrategyMapping);
                if (valueStrArr != null) {
                    if(pdfFieldStrategyMapping.getMappingKey().equals(BoeDetailsFields.BILL_OF_ENTRY_SUBMISSION.getValue())){
                        if(valueStrArr.length >= 2){
                            if(Objects.nonNull(valueStrArr[0])
                                    && Objects.nonNull(DateFormatUtil.formatToDate(valueStrArr[0]))) {
                                dataMap.put(BoeDetailsFields.BILL_OF_ENTRY_SUBMISSION_DATE.getValue(), valueStrArr[0]);
                                dataMap.put(BoeDetailsFields.BILL_OF_ENTRY_SUBMISSION_TIME.getValue(), valueStrArr[1]);
                            }
                        }
                    } else if(pdfFieldStrategyMapping.getMappingKey().equals(BoeDetailsFields.BILL_OF_ENTRY_ASSESSMENT.getValue())){
                        if(valueStrArr.length >= 2){
                            if(Objects.nonNull(valueStrArr[0])
                                    && Objects.nonNull(DateFormatUtil.formatToDate(valueStrArr[0]))) {
                                dataMap.put(BoeDetailsFields.BILL_OF_ENTRY_ASSESSMENT_DATE.getValue(), valueStrArr[0]);
                                dataMap.put(BoeDetailsFields.BILL_OF_ENTRY_ASSESSMENT_TIME.getValue(), valueStrArr[1]);
                            }
                        }
                    } else if(pdfFieldStrategyMapping.getMappingKey().equals(BoeDetailsFields.BILL_OF_ENTRY_EXAMINATION.getValue())){
                        if(valueStrArr.length >= 2){
                            if(Objects.nonNull(valueStrArr[0])
                                    && Objects.nonNull(DateFormatUtil.formatToDate(valueStrArr[0]))) {
                                dataMap.put(BoeDetailsFields.BILL_OF_ENTRY_EXAMINATION_DATE.getValue(), valueStrArr[0]);
                                dataMap.put(BoeDetailsFields.BILL_OF_ENTRY_EXAMINATION_TIME.getValue(), valueStrArr[1]);
                            }
                        }
                    } else if(pdfFieldStrategyMapping.getMappingKey().equals(BoeDetailsFields.BILL_OF_ENTRY_OOC.getValue())){
                        if(valueStrArr.length >= 2){
                            if(Objects.nonNull(valueStrArr[0])
                                    && Objects.nonNull(DateFormatUtil.formatToDate(valueStrArr[0]))) {
                                dataMap.put(BoeDetailsFields.BILL_OF_ENTRY_OOC_DATE.getValue(), valueStrArr[0]);
                                dataMap.put(BoeDetailsFields.BILL_OF_ENTRY_OOC_TIME.getValue(), valueStrArr[1]);
                            }
                        }
                    }
                }
                break;
            }
            default: {
                break;
            }
        }
    }

    public static boolean ifSectionPresent(PdfSectionDetailsVO sectionDetailsVO, RandomAccessFile fileReader){
        List<PdfDivBean> targetedFieldNameDivList;
        List<PdfDivBean> pageDivList;
        boolean sectionNameFound;

        PdfFieldDivMetadata sectionDivMetadata = new PdfFieldDivMetadata();
        //Get the first page where we get the Section heading has found.
        try {
            //Get all the data div from the page.
            pageDivList = PdfUtil.getAllRowsForThePage(fileReader, 0, true);

            //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
            targetedFieldNameDivList = pageDivList.stream()
                    .filter(pageDiv -> null != pageDiv.getFontWeight() && pageDiv.getFontWeight().equals("bold"))
                    .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator())
                    .collect(Collectors.toList());

            sectionNameFound = searchFieldAndGetMetadata(sectionDetailsVO.getSectionDisplayName(), targetedFieldNameDivList, sectionDivMetadata);
        }catch(Exception e){
            LOGGER.error("ERROR >> PdfParsingHelper >> ifSectionPresent >> SECTION_KEY >> "+sectionDetailsVO.getSectionKey(), e);
            sectionNameFound = false;
        }

        return sectionNameFound;
    }

    public static void checkAndSetFieldWrapperDiv(PdfFieldDivMetadata fieldDivMetadata, List<PdfDivBean> otherDivList){
        if(null != otherDivList && !otherDivList.isEmpty()) {
            //Added buffer of 1 pt while comparing the left of next div with the wrapper width and while comparing the wrapper left with start field left
            otherDivList.stream()
                    .filter(div -> div.getLeft() >= fieldDivMetadata.getFieldStartDiv().getLeft() -
                            (50 * (BoeDetailsFields.ITEM_DESC.getValue().equals(fieldDivMetadata.getFieldMappingKey()) ? 2 : 1)) &&
                            div.getWidth() > 5 &&
                            (null == fieldDivMetadata.getNextFieldDiv() || Math.floor(fieldDivMetadata.getNextFieldDiv().getLeft()+1) >= Math.floor(div.getLeft() + div.getWidth())) &&
                            div.getTop() <= fieldDivMetadata.getFieldStartDiv().getTop() &&
                            Math.floor(div.getLeft()) <= Math.floor(fieldDivMetadata.getFieldStartDiv().getLeft()+2)
                            && (null == fieldDivMetadata.getPrevFieldDiv() || Math.floor(div.getLeft()) >= Math.floor(fieldDivMetadata.getPrevFieldDiv().getLeft() - 1))
                    )
                    .max(Comparator.comparingDouble(PdfDivBean::getTop).thenComparing(PdfDivBean::getLeft))
                    .ifPresent(fieldDivMetadata::setFieldWrapperDiv);
        }
    }
}
