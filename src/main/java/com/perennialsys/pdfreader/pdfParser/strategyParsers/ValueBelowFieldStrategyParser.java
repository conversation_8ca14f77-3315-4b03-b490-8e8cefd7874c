package com.perennialsys.pdfreader.pdfParser.strategyParsers;

import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.pdfParser.PdfParsingHelper;
import com.perennialsys.pdfreader.util.NumberUtil;
import com.perennialsys.pdfreader.util.PdfUtil;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import org.apache.log4j.Logger;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public final class ValueBelowFieldStrategyParser {
    private static final Logger LOG = Logger.getLogger(ValueBelowFieldStrategyParser.class);

    /**
     * @param pageDivList          - List of all the page divs.
     * @param fieldDivMetadata     - Field name div metadata.
     * @param checkSpaceBetween
     * <AUTHOR>
     * @since 20/12/2022
     * </p>
     * Steps -
     * 1. Set the max left attribute value based on, if the next field div is present or not.
     * If the next div is present then set the max left attribute value = left of next field div.
     * If not then set the max left attribute value = Page Width.
     * 2. Calculate the top of next row using the line-height and Field Strategy Mapping Buffer Value.
     * Fetch the value from the next row
     * 3. Get all the divs in the next row with left less than the max left attr value.
     * 4. Get the Div with the nearest left attribute to the field start div.
     * 5. Traverse to the left in the div list.
     * 6. Get the previous div until we get a div with differance between 2 divs is not equals to the width of a space.
     * 7. Start traversing through the value of each targeted div till we reach the next field div or end of the page.
     */
    public static String fetchSingleLineValue(List<PdfDivBean> pageDivList, PdfFieldDivMetadata fieldDivMetadata,
                                              double bufferValue, boolean checkSpaceBetween) throws PdfReaderException {
        LOG.info("START >> ValueBelowFieldSingleLineStrategyParser >> fetchSingleLineValue");
        //Set the max left attribute value based on, if the next field div is present or not.
        double maxLeftAttrValue = fieldDivMetadata.getNextFieldDiv() != null ? fieldDivMetadata.getNextFieldDiv().getLeft() : fieldDivMetadata.getPageWidth();

        boolean isWrapperUsed = false;
        if(null != fieldDivMetadata.getFieldWrapperDiv() && fieldDivMetadata.getFieldWrapperDiv().getWidth() > 0){
            double wrapperDivWidth = NumberUtil.addDoubles(fieldDivMetadata.getFieldWrapperDiv().getLeft(), fieldDivMetadata.getFieldWrapperDiv().getWidth());
            if(wrapperDivWidth < maxLeftAttrValue) {
                maxLeftAttrValue = wrapperDivWidth;
                isWrapperUsed = true;
            }
        }

        //Calculate the top of next row using the line-height and Field Strategy Mapping Buffer Value.
        double topOfNextRow = PdfUtil.calculateTopOfNextRow(fieldDivMetadata.getFieldStartDiv(), bufferValue);

        //Get all the divs in the next row with left less than the max left attr value.
        List<PdfDivBean> targetedPdfDivWithSameTop = PdfUtil.getSameTopDivList(pageDivList, maxLeftAttrValue, topOfNextRow);

        if(targetedPdfDivWithSameTop.isEmpty()){
            double topOfNextRowDecimalPart = BigDecimal.valueOf(topOfNextRow).subtract(new BigDecimal(BigDecimal.valueOf(topOfNextRow).intValue())).doubleValue();
            double decimalPointCounter = 0;
            topOfNextRow = new BigDecimal(String.valueOf(topOfNextRow)).intValue();

            while (decimalPointCounter <= topOfNextRowDecimalPart && targetedPdfDivWithSameTop.isEmpty()){
                topOfNextRow = NumberUtil.addDoubles(topOfNextRow, 0.001);
                targetedPdfDivWithSameTop = PdfUtil.getSameTopDivList(pageDivList, maxLeftAttrValue, topOfNextRow);
                decimalPointCounter = NumberUtil.addDoubles(decimalPointCounter, 0.001);
            }
        }

        if(isWrapperUsed && (null == targetedPdfDivWithSameTop || targetedPdfDivWithSameTop.isEmpty())){
            //get the top value based on the next div left or page width.
            maxLeftAttrValue = fieldDivMetadata.getNextFieldDiv() != null ? fieldDivMetadata.getNextFieldDiv().getLeft() : fieldDivMetadata.getPageWidth();
            targetedPdfDivWithSameTop = PdfUtil.getSameTopDivList(pageDivList, maxLeftAttrValue, topOfNextRow);
        }

        String value = PdfParsingHelper.fetchValue(maxLeftAttrValue, fieldDivMetadata, targetedPdfDivWithSameTop, checkSpaceBetween);

        LOG.info("END >> ValueBelowFieldSingleLineStrategyParser >> fetchSingleLineValue >> VALUE >> " + value);
        return value;
    }


    /**
     * @param pageDivList      - List of all the divs in single page.
     * @param fieldDivMetadata - Field div metadata.
     * @return It returns the multiple line value for the field.
     * @throws PdfReaderException - Throws an Exim Exception if something unexpected happens in the code.
     * <AUTHOR> Nagare
     * @since 22/12/2022
     *
     * </p>
     * This method fetches teh value for the fields with VALUE_BELOW_FIELD strategy until it reaches the next row with fields
     */
    public static String fetchMultiLineLineValue(List<PdfDivBean> pageDivList, PdfFieldDivMetadata fieldDivMetadata) throws PdfReaderException {
        StringBuilder valueStringBuilder = new StringBuilder();
        //Set the max left attribute value based on, if the next field div is present or not.
        double maxLeftAttrValue = fieldDivMetadata.getNextFieldDiv() != null ? fieldDivMetadata.getNextFieldDiv().getLeft() : fieldDivMetadata.getPageWidth();
        double minLeftAttrValue = fieldDivMetadata.getFieldStartDiv().getLeft();
        if(null != fieldDivMetadata.getFieldWrapperDiv() && fieldDivMetadata.getFieldWrapperDiv().getWidth() > 0){
            double wrapperDivWidth = NumberUtil.addDoubles(fieldDivMetadata.getFieldWrapperDiv().getLeft(), fieldDivMetadata.getFieldWrapperDiv().getWidth());
            if(wrapperDivWidth < maxLeftAttrValue) {
                maxLeftAttrValue = wrapperDivWidth;
            }

            if(fieldDivMetadata.getFieldWrapperDiv().getLeft() < minLeftAttrValue) {
                minLeftAttrValue = fieldDivMetadata.getFieldWrapperDiv().getLeft();
            }
        }

        //Get all the divs in the next row with left less than the max left attr value.
        List<PdfDivBean> targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(minLeftAttrValue,
                fieldDivMetadata.getFieldStartDiv().getTop(), pageDivList, maxLeftAttrValue);

        int count = 0;
        while (targetedPdfDivWithSameTop.get(0).getFontWeight() == null ||
                !targetedPdfDivWithSameTop.get(0).getFontWeight().equals("bold")) {

            if (count != 0) {
                valueStringBuilder.append(" ");
            }
            valueStringBuilder.append(PdfParsingHelper.fetchValue(maxLeftAttrValue, fieldDivMetadata, targetedPdfDivWithSameTop, true));
            count++;

            targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(minLeftAttrValue,
                    targetedPdfDivWithSameTop.get(0).getTop(), pageDivList, maxLeftAttrValue);
        }

        return valueStringBuilder.toString();
    }

    public static Object[] fetchMultiLineValueSeperatedByNewLine(List<PdfDivBean> pageDivList, PdfFieldStrategyMappingVO fieldStrategyMapping) throws PdfReaderException {
        LOG.info("START >> fetchBelowFieldValue >> fetchValue");
        List<String> valueList = new ArrayList<>();

        //Get all the data dives for that page
        List<PdfDivBean> fieldNameDivList = PdfUtil.getAllFieldNameDivs(pageDivList);
        double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

        PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
        fieldDivMetadata.setPageWidth(pageWidth);

        if (PdfParsingHelper.searchFieldAndGetMetadata(fieldStrategyMapping.getFieldName(), fieldNameDivList, fieldDivMetadata)) {
            //Set the max left attribute value based on, if the next field div is present or not.
            double maxLeftAttrValue = fieldDivMetadata.getNextFieldDiv() != null ? fieldDivMetadata.getNextFieldDiv().getLeft() : fieldDivMetadata.getPageWidth();

            //Calculate the top of next row using the line-height and Field Strategy Mapping Buffer Value.
            double topOfNextRow = PdfUtil.calculateTopOfNextRow(fieldDivMetadata.getFieldStartDiv(), fieldStrategyMapping.getBufferValue());

            //TODO [30/03/2023] Mrunal Nagare - Adding this counter to check if the loop is running for long.
            // Will have to remove this after we implement the new strategy to fing the invoice date and invoice number for SB and BOE PDF.
            int count = 0;
            //Get all the divs in the next row with left less than the max left attr value.
            List<PdfDivBean> targetedPdfDivWithSameTop = PdfUtil.getSameTopDivList(pageDivList, maxLeftAttrValue, topOfNextRow);
            do {
                valueList.add(PdfParsingHelper.fetchValue(maxLeftAttrValue, fieldDivMetadata, targetedPdfDivWithSameTop, true));
                double previousRowTop = targetedPdfDivWithSameTop.get(0).getTop();
                targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(targetedPdfDivWithSameTop.get(0), pageDivList, maxLeftAttrValue);

                if(targetedPdfDivWithSameTop.isEmpty() || targetedPdfDivWithSameTop.stream().anyMatch(pageDiv -> pageDiv.getFontWeight() != null)){
                    targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(fieldDivMetadata.getFieldStartDiv().getLeft(), previousRowTop, pageDivList, maxLeftAttrValue);
                }
                count ++;
            } while (!targetedPdfDivWithSameTop.isEmpty() && targetedPdfDivWithSameTop.stream().noneMatch(pageDiv -> pageDiv.getFontWeight() != null) && count < 100);
        }

        LOG.info("END >> fetchBelowFieldValue >> fetchValue >> VALUE >> " + valueList);
        return valueList.isEmpty() ? null : valueList.toArray();
    }

    public static String fetchDutyRateValue(List<PdfDivBean> pageDivList, PdfFieldDivMetadata fieldDivMetadata,
                                            PdfFieldStrategyMappingVO pdfFieldStrategyMapping, int rowCount) throws PdfReaderException {
        StringBuilder valueStringBuilder = new StringBuilder();
        //Set the max left attribute value based on, if the next field div is present or not.
        double maxLeftAttrValue = fieldDivMetadata.getNextFieldDiv() != null ? fieldDivMetadata.getNextFieldDiv().getLeft() : fieldDivMetadata.getPageWidth();

        //Calculate the top of next row using the line-height and Field Strategy Mapping Buffer Value.
        double topOfNextRow = PdfUtil.calculateTopOfNextRow(fieldDivMetadata.getFieldStartDiv(), pdfFieldStrategyMapping.getBufferValue());
        //Get all the divs in the next row with left less than the max left attr value.
        List<PdfDivBean> targetedPdfDivWithSameTop = PdfUtil.getSameTopDivList(pageDivList, maxLeftAttrValue, topOfNextRow);

        int count = 0;
        while (true) {
            //targetedPdfDivWithSameTop.removeIf(div -> StringUtils.isNotBlank(div.getFontWeight()));
            if (count == rowCount) {
                valueStringBuilder.append(PdfParsingHelper.fetchValue(maxLeftAttrValue, fieldDivMetadata, targetedPdfDivWithSameTop, true));
                break;
            }

            count++;

            if(targetedPdfDivWithSameTop.isEmpty()){
                break;
            }

            targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(targetedPdfDivWithSameTop.get(0), pageDivList, maxLeftAttrValue);
        }

        return valueStringBuilder.toString();
    }
}
