package com.perennialsys.pdfreader.pdfParser.strategyParsers;

import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.pdfParser.PdfParsingHelper;
import com.perennialsys.pdfreader.util.NumberUtil;
import com.perennialsys.pdfreader.util.PdfUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import org.apache.log4j.Logger;
import org.json.JSONObject;

import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 20/12/2022
 * </p>
 * This class will contain the logic and code to fetch the custom house name from the PDF Html
 */
public final class NameAndAddressMultiLineStrategyParser {

    private static final Logger LOG = Logger.getLogger(NameAndAddressMultiLineStrategyParser.class);

    public static JSONObject fetchValue(RandomAccessFile fileReader,
                                        PdfFieldStrategyMappingVO fieldStrategyMapping) throws PdfReaderException {
        LOG.info("START >> NameAndAddressMultiLineStrategyParser >> fetchValue >> FIELD_NAME >> " + fieldStrategyMapping.getFieldName());
        JSONObject valueJson = new JSONObject();
        StringBuilder nameValueStringBuilder = new StringBuilder();
        StringBuilder addressValueStringBuilder = new StringBuilder();
        PdfFieldDivMetadata fieldDivMetadata = null;
        boolean fieldFound = false;
        List<PdfDivBean> pageDivList = new ArrayList<>();
        List<PdfDivBean> otherDivList = new ArrayList<>();
        int pageCount = 0;
        do {
            try {
                pageDivList.clear();
                otherDivList.clear();

                PdfUtil.getAllRowsForThePage(fileReader, pageCount, true, pageDivList , otherDivList, false, 0);

                List<PdfDivBean> fieldNameDivList = PdfUtil.getAllFieldNameDivs(pageDivList);
                double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

                fieldDivMetadata = new PdfFieldDivMetadata();
                fieldDivMetadata.setPageWidth(pageWidth);

                fieldFound = PdfParsingHelper.searchFieldAndGetMetadata(fieldStrategyMapping.getFieldName(), fieldNameDivList, fieldDivMetadata);
            } catch (PdfReaderException e) {
                if (e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                    break;
                } else {
                    throw e;
                }
            }
            pageCount++;
        } while (!fieldFound);

        if (fieldFound) {
            PdfParsingHelper.checkAndSetFieldWrapperDiv(fieldDivMetadata, otherDivList);

            //Set the max left attribute value based on, if the next field div is present or not.
            double maxLeftAttrValue = fieldDivMetadata.getNextFieldDiv() != null ? fieldDivMetadata.getNextFieldDiv().getLeft() : fieldDivMetadata.getPageWidth();
            double minimumLeftAttrValue = null != fieldDivMetadata.getFieldWrapperDiv() ? fieldDivMetadata.getFieldWrapperDiv().getLeft() : fieldDivMetadata.getFieldStartDiv().getLeft();
            if(null != fieldDivMetadata.getFieldWrapperDiv() && fieldDivMetadata.getFieldWrapperDiv().getWidth() > 0){
                double wrapperDivWidth = NumberUtil.addDoubles(fieldDivMetadata.getFieldWrapperDiv().getLeft(), fieldDivMetadata.getFieldWrapperDiv().getWidth());
                if(wrapperDivWidth < maxLeftAttrValue) {
                    maxLeftAttrValue = wrapperDivWidth;
                }
            }

            //Get all the divs in the next row with left less than the max left attr value.
            List<PdfDivBean> targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(minimumLeftAttrValue,
                    fieldDivMetadata.getFieldStartDiv().getTop(), pageDivList, maxLeftAttrValue);

            int lineCount = 0;
            while (!targetedPdfDivWithSameTop.isEmpty() && (targetedPdfDivWithSameTop.get(0).getFontWeight() == null ||
                    !targetedPdfDivWithSameTop.get(0).getFontWeight().equals("bold"))) {

                targetedPdfDivWithSameTop.removeIf(div -> StringUtils.isNotBlank(div.getFontWeight()));

                if (lineCount == 0) {
                    lineCount++;
                    nameValueStringBuilder.append(PdfParsingHelper.fetchValue(maxLeftAttrValue, fieldDivMetadata, targetedPdfDivWithSameTop, true));
                } else {
                    addressValueStringBuilder.append(" ");
                    addressValueStringBuilder.append(PdfParsingHelper.fetchValue(maxLeftAttrValue, fieldDivMetadata, targetedPdfDivWithSameTop, true));
                }

                targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(targetedPdfDivWithSameTop.get(0).getLeft(),
                        targetedPdfDivWithSameTop.get(0).getTop(), pageDivList, maxLeftAttrValue);
            }

            valueJson.put("name", nameValueStringBuilder.toString());
            valueJson.put("address", addressValueStringBuilder.toString());

            LOG.info("END >> NameAndAddressMultiLineStrategyParser >> fetchValue  >> FIELD_NAME >> "
                    + fieldStrategyMapping.getFieldName() + " >> VALUE >> " + valueJson);
        } else {
            LOG.error("ERROR >> NameAndAddressMultiLineStrategyParser >> fetchValue  >> FIELD_NAME >> "
                    + fieldStrategyMapping.getFieldName() + " >> Field Not Found in PDF >>  SEARCHED_PAGES >> " + pageCount);
        }
        return valueJson;
    }

}
