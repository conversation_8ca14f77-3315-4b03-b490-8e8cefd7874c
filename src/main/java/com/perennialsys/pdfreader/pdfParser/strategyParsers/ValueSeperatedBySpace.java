package com.perennialsys.pdfreader.pdfParser.strategyParsers;

import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.enums.PdfParsingStratergies;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.pdfParser.PdfParsingHelper;
import com.perennialsys.pdfreader.util.PdfUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import org.apache.log4j.Logger;

import java.io.RandomAccessFile;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @since 20/12/2022
 * </p>
 * This class will contain the logic and code to fetch the custom house name from the PDF Html
 */
public final class ValueSeperatedBySpace {

    private static final Logger LOG = Logger.getLogger(ValueSeperatedBySpace.class);

    public static String[] fetchBelowFieldValue(RandomAccessFile fileReader,
                                                PdfFieldStrategyMappingVO fieldStrategyMapping,
                                                boolean checkSpaceBetween, int pageCount) throws PdfReaderException {
        LOG.info("START >> fetchBelowFieldValue >> fetchValue");
        String value = null;

        boolean fieldFound;
        do {
            try {
                //Get all the data dives for that page
                List<PdfDivBean> pageDivList = PdfUtil.getAllRowsForThePage(fileReader, pageCount, true);
                List<PdfDivBean> fieldNameDivList = PdfUtil.getAllFieldNameDivs(pageDivList);
                double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

                PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
                fieldDivMetadata.setPageWidth(pageWidth);

                fieldFound = PdfParsingHelper.searchFieldAndGetMetadata(fieldStrategyMapping.getFieldName(), fieldNameDivList, fieldDivMetadata);

                if (fieldFound) {
                    value = ValueBelowFieldStrategyParser.fetchSingleLineValue(pageDivList, fieldDivMetadata,
                            fieldStrategyMapping.getBufferValue(), checkSpaceBetween);
                }

                pageCount++;
            } catch (PdfReaderException e) {
                if (e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                    break;
                } else {
                    throw e;
                }
            }
        } while (!fieldFound);

        LOG.info("END >> fetchBelowFieldValue >> fetchValue >> VALUE >> " + value);
        return StringUtils.isBlank(value) ? null : value.split(" ");
    }

    public static String[] fetchBesideFieldValue(RandomAccessFile fileReader,
                                                 PdfFieldStrategyMappingVO fieldStrategyMapping) throws PdfReaderException {
        LOG.info("START >> fetchBesideFieldValue >> fetchValue");
        String value = null;
        boolean fieldFound;
        int pageCount = 0;

        do {
            try {
                //Get all the data dives for that page
                List<PdfDivBean> pageDivList = PdfUtil.getAllRowsForThePage(fileReader, pageCount, true);
                List<PdfDivBean> fieldNameDivList = PdfUtil.getAllFieldNameDivs(pageDivList);
                double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

                PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
                fieldDivMetadata.setPageWidth(pageWidth);

                fieldFound = PdfParsingHelper.searchFieldAndGetMetadata(fieldStrategyMapping.getFieldName(), fieldNameDivList, fieldDivMetadata);

                if (fieldFound) {
                    value = ValueBesideFieldStrategyParser.fetchSingleLineValue(pageDivList, fieldDivMetadata,
                            !fieldStrategyMapping.getStrategy().getStrategy()
                                    .equals(PdfParsingStratergies.VALUE_BESIDE_FIELD_MULTI_COLUMN.name()));
                }
                pageCount++;
            } catch (PdfReaderException e) {
                if (e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                    break;
                } else {
                    throw e;
                }
            }
        } while (!fieldFound);

        LOG.info("END >> fetchBesideFieldValue >> fetchValue >> VALUE >> " + value);
        return StringUtils.isBlank(value) ? null : value.split(" ");
    }

}
