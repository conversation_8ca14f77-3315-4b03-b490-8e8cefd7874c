package com.perennialsys.pdfreader.pdfParser.strategyParsers;

import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.ExportSheetProductFields;
import com.perennialsys.pdfreader.enums.PDFSections;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.pdfParser.PdfParsingHelper;
import com.perennialsys.pdfreader.util.DateFormatUtil;
import com.perennialsys.pdfreader.util.NumberUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public final class ValueBelowFieldTableStrategyParser {
    private static final Logger LOG = Logger.getLogger(ValueBelowFieldTableStrategyParser.class);

    public static List<Map<String, Object>> fetchTableRowData(List<PdfDivBean> pageDivList, List<PdfFieldDivMetadata> fieldDivMetadataList,
                                                              double bufferValue, String fileType, String sectionKey,
                                                              String invSrNo, Set<String> resCodeSet) throws PdfReaderException {
        LOG.info("START >> ValueBelowFieldTableStrategyParser >> fetchTableRowData");
        List<Map<String, Object>> valueDetails = new ArrayList<>();

        if(null != fieldDivMetadataList && !fieldDivMetadataList.isEmpty()){
            fieldDivMetadataList = fieldDivMetadataList.stream()
                    .sorted(Comparator.comparingDouble(metadata -> metadata.getFieldStartDiv().getLeft()))
                    .collect(Collectors.toList());

            boolean reachedEndOfTable = false;

            //Set the max left attribute value based on, if the next field div is present or not.
            double maxLeftAttrValue = fieldDivMetadataList.get(fieldDivMetadataList.size() - 1).getFieldWrapperDiv() != null
                    ? fieldDivMetadataList.get(fieldDivMetadataList.size() - 1).getFieldWrapperDiv().getLeft()  +
                        fieldDivMetadataList.get(fieldDivMetadataList.size() - 1).getFieldWrapperDiv().getWidth()
                    : fieldDivMetadataList.get(fieldDivMetadataList.size() - 1).getNextFieldDiv() != null
                        ? fieldDivMetadataList.get(fieldDivMetadataList.size() - 1).getNextFieldDiv().getLeft()
                        : fieldDivMetadataList.get(0).getPageWidth();

            //Get all the divs in the next row with left less than the max lpageDivList = {ArrayList@14223}  size = 435eft attr value.
            List<PdfDivBean> targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(
                    fieldDivMetadataList.get(0).getFieldStartDiv().getLeft(),
                    fieldDivMetadataList.get(0).getFieldStartDiv().getTop(), pageDivList, maxLeftAttrValue);

            if (targetedPdfDivWithSameTop != null && !targetedPdfDivWithSameTop.isEmpty()) {
                //Get all the data dives for that page
                do {
                    Map<String, Object> valueMap = new HashMap<>();
                    double currentRowTop = targetedPdfDivWithSameTop.get(0).getTop();
                    for (PdfFieldDivMetadata fieldDivMetadata : fieldDivMetadataList) {

                        maxLeftAttrValue = null != fieldDivMetadata.getFieldWrapperDiv()
                                ? fieldDivMetadata.getFieldWrapperDiv().getLeft() + fieldDivMetadata.getFieldWrapperDiv().getWidth()
                                : fieldDivMetadata.getNextFieldDiv() != null
                                    ? fieldDivMetadata.getNextFieldDiv().getLeft()
                                    : fieldDivMetadata.getPageWidth();

                        //If section key is Additional supporting doc details and mapping key is supporting doc issue place
                        //then we have to consider the next fields left as max left attribute.
                        //else if Wrapper div is present then we have to check and set the max left attribute accordingly
                        if(!sectionKey.equals(PDFSections.ADDITIONAL_SUPPORTING_DOC_DETAILS.getSectionKey()) &&
                            !fieldDivMetadata.getFieldMappingKey()
                                .equals(BoeDetailsFields.SUPPORTING_DOCS_ISSUE_PLACE.getValue()) &&
                            null != fieldDivMetadata.getFieldWrapperDiv() &&
                            fieldDivMetadata.getFieldWrapperDiv().getWidth() > 0){

                            double wrapperDivWidth = NumberUtil.addDoubles(
                                    fieldDivMetadata.getFieldWrapperDiv().getLeft(),
                                    fieldDivMetadata.getFieldWrapperDiv().getWidth());
                            if(wrapperDivWidth < maxLeftAttrValue) {
                                maxLeftAttrValue = wrapperDivWidth;
                            }
                        }

                        if (targetedPdfDivWithSameTop == null || targetedPdfDivWithSameTop.isEmpty()) {
                            reachedEndOfTable = true;
                            break;
                        }

                        Optional<PdfDivBean> fieldDivOptional = targetedPdfDivWithSameTop.stream()
                                .filter(pdfDvi -> pdfDvi.getFontWeight() != null && pdfDvi.getFontWeight().equals("bold"))
                                .findFirst();

                        if (fieldDivOptional.isPresent()) {
                            reachedEndOfTable = true;
                            break;
                        }


                        if (PdfFileType.SHIPPING_BILL.name().equals(fileType)
                                && PDFSections.RODTEP_DETAILS.getSectionKey().equals(sectionKey)
                                && fieldDivMetadata.getFieldMappingKey().equals(ExportSheetProductFields.RODTEP_INV_SNO_ITEM_SNO.getValue())) {

                            valueMap.put(ExportSheetProductFields.RODTEP_INV_SNO.getValue(),
                                    targetedPdfDivWithSameTop.get(0).getValue());

                            valueMap.put(ExportSheetProductFields.RODTEP_ITM_SNO.getValue(),
                                    targetedPdfDivWithSameTop.get(1).getValue());
                        } else {
                            String value = PdfParsingHelper.fetchValue(maxLeftAttrValue, fieldDivMetadata, targetedPdfDivWithSameTop, true);
                            if(StringUtils.isNotBlank(value)){
                                if(sectionKey.equals(PDFSections.ADDITIONAL_SUPPORTING_DOC_DETAILS.getSectionKey())){
                                    if(fieldDivMetadata.getFieldMappingKey().equals(BoeDetailsFields.ITEM_SN.getValue())){
                                        String[] valueArr = value.split(" ");
                                        //Iteration count is equals to the invoice sr.no.
                                        valueMap.put(BoeDetailsFields.ITEM_SN.getValue(), Integer.parseInt(valueArr[0]) + 1);
                                        if(valueArr.length > 1) {
                                            valueMap.put(BoeDetailsFields.SUPPORTING_DOCS_TYPE.getValue(), valueArr[1]);
                                        }
                                    }else if(fieldDivMetadata.getFieldMappingKey().equals(BoeDetailsFields.INV_SN.getValue())){
                                        valueMap.put(BoeDetailsFields.INV_SN.getValue(), Integer.parseInt(value) + 1);
                                    }else if(fieldDivMetadata.getFieldMappingKey()
                                            .equals(BoeDetailsFields.SUPPORTING_DOCS_ICE_GATE_ID.getValue())){
                                        String[] valueArr = value.split(" ");

                                        if(valueArr.length > 1){
                                            value = valueArr[1];
                                        }

                                        valueMap.put(fieldDivMetadata.getFieldMappingKey(), value);
                                    }else if(fieldDivMetadata.getFieldMappingKey()
                                            .equals(BoeDetailsFields.SUPPORTING_DOCS_ISSUE_PLACE.getValue())){

                                        String[] valueArr = value.split(" ");

                                        if(valueArr.length > 1){
                                            StringBuilder strBuilder = new StringBuilder();
                                            for(int i = 0; i < valueArr.length-1 ; i++){
                                                if(i > 0){
                                                    strBuilder.append(" ");
                                                }
                                                strBuilder.append(valueArr[i]);
                                            }

                                            valueMap.put(fieldDivMetadata.getFieldMappingKey(), strBuilder.toString());
                                        }else if(!DateFormatUtil.isValidDate(value, DateFormatUtil.ddMMMYY_Hyphen)){
                                            valueMap.put(fieldDivMetadata.getFieldMappingKey(), value);
                                        }

                                    }else if(fieldDivMetadata.getFieldMappingKey()
                                            .equals(BoeDetailsFields.SUPPORTING_DOCS_ISSUE_DT.getValue())){
                                        String[] valueArr = value.split(" ");

                                        if(valueArr.length > 1){
                                            value = valueArr[valueArr.length-1];
                                        }

                                        valueMap.put(fieldDivMetadata.getFieldMappingKey(), value);
                                    }else{
                                        valueMap.put(fieldDivMetadata.getFieldMappingKey(), value);
                                    }
                                }if(sectionKey.equals(PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONTROL_DETAILS.getSectionKey())){
                                    if(fieldDivMetadata.getFieldMappingKey().equals(BoeDetailsFields.SINGLE_WINDOW_CONTROL_END_DT.getValue())){
                                        //In some cases we capture RES Code value along with the RES End date,
                                        //so we have to split the values between end date and RES Code
                                        String[] valueArr = value.split(" ");

                                        valueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_END_DT.getValue(), valueArr[0]);

                                        //If there are more than 1 element in the array then it means that
                                        // we have captured RES Code value with the end date field value
                                        if(valueArr.length > 1){
                                            valueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_RES_CD.getValue(), valueArr[1]);
                                            if(null != resCodeSet){
                                                resCodeSet.add(valueArr[1]);
                                            }
                                        }
                                    }else if(fieldDivMetadata.getFieldMappingKey().equals(BoeDetailsFields.SINGLE_WINDOW_CONTROL_RES_TEXT.getValue())){
                                        //In some cases we capture end date and RES Code value along with the RES Text,
                                        //so we have to check if the extra values are captured and if yes then
                                        //remove the extra values from the extracted data

                                        if(null != resCodeSet && !resCodeSet.isEmpty()){
                                            //Check if the RES Code is present in the extracted Value.
                                            Optional<String> resCodeOptional = resCodeSet.stream()
                                                .filter(value::contains)
                                                .findFirst();

                                            if(resCodeOptional.isPresent()){
                                                //Remove the data till the RES Code and add remaining data in to the value map
                                                String resCode = resCodeOptional.get();
                                                value = value.substring(value.indexOf(resCode)+resCode.length());
                                            }
                                        }

                                        valueMap.put(BoeDetailsFields.SINGLE_WINDOW_CONTROL_RES_TEXT.getValue(), value);
                                    }else if(fieldDivMetadata.getFieldMappingKey()
                                            .equals(BoeDetailsFields.SINGLE_WINDOW_CONTROL_RES_CD.getValue()) &&
                                            StringUtils.isNotBlank(value)) {
                                        if (null != resCodeSet && !resCodeSet.isEmpty()) {
                                            resCodeSet.add(value);
                                        }
                                    }else{
                                        valueMap.put(fieldDivMetadata.getFieldMappingKey(), value);
                                    }
                                }else {
                                    valueMap.put(fieldDivMetadata.getFieldMappingKey(),
                                            PdfParsingHelper.fetchValue(maxLeftAttrValue, fieldDivMetadata, targetedPdfDivWithSameTop, true));
                                }
                            }
                        }
                    }

                    if (!reachedEndOfTable) {
                        if(!valueMap.isEmpty()){
                            if(sectionKey.equals(PDFSections.INVOICE_AND_VALUATION_DETAILS_ITEM_DETAILS.getSectionKey())){
                                //Iteration count is equals to the invoice sr.no.
                                valueMap.put(BoeDetailsFields.INV_SN.getValue(), invSrNo);
                            }
                            valueDetails.add(valueMap);
                        }

                        targetedPdfDivWithSameTop = PdfParsingHelper.getNextRowDivs(
                                fieldDivMetadataList.get(0).getFieldStartDiv().getLeft(), currentRowTop, pageDivList,
                                maxLeftAttrValue);

                        reachedEndOfTable = null == targetedPdfDivWithSameTop || targetedPdfDivWithSameTop.isEmpty()
                                || targetedPdfDivWithSameTop.stream()
                                .anyMatch(pageDiv -> null != pageDiv.getFontWeight() && pageDiv.getFontWeight().equals("bold"));
                    }

                } while (!reachedEndOfTable);
            }
        }

        LOG.info("END >> ValueBelowFieldTableStrategyParser >> fetchTableRowData >> VALUES >> " + valueDetails);
        return valueDetails;
    }
}
