package com.perennialsys.pdfreader.pdfParser.strategyParsers;

import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.pdfParser.PdfParsingHelper;
import com.perennialsys.pdfreader.util.PdfUtil;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @since 20/12/2022
 * </p>
 * This class will contain the logic and code to fetch the custom house name from the PDF Html
 */
public final class CustomHouseNameStrategyParser {

    private static final Logger LOG = Logger.getLogger(CustomHouseNameStrategyParser.class);

    public static String fetchValueFromShippingBillPdf(RandomAccessFile fileReader) throws PdfReaderException {
        LOG.info("START >> CustomHouseNameStrategyParser >> fetchValueFromShippingBillPdf");
        PdfDivBean pdfDivBean;
        StringBuilder customHouseNameStringBuilder = new StringBuilder();
        try {
            //Go to the start of the page
            do {
                pdfDivBean = PdfUtil.parseDataPdfHtmlDiv(fileReader.readLine());
            } while (pdfDivBean == null || (!pdfDivBean.getDivClass().equals("page") && pdfDivBean.getId().equals("page_0")));

            int count = 0;
            //Get all the data dives for that page
            while (null == pdfDivBean || !pdfDivBean.getValue().equals("Port")) {
                if (null != pdfDivBean && pdfDivBean.getDivClass().equals("p")) {
                    if (count != 0) {
                        customHouseNameStringBuilder.append(" ");
                    }
                    customHouseNameStringBuilder.append(pdfDivBean.getValue());
                    count++;
                }
                pdfDivBean = PdfUtil.parseDataPdfHtmlDiv(fileReader.readLine());
            }
        } catch (IOException e) {
            LOG.error("ERROR >> CustomHouseNameStrategyParser >> fetchValueFromShippingBillPdf >> ", e);
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }

        LOG.info("END >> CustomHouseNameStrategyParser >> fetchValueFromShippingBillPdf >> VALUE >> " + customHouseNameStringBuilder);
        return customHouseNameStringBuilder.toString();
    }

    /**
     * @param fileReader           - Random File reader
     * @param fieldStrategyMapping - Field strategy Mapping details
     * @return - returns String value
     * @throws PdfReaderException - Throws PdfReaderException
     * <AUTHOR> Nagare
     * @since 04/01/2023
     * </p>
     * Steps-
     * 1. Get all the divs from the 1st page.
     * 2. Get the metadata for Actual field name, Port Code field and PART - I - BILL OF ENTRY SUMMARY section heading (this will be used to restrict the targeted divs)
     * 3. iterate over each row divs until we reach the section heading top value.
     */
    public static String fetchValueFromBOEPdf(RandomAccessFile fileReader, PdfFieldStrategyMappingVO fieldStrategyMapping) throws PdfReaderException {
        LOG.info("START >> CustomHouseNameStrategyParser >> fetchValueFromBOEPdf");
        StringBuilder customHouseNameStringBuilder = new StringBuilder();

        List<PdfDivBean> pageDivList = PdfUtil.getAllRowsForThePage(fileReader, 0, true);
        List<PdfDivBean> fieldDicsList = PdfUtil.getAllFieldNameDivs(pageDivList);

        PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
        PdfFieldDivMetadata portCodeFieldMetadata = new PdfFieldDivMetadata();
        PdfFieldDivMetadata boeSummarySectionMetadata = new PdfFieldDivMetadata();

        boolean isFieldFound = PdfParsingHelper.searchFieldAndGetMetadata(fieldStrategyMapping.getFieldName(), fieldDicsList, fieldDivMetadata);

        boolean isPortCodeFound = PdfParsingHelper.searchFieldAndGetMetadata("Port Code", fieldDicsList, portCodeFieldMetadata);

        boolean isBoeSummarySectionFound = PdfParsingHelper.searchFieldAndGetMetadata("PART - I - BILL OF ENTRY SUMMARY", fieldDicsList, boeSummarySectionMetadata);

        if (isFieldFound && isBoeSummarySectionFound && isPortCodeFound) {
            double maxLeftAttrValue = portCodeFieldMetadata.getFieldStartDiv().getLeft();
            double maxTopValue = boeSummarySectionMetadata.getFieldStartDiv().getTop();

            double topOfNextRow = PdfUtil.calculateTopOfNextRow(fieldDivMetadata.getFieldStartDiv(), fieldStrategyMapping.getBufferValue());

            int count = 0;
            //Get all the data dives for that page
            do {
                //Get all the divs in the next row with left less than the max left attr value.
                List<PdfDivBean> targetedPdfDivWithSameTop = PdfUtil.getSameTopDivList(pageDivList, maxLeftAttrValue, topOfNextRow);

                if (targetedPdfDivWithSameTop == null || targetedPdfDivWithSameTop.isEmpty()) {
                    break;
                }

                for (PdfDivBean divBean : targetedPdfDivWithSameTop) {
                    if (count != 0) {
                        customHouseNameStringBuilder.append(" ");
                    }
                    customHouseNameStringBuilder.append(divBean.getValue());
                    count++;
                }

                topOfNextRow = PdfUtil.calculateTopOfNextRow(targetedPdfDivWithSameTop.get(0), 1.6);
            } while (maxTopValue >= topOfNextRow);
        }

        String resultString = customHouseNameStringBuilder.toString();
        if(customHouseNameStringBuilder.toString().contains("BILL OF ENTRY FOR")){
            resultString = customHouseNameStringBuilder.substring(0, customHouseNameStringBuilder.indexOf("BILL OF ENTRY FOR"));
        }

        LOG.info("END >> CustomHouseNameStrategyParser >> fetchValueFromBOEPdf >> VALUE >> " + customHouseNameStringBuilder);
        return resultString;
    }

}
