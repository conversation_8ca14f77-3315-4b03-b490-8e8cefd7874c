package com.perennialsys.pdfreader.pdfParser.strategyParsers;

import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.util.PdfUtil;
import org.apache.log4j.Logger;

import java.util.List;
import java.util.stream.Collectors;

public final class ValueBesideFieldStrategyParser {
    private static final Logger LOG = Logger.getLogger(ValueBesideFieldStrategyParser.class);

    /**
     * @param pageDivList      - List of all the page divs.
     * @param fieldDivMetadata - Field name div metadata.
     * @param checkSpace
     * <AUTHOR>
     * @since 20/12/2022
     * </p>
     * Steps -
     * 1. Get the value divs with same top value and left greater than the last field part div.
     * 2. Sort the divs in ascending order of left value.
     * 3. Traverse through the div list till we reach the end of page or till we get the next field name div.
     * 4. Return the value.
     */
    public static String fetchSingleLineValue(List<PdfDivBean> pageDivList, PdfFieldDivMetadata fieldDivMetadata,
                                              boolean checkSpace) throws PdfReaderException {
        LOG.info("START >> ValueBesideFieldStrategyParser >> fetchValue");
        //Get the value divs with same top value and left greater than the last field part div.
        //Sort the divs in ascending order of left value.
        List<PdfDivBean> targetedPdfDivWithSameTop = pageDivList.stream()
                .filter(pageDiv -> pageDiv.getLeft() > fieldDivMetadata.getFieldEndDiv().getLeft()
                        && pageDiv.getTop() == fieldDivMetadata.getTop())
                .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                .collect(Collectors.toList());
        StringBuilder valueStringBuilder = new StringBuilder();
        int count = 0;

        if (targetedPdfDivWithSameTop.isEmpty()) {
            LOG.error("ERROR >> ValueBesideFieldStrategyParser >> fetchSingleLineValue >> Targeted PDF div with same top not found");
            //throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }

        PdfDivBean previousFieldDiv = null;
        for (PdfDivBean pdfDivBean : targetedPdfDivWithSameTop) {
            if (pdfDivBean.getDivClass().equals("p")) {
                if (null != pdfDivBean.getFontWeight() && pdfDivBean.getFontWeight().equals("bold") ||
                        (null != previousFieldDiv && (checkSpace && !PdfUtil.checkIfDivHaveSpaceBetween(previousFieldDiv, pdfDivBean)))) {
                    break;
                }
                if (count == 0) {
                    valueStringBuilder.append(pdfDivBean.getValue());
                } else {
                    valueStringBuilder.append(" ").append(pdfDivBean.getValue());
                }
                previousFieldDiv = pdfDivBean;
            }
            count++;
        }

        LOG.info("END >> ValueBesideFieldStrategyParser >> fetchValue >> VALUE >> " + valueStringBuilder);
        return valueStringBuilder.toString();
    }

}
