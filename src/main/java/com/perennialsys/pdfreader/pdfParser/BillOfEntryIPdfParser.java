package com.perennialsys.pdfreader.pdfParser;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.UpdateManyModel;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.WriteModel;
import com.perennialsys.pdfreader.bean.PdfDivBean;
import com.perennialsys.pdfreader.bean.PdfFieldDivMetadata;
import com.perennialsys.pdfreader.constants.NoSqlDBTables;
import com.perennialsys.pdfreader.constants.QueryOperators;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.db.mongo.MongoDao;
import com.perennialsys.pdfreader.enums.BoeAdditionalDetailsFields;
import com.perennialsys.pdfreader.enums.BoeDetailsFields;
import com.perennialsys.pdfreader.enums.PDFSections;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.SBInvoiceDetailsFields;
import com.perennialsys.pdfreader.excel.StreamHelper;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.helper.FieldIdentificationHelper;
import com.perennialsys.pdfreader.helper.FileHelper;
import com.perennialsys.pdfreader.pdfParser.strategyParsers.ValueBelowFieldStrategyParser;
import com.perennialsys.pdfreader.pdfParser.strategyParsers.ValueBelowFieldTableStrategyParser;
import com.perennialsys.pdfreader.service.IService;
import com.perennialsys.pdfreader.util.NumberUtil;
import com.perennialsys.pdfreader.util.PdfUtil;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.validator.Normalizer;
import com.perennialsys.pdfreader.validator.ValidationProcessor;
import com.perennialsys.pdfreader.vo.DataNormalizerVO;
import com.perennialsys.pdfreader.vo.FieldIdentificationStrategyVO;
import com.perennialsys.pdfreader.vo.FileUploadDetailsVO;
import com.perennialsys.pdfreader.vo.PdfFieldStrategyMappingVO;
import com.perennialsys.pdfreader.vo.PdfSectionDetailsVO;
import com.perennialsys.pdfreader.vo.mongoDocuments.BoeDetailsDocument;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class BillOfEntryIPdfParser {
    private static final Logger LOG = Logger.getLogger(BillOfEntryIPdfParser.class);
    private final IService service;
    private final MongoDao mongoDao;
    private final List<DataNormalizerVO> dataNormalizerVOList;

    public BillOfEntryIPdfParser(IService service, MongoDao mongoDao) {
        this.service = service;
        this.mongoDao = mongoDao;
        this.dataNormalizerVOList = service.getDataNormalizerList();
    }

    public long parsePdf(FileUploadDetailsVO fileUploadDetailsVo, boolean isSftp) throws PdfReaderException, IOException {

        File pdfFile = StreamHelper.getFileFromLocation(fileUploadDetailsVo.getPan(), fileUploadDetailsVo.getFileType(),
                fileUploadDetailsVo.getFileName(), fileUploadDetailsVo.getFileLoc(), isSftp);

        String htmlFilePath = PdfUtil.parseAndStorePDFAsHtml(pdfFile, fileUploadDetailsVo.getPan(), fileUploadDetailsVo.getTxnId());

        RandomAccessFile fileReader = PdfUtil.getPdfHtmlFile(htmlFilePath);

        try {
            if (!PdfParsingHelper.ifSectionPresent(service.getPdfSectionDetails(PdfFileType.BILL_OF_ENTRY.name(), PDFSections.BILL_OF_ENTRY_SUMMARY.getSectionKey()),
                    fileReader)) {
                LOG.error("ERROR >> BillOfEntryPdfParser >> parsePdf >> TXN_ID >> " + fileUploadDetailsVo.getTxnId()
                        + " >> PAN >> " + fileUploadDetailsVo.getPan() + " >> FILE_NAME >> " + fileUploadDetailsVo.getFileName() + " >> BOE number not found.");
                throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.INVALID_PDF_FILE);
            }

            Map<String, Object> billOfEntrySummaryDetailsMap = fetchBillOfEntrySummarySectionData(fileReader);
            String boeNo = "";
            if (billOfEntrySummaryDetailsMap != null && !billOfEntrySummaryDetailsMap.isEmpty() && billOfEntrySummaryDetailsMap.containsKey(BoeDetailsFields.BOE_NO.getValue())) {
                MongoCollection<BoeDetailsDocument> boeDetailsCollection = mongoDao.getMongoCollection(NoSqlDBTables.BOE_DETAILS_STATEMENT, BoeDetailsDocument.class);
                //Check if same BOE is present in the system or not
                if (!ValidationProcessor.checkUniqueBoeNo(billOfEntrySummaryDetailsMap.get(BoeDetailsFields.BOE_NO.getValue()),
                        fileUploadDetailsVo.getTxnId(), fileUploadDetailsVo.getPan(), boeDetailsCollection)) {
                    LOG.error("ERROR >> BillOfEntryPdfParser >> parsePdf >> TXN_ID >> " + fileUploadDetailsVo.getTxnId()
                            + " >> PAN >> " + fileUploadDetailsVo.getPan() + " >> FILE_NAME >> " + fileUploadDetailsVo.getFileName()
                            + ">> BOE_NO >> " + billOfEntrySummaryDetailsMap.get(BoeDetailsFields.BOE_NO.getValue())
                            + " >> BOE No Already present in the system.");
                    throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.BOE_NO_MUST_BE_UNIQUE);
                }

                boeNo = billOfEntrySummaryDetailsMap.get(BoeDetailsFields.BOE_NO.getValue()).toString();

                Map<String, Long> pageDtlsMap = new HashMap<>();
                List<Map<String, Object>> bondDetailList = fetchRepeatingSectionDetails(fileReader, PDFSections.BILL_OF_ENTRY_SUMMARY_BOND_DETAILS.getSectionKey(), true, true, pageDtlsMap, false);
                List<Map<String, Object>> warehouseDetailList = fetchRepeatingSectionDetails(fileReader, PDFSections.BILL_OF_ENTRY_SUMMARY_WAREHOUSE_DETAILS.getSectionKey(), true, true, pageDtlsMap, false);
                List<Map<String, Object>> paymentDetailList = fetchRepeatingSectionDetails(fileReader, PDFSections.BILL_OF_ENTRY_SUMMARY_PAYMENT_DETAILS.getSectionKey(), true, true, pageDtlsMap, false);
                List<Map<String, Object>> invoiceAndValuationDetailList = fetchRepeatingSectionDetails(fileReader, PDFSections.INVOICE_AND_VALUATION_DETAILS.getSectionKey(), true, false, pageDtlsMap, false);
                List<Map<String, Object>> itemDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.INVOICE_AND_VALUATION_DETAILS_ITEM_DETAILS.getSectionKey(), true, false, pageDtlsMap, false);
                List<Map<String, Object>> dutiesDetailList = fetchRepeatingSectionDetails(fileReader, PDFSections.DUTIES.getSectionKey(), false, true, pageDtlsMap, false);

                List<Map<String, Object>> additionalSvbDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_SVB_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalPrevBoeDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_PREV_BOE_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalReImportDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_RE_IMPORT_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalItemManufacturerDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_ITEM_MANUFACTURER_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalAccessoryStatusDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_ACCESSORY_STATUS_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalLicenceDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_LICENCE_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalCertificateDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_CERTIFICATE_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalHssDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_HSS_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalSingleWindowDeclarationDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalSingleWindowDeclarationConstituentsDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONSTITUENTS_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalSingleWindowDeclarationControlDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONTROL_DETAILS.getSectionKey(), false, true, pageDtlsMap, false);
                List<Map<String, Object>> additionalSupportingDocDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_SUPPORTING_DOC_DETAILS.getSectionKey(), false, true, pageDtlsMap, true);
                List<Map<String, Object>> additionalContainerDocDetails = fetchRepeatingSectionDetails(fileReader, PDFSections.ADDITIONAL_CONTAINER_DETAILS.getSectionKey(), false, true, pageDtlsMap, true);

                Map<String, Object> otherCompliancesMap = fetchOtherComplianceSectionData(fileReader, pageDtlsMap);

                putDataIntoMongo(billOfEntrySummaryDetailsMap, otherCompliancesMap, invoiceAndValuationDetailList, dutiesDetailList,
                        itemDetails, additionalSvbDetails, additionalPrevBoeDetails, additionalReImportDetails,
                        additionalItemManufacturerDetails, additionalAccessoryStatusDetails, additionalLicenceDetails,
                        additionalCertificateDetails, additionalHssDetails, additionalSingleWindowDeclarationDetails,
                        additionalSingleWindowDeclarationConstituentsDetails, additionalSingleWindowDeclarationControlDetails,
                        additionalSupportingDocDetails, additionalContainerDocDetails, bondDetailList, warehouseDetailList,
                        paymentDetailList, fileUploadDetailsVo.getTxnId(), fileUploadDetailsVo.getSubTxnId(),
                        fileUploadDetailsVo.getPan(), boeNo);

            } else {
                LOG.error("ERROR >> BillOfEntryPdfParser >> parsePdf >> TXN_ID >> " + fileUploadDetailsVo.getTxnId()
                        + " >> PAN >> " + fileUploadDetailsVo.getPan() + " >> FILE_NAME >> " + fileUploadDetailsVo.getFileName() + " >> BOE number not found.");
                throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.INVALID_PDF_FILE);
            }
        } finally {
            FileHelper.removeLocalCopy(Collections.singletonList(htmlFilePath));
        }

        return PdfUtil.getTotalPageCount(fileReader);
    }

    /**
     * <AUTHOR> Majithia
     * @since 02/01/2023
     * </p>
     * Steps -
     * 1. Get to the invoice details page.
     * 2. Iterate over all the pages with Bill Of Summary heading.
     * 3. Get the details for each invoice and store it into a map. (This will be used to map the items to the invoice in the further processing)
     */
    public Map<String, Object> fetchBillOfEntrySummarySectionData(RandomAccessFile fileReader) throws PdfReaderException {
        LOG.info("START >> PdfParsingProcessorImpl >> processBillOfSummaryFields >> FILE_TYPE >> " + PdfFileType.BILL_OF_ENTRY.name());
        Map<String, Object> dataMap = new HashMap<>();

        //Get all the Bill Of Summary level fields for the file.
        List<PdfFieldStrategyMappingVO> pdfFieldsList = service.getPdfFieldStrategyMapping(PDFSections.BILL_OF_ENTRY_SUMMARY.name(),
                PdfFileType.BILL_OF_ENTRY.name());
        if (null != pdfFieldsList && !pdfFieldsList.isEmpty()) {
            int pageCount = 0;

            //Get all the data div from the page.
            List<PdfDivBean> pageDivList = new ArrayList<>();
            List<PdfDivBean> otherDivList = new ArrayList<>();
            PdfUtil.getAllRowsForThePage(fileReader, pageCount, true, pageDivList , otherDivList, false, 0);
            double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

            //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
            List<PdfDivBean> fieldNameDivList = PdfUtil.getAllFieldNameDivs(pageDivList);

            //Traverse over the fields list and get the data for the fields.
            for (PdfFieldStrategyMappingVO pdfFieldStrategyMapping : pdfFieldsList) {
                if (!pdfFieldStrategyMapping.getStrategy().isFieldSpecific()) {
                    PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
                    fieldDivMetadata.setPageWidth(pageWidth);

                    try {
                        if (PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldStrategyMapping.getFieldName(), fieldNameDivList, fieldDivMetadata)) {
                            PdfParsingHelper.checkAndSetFieldWrapperDiv(fieldDivMetadata, otherDivList);
                            dataMap.put(pdfFieldStrategyMapping.getMappingKey(), PdfParsingHelper.fetchFieldValueBasedOnStrategy(pdfFieldStrategyMapping, pageDivList, fieldDivMetadata));
                        } else if (!pdfFieldStrategyMapping.isOptionalField()) {
                            LOG.error("ERROR >> fetchBillOfEntrySummarySectionData >> FIELD_NAME >> " + pdfFieldStrategyMapping.getFieldName() + " >> NOT_FOUND");
                            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                        }
                    } catch (PdfReaderException e) {
                        if (!pdfFieldStrategyMapping.isOptionalField()) {
                            throw e;
                        }
                    }
                } else {
                    PdfParsingHelper.fetchValueFromFieldSpecificStrategy(pdfFieldStrategyMapping, fileReader, dataMap, pageDivList, pageCount);
                }

            }
        } else {
            LOG.error("ERROR >> PdfParsingProcessorImpl >> processBillOfSummaryFields >> FILE_TYPE >> "
                    + PdfFileType.BILL_OF_ENTRY.name() + " >> Field Strategy Mapping not found");
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }
        LOG.info("END >> PdfParsingProcessorImpl >> processBillOfSummaryFields >> FILE_TYPE >> " + PdfFileType.BILL_OF_ENTRY.name());
        return dataMap;
    }

    /**
     * <AUTHOR> Majithia
     * @since 03/01/2023
     * Fetching PART - V - OTHER COMPLIANCE
     */
    public Map<String, Object> fetchOtherComplianceSectionData(RandomAccessFile fileReader, Map<String, Long> pageDtlsMap) throws PdfReaderException {
        LOG.info("START >> PdfParsingProcessorImpl >> processOtherComplianceFields >> FILE_TYPE >> " + PdfFileType.BILL_OF_ENTRY.name());
        Map<String, Object> dataMap = new HashMap<>();

        PdfSectionDetailsVO sectionDetailsVO = service.getPdfSectionDetails(PdfFileType.BILL_OF_ENTRY.name(), PDFSections.OTHER_COMPLIANCE.getSectionKey());
        List<PdfDivBean> targetedFieldNameDivList;

        int pageCount = pageDtlsMap.containsKey("current-page-count") ? pageDtlsMap.get("current-page-count").intValue() : 0;
        long pageStartPointer = pageDtlsMap.containsKey("current-page-start-pointer") ? pageDtlsMap.get("current-page-start-pointer").intValue() : 0L;

        //Get all the data div from the page.
        List<PdfDivBean> pageDivList = new ArrayList<>();
        List<PdfDivBean> otherDivList = new ArrayList<>();
        pageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, pageCount, false, pageDivList, otherDivList,
                true, pageStartPointer);
        double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

        //Get the first page where we get the Section heading has found.
        long tempPageStartPointer = pageStartPointer;
        int tempPageCount = pageCount;
        boolean sectionFound = false;
        PdfFieldDivMetadata sectionDivMetadata = new PdfFieldDivMetadata();
        while (!sectionFound) {
            try {
                //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
                targetedFieldNameDivList = pageDivList.stream()
                        .filter(pageDiv -> null != pageDiv.getFontWeight() && pageDiv.getFontWeight().equals("bold"))
                        .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator())
                        .collect(Collectors.toList());

                boolean sectionNameFound = PdfParsingHelper.searchFieldAndGetMetadata(sectionDetailsVO.getSectionDisplayName(), targetedFieldNameDivList, sectionDivMetadata);
                if (sectionNameFound) {
                    //Get all the Bill Of Summary level fields for the file.
                    List<PdfFieldStrategyMappingVO> pdfFieldsList = service.getPdfFieldStrategyMapping(PDFSections.OTHER_COMPLIANCE.getSectionKey(),
                            PdfFileType.BILL_OF_ENTRY.name());

                    if (null != pdfFieldsList && !pdfFieldsList.isEmpty()) {
                        //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
                        List<PdfDivBean> fieldNameDivList = PdfUtil.getAllFieldNameDivs(pageDivList);

                        //Traverse over the fields list and get the data for the fields.
                        for (PdfFieldStrategyMappingVO pdfFieldStrategyMapping : pdfFieldsList) {
                            if (!pdfFieldStrategyMapping.getStrategy().isFieldSpecific()) {
                                PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
                                fieldDivMetadata.setPageWidth(pageWidth);

                                boolean fieldFound = PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldStrategyMapping.getFieldName(), fieldNameDivList, fieldDivMetadata);

                                if (fieldFound) {
                                    dataMap.put(pdfFieldStrategyMapping.getMappingKey(), PdfParsingHelper.fetchFieldValueBasedOnStrategy(pdfFieldStrategyMapping, pageDivList, fieldDivMetadata));
                                }

                            } else {
                                PdfParsingHelper.fetchValueFromFieldSpecificStrategy(pdfFieldStrategyMapping, fileReader, dataMap, pageDivList, pageCount);
                            }
                        }
                    } else {
                        LOG.error("ERROR >> PdfParsingProcessorImpl >> processOtherComplianceFields >> FILE_TYPE >> "
                                + PdfFileType.BILL_OF_ENTRY.name() + " >> Field Strategy Mapping not found");
                        throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                    }
                    sectionFound = true;
                }

                tempPageCount++;

                //Get all the data div from the page.
                pageDivList.clear();
                otherDivList.clear();
                tempPageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, tempPageCount, false, pageDivList, otherDivList, false, tempPageStartPointer);
                pageWidth = PdfUtil.fetchPageWidth(pageDivList);
            } catch (PdfReaderException e) {
                if (e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                    break;
                } else {
                    throw e;
                }
            }
        }

        LOG.info("END >> PdfParsingProcessorImpl >> processOtherComplianceFields >> FILE_TYPE >> " + PdfFileType.BILL_OF_ENTRY.name());
        return dataMap;
    }

    /**
     * <AUTHOR> Majithia
     * @since 03/01/2023
     * </p>
     * Steps -<br>
     * 1. Get to the invoice details page.<br>
     * 2. Iterate over all the pages with Invoice details heading.<br>
     * 3. Get the details for each invoice and store it into a map. (This will be used to map the items to the invoice in the further processing)<br>
     * 4. Get to the item details section page.<br>
     * 5. Iterate over all the pages with item details heading starting from the page after invoice details page.
     */
    public List<Map<String, Object>> fetchRepeatingSectionDetails(RandomAccessFile fileReader, String sectionKey,
                                                                  boolean startFromStart, boolean startFromPageStart,
                                                                  Map<String, Long> pageDtlsMap, boolean checkTillEnd) throws PdfReaderException {
        List<Map<String, Object>> dataList = new ArrayList<>();
        try{
            int pageCount = !startFromStart && pageDtlsMap.containsKey("current-page-count") ? pageDtlsMap.get("current-page-count").intValue() : 0;
            long pageStartPointer = pageDtlsMap.containsKey("current-page-start-pointer") ? pageDtlsMap.get("current-page-start-pointer").intValue() : 0L;

            PdfSectionDetailsVO sectionDetailsVO = service.getPdfSectionDetails(PdfFileType.BILL_OF_ENTRY.name(), sectionKey);
            List<PdfDivBean> targetedFieldNameDivList;

            List<PdfDivBean> pageDivList = new ArrayList<>();
            List<PdfDivBean> otherDivList = new ArrayList<>();

            pageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, pageCount, startFromStart, pageDivList, otherDivList,
                    startFromPageStart, pageStartPointer);
            double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

            PdfFieldDivMetadata sectionDivMetadata = new PdfFieldDivMetadata();
            //Get the first page where we get the Section heading has found.
            long tempPageStartPointer = pageStartPointer;
            int tempPageCount = pageCount;
            int sectionFoundCount = 0;

            Set<String> resCodeSet =
                    PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONTROL_DETAILS.getSectionKey().equals(sectionKey)
                        ? new HashSet<>() : null;

            while (true) {
                try {
                    //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
                    targetedFieldNameDivList = pageDivList.stream()
                            .filter(pageDiv -> null != pageDiv.getFontWeight() && pageDiv.getFontWeight().equals("bold"))
                            .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator())
                            .collect(Collectors.toList());

                    boolean sectionNameFound = PdfParsingHelper.searchFieldAndGetMetadata(sectionDetailsVO.getSectionDisplayName(),
                            targetedFieldNameDivList, sectionDivMetadata);
                    if (sectionNameFound) {
                        sectionFoundCount++;
                        pageStartPointer = tempPageStartPointer;
                        pageCount = tempPageCount;
                        //Start reading the invoice details form the page
                        if (!targetedFieldNameDivList.isEmpty()) {

                            if (PDFSections.INVOICE_AND_VALUATION_DETAILS_ITEM_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_SVB_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_PREV_BOE_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_RE_IMPORT_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_ITEM_MANUFACTURER_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_ACCESSORY_STATUS_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_LICENCE_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_CERTIFICATE_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_HSS_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONSTITUENTS_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONTROL_DETAILS.getSectionKey().equals(sectionKey) ||
                                    PDFSections.ADDITIONAL_SUPPORTING_DOC_DETAILS.getSectionKey().equals(sectionKey)||
                                    PDFSections.ADDITIONAL_CONTAINER_DETAILS.getSectionKey().equals(sectionKey)||
                                    PDFSections.BILL_OF_ENTRY_SUMMARY_BOND_DETAILS.getSectionKey().equals(sectionKey)||
                                    PDFSections.BILL_OF_ENTRY_SUMMARY_WAREHOUSE_DETAILS.getSectionKey().equals(sectionKey)||
                                    PDFSections.BILL_OF_ENTRY_SUMMARY_PAYMENT_DETAILS.getSectionKey().equals(sectionKey)) {

                                String invSrNo = null;

                                if(PDFSections.INVOICE_AND_VALUATION_DETAILS_ITEM_DETAILS.getSectionKey().equals(sectionKey)){
                                    invSrNo = getInvSrNoForItemDetails(pageDivList, targetedFieldNameDivList, pageWidth);
                                }
                                //Get all the Invoice Details level fields for the file.
                                List<PdfFieldStrategyMappingVO> pdfFieldMappingList = service
                                        .getPdfFieldStrategyMapping(sectionDetailsVO.getSectionKey(),
                                        PdfFileType.BILL_OF_ENTRY.name());
                                if(null != pdfFieldMappingList && !pdfFieldMappingList.isEmpty()){
                                    targetedFieldNameDivList = targetedFieldNameDivList.stream()
                                            .filter(pageDiv -> pageDiv.getTop() > sectionDivMetadata.getFieldStartDiv().getTop())
                                            .collect(Collectors.toList());

                                    List<PdfFieldDivMetadata> fieldDivMetadataList = new ArrayList<>();
                                    for (PdfFieldStrategyMappingVO pdfFieldMapping : pdfFieldMappingList) {
                                        //Find the field and create the metadata for each field.
                                        PdfFieldDivMetadata fieldMetadata = new PdfFieldDivMetadata();
                                        fieldMetadata.setPageWidth(pageWidth);
                                        fieldMetadata.setFieldMappingKey(pdfFieldMapping.getMappingKey());

                                        try {
                                            if (PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldMapping.getFieldName(),
                                                    targetedFieldNameDivList, fieldMetadata)) {

                                                PdfParsingHelper.checkAndSetFieldWrapperDiv(fieldMetadata, otherDivList);
                                                fieldDivMetadataList.add(fieldMetadata);

                                            } else if (!pdfFieldMapping.isOptionalField()) {
                                                LOG.error("ERROR >> fetchRepeatingSectionDetails >> FIELD_NAME >> "
                                                        + pdfFieldMapping.getFieldName() + " >> NOT_FOUND");
                                                throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF,
                                                        ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                                            }
                                        } catch (PdfReaderException e) {
                                            if (!pdfFieldMapping.isOptionalField()) {
                                                throw e;
                                            }
                                        }
                                    }

                                    dataList.addAll(ValueBelowFieldTableStrategyParser.fetchTableRowData(pageDivList,
                                            fieldDivMetadataList, pdfFieldMappingList.get(0).getBufferValue(),
                                            PdfFileType.SHIPPING_BILL.name(), sectionKey, invSrNo, resCodeSet));
                                }

                            } else {

                                PdfDivBean firstFieldDiv;

                                //Get the fields which are below the section heading div row.
                                targetedFieldNameDivList = targetedFieldNameDivList.stream()
                                        .filter(pdfDiv -> pdfDiv.getTop() > sectionDivMetadata.getTop())
                                        .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                                        .collect(Collectors.toList());

                                //Get the first row of the section below section heading.
                                double topOfNextRow = PdfUtil.calculateTopOfNextRow(sectionDivMetadata.getFieldStartDiv(), sectionDetailsVO.getBufferValue());
                                Optional<PdfDivBean> sectionStartRowDiv = targetedFieldNameDivList.stream().sorted(new PdfDivBean.PdfDivBeanTopAttrComparator()).filter(pdfDiv -> pdfDiv.getTop() == topOfNextRow).findFirst();
                                if (sectionStartRowDiv.isPresent()) {
                                    firstFieldDiv = sectionStartRowDiv.get();
                                } else {
                                    LOG.error("ERROR >> PdfParsingProcessorImpl >> processItemLevelFields >> FILE_TYPE >> " + PdfFileType.BILL_OF_ENTRY.name()
                                            + " >> SECTION_KEY >> invoice_details >> Error while getting the start of section");
                                    throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                                }

                                //Get all the Invoice Details level fields for the file.
                                List<PdfFieldStrategyMappingVO> pdfFieldsList = service.getPdfFieldStrategyMapping(sectionDetailsVO.getSectionKey(),
                                        PdfFileType.BILL_OF_ENTRY.name());

                                boolean repeatedSectionFound;
                                do {
                                    repeatedSectionFound = false;
                                    PdfDivBean finalFirstFieldDiv = firstFieldDiv;
                                    targetedFieldNameDivList = targetedFieldNameDivList.stream().filter(pageDiv -> pageDiv.getTop() >= finalFirstFieldDiv.getTop()).collect(Collectors.toList());
                                    Map<String, Object> dataMap = new HashMap<>();

                                    if (PDFSections.INVOICE_AND_VALUATION_DETAILS.getSectionKey().equals(sectionKey) && !dataList.isEmpty()) {
                                        Optional<PdfFieldStrategyMappingVO> srNoFieldMappingDtlsOptional =
                                                pdfFieldsList.stream()
                                                        .filter(fieldDtls ->
                                                                fieldDtls.getMappingKey().equals(BoeDetailsFields.INV_SN.getValue()))
                                                        .findAny();

                                        if (srNoFieldMappingDtlsOptional.isPresent()) {
                                            getValue(fileReader, pageWidth, pageDivList, srNoFieldMappingDtlsOptional.get(),
                                                    targetedFieldNameDivList, dataMap, null);
                                        }

                                        if (dataMap.containsKey(BoeDetailsFields.INV_SN.getValue())) {
                                            Object invSrNo = dataMap.get(BoeDetailsFields.INV_SN.getValue());
                                            if (dataList.stream().anyMatch(data ->
                                                    data.containsKey(BoeDetailsFields.INV_SN.getValue()) &&
                                                            data.get(BoeDetailsFields.INV_SN.getValue()).equals(invSrNo))) {
                                                break;
                                            }
                                        }
                                    }

                                    //Start iterating for the fields
                                    for (PdfFieldStrategyMappingVO pdfFieldStrategyMapping : pdfFieldsList) {
                                        getValue(fileReader, pageWidth, pageDivList, pdfFieldStrategyMapping,
                                                targetedFieldNameDivList, dataMap, otherDivList);
                                    }

                                    dataList.add(dataMap);

                                    //Get to the next repeated section.
                                    for (PdfDivBean pageDiv : targetedFieldNameDivList) {
                                        if (pageDiv.getTop() > firstFieldDiv.getTop() && pageDiv.getValue().equals(firstFieldDiv.getValue())) {
                                            firstFieldDiv = pageDiv;
                                            repeatedSectionFound = true;
                                            break;
                                        }
                                    }
                                } while (repeatedSectionFound);
                            }
                        }
                    }else if(!checkTillEnd && sectionFoundCount > 0){
                        break;
                    }

                    tempPageCount++;

                    //Get all the data div from the page.
                    pageDivList.clear();
                    otherDivList.clear();
                    tempPageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, tempPageCount, false, pageDivList, otherDivList, false, tempPageStartPointer);
                    pageWidth = PdfUtil.fetchPageWidth(pageDivList);
                } catch (PdfReaderException e) {
                    if (e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                        break;
                    } else {
                        throw e;
                    }
                }
            }
            if (sectionKey.equals(PDFSections.DUTIES.getSectionKey())) {
                findAmountValue(dataList);
            }

            pageDtlsMap.put("current-page-count", (long) pageCount);
            pageDtlsMap.put("current-page-start-pointer", pageStartPointer);
        } catch (PdfReaderException e) {
            if (!e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                throw e;
            }
        }

        return dataList;
    }

    public int fetchRepeatingSectionDetails(RandomAccessFile fileReader, String sectionKey, boolean startFromStart,
                                            boolean startFromPageStart, int pageCount, List<Map<String, Object>> dataList) throws PdfReaderException {

        PdfSectionDetailsVO sectionDetailsVO = service.getPdfSectionDetails(PdfFileType.BILL_OF_ENTRY.name(), sectionKey);
        List<PdfDivBean> targetedFieldNameDivList;

        List<PdfDivBean> pageDivList = new ArrayList<>();
        List<PdfDivBean> otherDivList = new ArrayList<>();

        long pageStartPointer = PdfUtil.getAllRowsForThePage(fileReader, pageCount, startFromStart, pageDivList, otherDivList, startFromPageStart, 0);
        double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

        PdfFieldDivMetadata sectionDivMetadata = new PdfFieldDivMetadata();
        //Get the first page where we get the Section heading has found.
        while (true) {
            try {
                //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
                targetedFieldNameDivList = pageDivList.stream()
                        .filter(pageDiv -> null != pageDiv.getFontWeight() && pageDiv.getFontWeight().equals("bold"))
                        .sorted(new PdfDivBean.PdfDivBeanTopAttrComparator())
                        .collect(Collectors.toList());

                boolean sectionNameFound = PdfParsingHelper.searchFieldAndGetMetadata(sectionDetailsVO.getSectionDisplayName(), targetedFieldNameDivList, sectionDivMetadata);
                if (sectionNameFound) {
                    //Start reading the invoice details form the page
                    if (!targetedFieldNameDivList.isEmpty()) {

                        if (PDFSections.INVOICE_AND_VALUATION_DETAILS_ITEM_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_SVB_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_PREV_BOE_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_RE_IMPORT_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_ITEM_MANUFACTURER_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_ACCESSORY_STATUS_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_LICENCE_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_CERTIFICATE_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_HSS_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONSTITUENTS_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_SINGLE_WINDOW_DECLARATION_CONTROL_DETAILS.getSectionKey().equals(sectionKey) ||
                                PDFSections.ADDITIONAL_SUPPORTING_DOC_DETAILS.getSectionKey().equals(sectionKey)) {
                            String invSrNo = getInvSrNoForItemDetails(pageDivList, targetedFieldNameDivList, pageWidth);
                            //Get all the Invoice Details level fields for the file.
                            List<PdfFieldStrategyMappingVO> pdfFieldMappingList = service.getPdfFieldStrategyMapping(sectionDetailsVO.getSectionKey(),
                                    PdfFileType.BILL_OF_ENTRY.name());
                            if(null != pdfFieldMappingList && !pdfFieldMappingList.isEmpty()){
                                targetedFieldNameDivList = targetedFieldNameDivList.stream()
                                        .filter(pageDiv -> pageDiv.getTop() > sectionDivMetadata.getFieldStartDiv().getTop())
                                        .collect(Collectors.toList());

                                List<PdfFieldDivMetadata> fieldDivMetadataList = new ArrayList<>();
                                for (PdfFieldStrategyMappingVO pdfFieldMapping : pdfFieldMappingList) {
                                    //Find the field and create the metadata for each field.
                                    PdfFieldDivMetadata fieldMetadata = new PdfFieldDivMetadata();
                                    fieldMetadata.setPageWidth(pageWidth);
                                    fieldMetadata.setFieldMappingKey(pdfFieldMapping.getMappingKey());

                                    try {
                                        if (PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldMapping.getFieldName(), targetedFieldNameDivList, fieldMetadata)) {
                                            if(!PDFSections.ADDITIONAL_LICENCE_DETAILS.getSectionKey().equals(sectionKey)) {
                                                PdfParsingHelper.checkAndSetFieldWrapperDiv(fieldMetadata, otherDivList);
                                            }
                                            fieldDivMetadataList.add(fieldMetadata);
                                        } else if (!pdfFieldMapping.isOptionalField()) {
                                            LOG.error("ERROR >> fetchRepeatingSectionDetails >> FIELD_NAME >> " + pdfFieldMapping.getFieldName() + " >> NOT_FOUND");
                                            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                                        }
                                    } catch (PdfReaderException e) {
                                        if (!pdfFieldMapping.isOptionalField()) {
                                            throw e;
                                        }
                                    }
                                }

                                dataList.addAll(ValueBelowFieldTableStrategyParser.fetchTableRowData(pageDivList,
                                        fieldDivMetadataList, pdfFieldMappingList.get(0).getBufferValue(),
                                        PdfFileType.SHIPPING_BILL.name(), sectionKey, invSrNo, null));
                            }

                        } else {

                            PdfDivBean firstFieldDiv;

                            //Get the fields which are below the section heading div row.
                            targetedFieldNameDivList = targetedFieldNameDivList.stream()
                                    .filter(pdfDiv -> pdfDiv.getTop() > sectionDivMetadata.getTop())
                                    .sorted(new PdfDivBean.PdfDivBeanLeftAttrComparator())
                                    .collect(Collectors.toList());

                            //Get the first row of the section below section heading.
                            double topOfNextRow = PdfUtil.calculateTopOfNextRow(sectionDivMetadata.getFieldStartDiv(), sectionDetailsVO.getBufferValue());
                            Optional<PdfDivBean> sectionStartRowDiv = targetedFieldNameDivList.stream().sorted(new PdfDivBean.PdfDivBeanTopAttrComparator()).filter(pdfDiv -> pdfDiv.getTop() == topOfNextRow).findFirst();
                            if (sectionStartRowDiv.isPresent()) {
                                firstFieldDiv = sectionStartRowDiv.get();
                            } else {
                                LOG.error("ERROR >> PdfParsingProcessorImpl >> processItemLevelFields >> FILE_TYPE >> " + PdfFileType.BILL_OF_ENTRY.name()
                                        + " >> SECTION_KEY >> invoice_details >> Error while getting the start of section");
                                throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
                            }

                            //Get all the Invoice Details level fields for the file.
                            List<PdfFieldStrategyMappingVO> pdfFieldsList = service.getPdfFieldStrategyMapping(sectionDetailsVO.getSectionKey(),
                                    PdfFileType.BILL_OF_ENTRY.name());

                            boolean repeatedSectionFound;
                            do {
                                repeatedSectionFound = false;
                                PdfDivBean finalFirstFieldDiv = firstFieldDiv;
                                targetedFieldNameDivList = targetedFieldNameDivList.stream().filter(pageDiv -> pageDiv.getTop() >= finalFirstFieldDiv.getTop()).collect(Collectors.toList());
                                Map<String, Object> dataMap = new HashMap<>();

                                if (PDFSections.INVOICE_AND_VALUATION_DETAILS.getSectionKey().equals(sectionKey) && !dataList.isEmpty()) {
                                    Optional<PdfFieldStrategyMappingVO> srNoFieldMappingDtlsOptional =
                                            pdfFieldsList.stream()
                                                    .filter(fieldDtls ->
                                                            fieldDtls.getMappingKey().equals(BoeDetailsFields.INV_SN.getValue()))
                                                    .findAny();

                                    if (srNoFieldMappingDtlsOptional.isPresent()) {
                                        getValue(fileReader, pageWidth, pageDivList, srNoFieldMappingDtlsOptional.get(),
                                                targetedFieldNameDivList, dataMap, null);
                                    }

                                    if (dataMap.containsKey(BoeDetailsFields.INV_SN.getValue())) {
                                        Object invSrNo = dataMap.get(BoeDetailsFields.INV_SN.getValue());
                                        if (dataList.stream().anyMatch(data ->
                                                data.containsKey(BoeDetailsFields.INV_SN.getValue()) &&
                                                        data.get(BoeDetailsFields.INV_SN.getValue()).equals(invSrNo))) {
                                            break;
                                        }
                                    }
                                }

                                //Start iterating for the fields
                                for (PdfFieldStrategyMappingVO pdfFieldStrategyMapping : pdfFieldsList) {
                                    getValue(fileReader, pageWidth, pageDivList, pdfFieldStrategyMapping,
                                            targetedFieldNameDivList, dataMap, otherDivList);
                                }

                                dataList.add(dataMap);

                                //Get to the next repeated section.
                                for (PdfDivBean pageDiv : targetedFieldNameDivList) {
                                    if (pageDiv.getTop() > firstFieldDiv.getTop() && pageDiv.getValue().equals(firstFieldDiv.getValue())) {
                                        firstFieldDiv = pageDiv;
                                        repeatedSectionFound = true;
                                        break;
                                    }
                                }
                            } while (repeatedSectionFound);
                        }
                    }
                }

                pageCount++;

                //Get all the data div from the page.
                pageDivList.clear();
                otherDivList.clear();
                PdfUtil.getAllRowsForThePage(fileReader, pageCount, false, pageDivList, otherDivList, false, 0);
                pageWidth = PdfUtil.fetchPageWidth(pageDivList);
            } catch (PdfReaderException e) {
                if (e.getErrorCode().equals(ResponseCode.REACHED_END_OF_PDF_FILE)) {
                    break;
                } else {
                    throw e;
                }
            }
        }
        if (sectionKey.equals(PDFSections.DUTIES.getSectionKey())) {
            findAmountValue(dataList);
        }
        return pageCount;
    }

    private String getInvSrNoForItemDetails(List<PdfDivBean> pageDivList, List<PdfDivBean> targetedFieldNameDivList, double pageWidth) throws PdfReaderException{
        String invSrNo = "0";
        PdfFieldStrategyMappingVO invSrNoParsingStrategyVo = service.getPdfReaderPdfFieldStrategyMappingRepo()
                .findFirstByMappingKeyAndSectionDetails_SectionKeyAndFileType(BoeDetailsFields.INV_SN.getValue(),
                        PDFSections.INVOICE_AND_VALUATION_DETAILS.getSectionKey(), PdfFileType.BILL_OF_ENTRY.name());

        PdfFieldDivMetadata fieldMetadata = new PdfFieldDivMetadata();
        fieldMetadata.setPageWidth(pageWidth);
        fieldMetadata.setFieldMappingKey(invSrNoParsingStrategyVo.getMappingKey());

        if(PdfParsingHelper.searchFieldAndGetMetadata(invSrNoParsingStrategyVo.getFieldName(), targetedFieldNameDivList, fieldMetadata)){
            invSrNo = ValueBelowFieldStrategyParser.fetchSingleLineValue(pageDivList, fieldMetadata, invSrNoParsingStrategyVo.getBufferValue(), true);
        }

        return invSrNo;
    }

    private void getValue(RandomAccessFile fileReader, double pageWidth, List<PdfDivBean> pageDivList,
                          PdfFieldStrategyMappingVO pdfFieldStrategyMapping, List<PdfDivBean> targetedFieldNameDivList,
                          Map<String, Object> dataMap, List<PdfDivBean> otherDivList) throws PdfReaderException {
        PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
        fieldDivMetadata.setPageWidth(pageWidth);
        boolean fieldFound = PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldStrategyMapping.getFieldName(),
                targetedFieldNameDivList, fieldDivMetadata);
        fieldDivMetadata.setFieldMappingKey(pdfFieldStrategyMapping.getMappingKey());
        if (fieldFound) {

            PdfParsingHelper.checkAndSetFieldWrapperDiv(fieldDivMetadata, otherDivList);

            if (pdfFieldStrategyMapping.getStrategy().isFieldSpecific()) {
                PdfParsingHelper.fetchValueFromFieldSpecificStrategy(pdfFieldStrategyMapping, fileReader, dataMap, pageDivList, 0);
            } else {
                dataMap.put(pdfFieldStrategyMapping.getMappingKey(),
                        PdfParsingHelper.fetchFieldValueBasedOnStrategy(pdfFieldStrategyMapping, pageDivList, fieldDivMetadata));
            }
        }
    }

    /**
     * <AUTHOR> Majithia
     * @since 03/01/2023
     * calculating amount value based on unit-price and quantity for each item
     * adding amount value in list of map for duties section.
     */
    private void findAmountValue(List<Map<String, Object>> dutiesDetailList) {
        for (Map<String, Object> map : dutiesDetailList) {
            double price = 0;
            double units = 0;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getKey().equals("unitPrice")) {
                    price = Double.parseDouble(entry.getValue().toString());
                }
                if (entry.getKey().equals("quantity")) {
                    units = Double.parseDouble(entry.getValue().toString());
                }
            }
            map.put("amount", NumberUtil.multiplyDoubles(units, price));
        }
    }

    private Map<String, Object> fetchAdditionalDetailsSectionData(RandomAccessFile fileReader) throws PdfReaderException {
        LOG.info("START >> PdfParsingProcessorImpl >> processAdditionalDetailsFields >> FILE_TYPE >> " + PdfFileType.BILL_OF_ENTRY.name());
        Map<String, Object> dataMap = new HashMap<>();
        //Get all the Bill Of Summary level fields for the file.
        List<PdfFieldStrategyMappingVO> pdfFieldsList = service.getPdfFieldStrategyMapping(PDFSections.ADDITIONAL_LICENCE_DETAILS.name(),
                PdfFileType.BILL_OF_ENTRY.name());

        if (null != pdfFieldsList && !pdfFieldsList.isEmpty()) {
            int pageCount = 0;

            //Get all the data div from the page.
            List<PdfDivBean> pageDivList = PdfUtil.getAllRowsForThePage(fileReader, pageCount, true);
            double pageWidth = PdfUtil.fetchPageWidth(pageDivList);

            //Get all the divs with the field name in to them. i.e. divs with font-weight - bold
            List<PdfDivBean> fieldNameDivList = PdfUtil.getAllFieldNameDivs(pageDivList);

            //Traverse over the fields list and get the data for the fields.
            for (PdfFieldStrategyMappingVO pdfFieldStrategyMapping : pdfFieldsList) {
                if (!pdfFieldStrategyMapping.getStrategy().isFieldSpecific()) {
                    PdfFieldDivMetadata fieldDivMetadata = new PdfFieldDivMetadata();
                    fieldDivMetadata.setPageWidth(pageWidth);

                    boolean fieldFound = PdfParsingHelper.searchFieldAndGetMetadata(pdfFieldStrategyMapping.getFieldName(), fieldNameDivList, fieldDivMetadata);

                    if (fieldFound) {
                        dataMap.put(pdfFieldStrategyMapping.getMappingKey(), PdfParsingHelper.fetchFieldValueBasedOnStrategy(pdfFieldStrategyMapping, pageDivList, fieldDivMetadata));
                    }

                } else {
                    PdfParsingHelper.fetchValueFromFieldSpecificStrategy(pdfFieldStrategyMapping, fileReader, dataMap, pageDivList, pageCount);
                }

            }
        } else {
            LOG.error("ERROR >> PdfParsingProcessorImpl >> processBillOfSummaryFields >> FILE_TYPE >> "
                    + PdfFileType.BILL_OF_ENTRY.name() + " >> Field Strategy Mapping not found");
            throw new PdfReaderException(ResponseCode.ERROR_WHILE_PARSING_PDF, ResponseMessage.ERROR_WHILE_PARSING_PDF_FILE);
        }
        LOG.info("END >> PdfParsingProcessorImpl >> processBillOfSummaryFields >> FILE_TYPE >> " + PdfFileType.BILL_OF_ENTRY.name());
        return dataMap;
    }

    public void putDataIntoMongo(Map<String, Object> billOfEntrySummaryDetailsMap, Map<String, Object> otherCompliancesMap,
                                 List<Map<String, Object>> invoiceAndValuationDetailList,
                                 List<Map<String, Object>> dutiesDetailList, List<Map<String, Object>> itemDetails,
                                 List<Map<String, Object>> additionalSvbDetails, List<Map<String, Object>> additionalPrevBoeDetails,
                                 List<Map<String, Object>> additionalReImportDetails,
                                 List<Map<String, Object>> additionalItemManufacturerDetails,
                                 List<Map<String, Object>> additionalAccessoryStatusDetails, List<Map<String, Object>> additionalLicenceDetails,
                                 List<Map<String, Object>> additionalCertificateDetails, List<Map<String, Object>> additionalHssDetails,
                                 List<Map<String, Object>> additionalSingleWindowDeclarationDetails,
                                 List<Map<String, Object>> additionalSingleWindowDeclarationConstituentsDetails,
                                 List<Map<String, Object>> additionalSingleWindowDeclarationControlDetails,
                                 List<Map<String, Object>> additionalSupportingDocDetails,
                                 List<Map<String, Object>> additionalContainerDocDetails,
                                 List<Map<String, Object>> bondDetails,
                                 List<Map<String, Object>> warehouseDetailList,
                                 List<Map<String, Object>> paymentDetailList, String txnId, String subTxnId,
                                 String pan, String boeNo)
            throws PdfReaderException {

        List<BasicDBObject> bulkDocumentList = new ArrayList<>();
        final List<FieldIdentificationStrategyVO> fieldIdentificationStrategyVoList =
            service.getFieldIdentificationStrategyRepo().findAllByPanAndFileTypeAndIsActive(pan,
                PdfFileType.BILL_OF_ENTRY.name(), true);

        //Set Item Level data
        for (Map<String, Object> dutyDetailsMap : dutiesDetailList) {
            BasicDBObject setter = new BasicDBObject(BoeDetailsFields.TXN_ID.getValue(), txnId)
                    .append(BoeDetailsFields.PAN.getValue(), pan).append(BoeDetailsFields.CREATED_AT.getValue(), new Date());

            if (StringUtils.isNotBlank(subTxnId)) {
                setter.append(BoeDetailsFields.SUB_TXN_ID.getValue(), subTxnId);
            }

            Object invSNo = dutyDetailsMap.get(BoeDetailsFields.INV_SN.getValue());
            Object itemSNo = dutyDetailsMap.get(BoeDetailsFields.ITEM_SN.getValue());

            for (Map.Entry<String, Object> dutyDetails : dutyDetailsMap.entrySet()) {
                BoeDetailsFields sheetFieldEnum = BoeDetailsFields.getFieldEnum(dutyDetails.getKey());
                Object value = Normalizer.normalizeAndGetValue(sheetFieldEnum.getValue(), dutyDetails.getValue(),
                        dataNormalizerVOList);

                if (sheetFieldEnum.equals(BoeDetailsFields.QUANTITY)) {
                    setter.append(BoeDetailsFields.IMPORTED_QTY.getValue(), value);
                    setter.append(BoeDetailsFields.AVAILABLE_QTY.getValue(), value);
                } else {
                    setter.append(sheetFieldEnum.getValue(), value);
                }

                checkAndIdentifyFields(sheetFieldEnum.getValue(), value, fieldIdentificationStrategyVoList, setter);

                iterateMapAndSetData(setter, billOfEntrySummaryDetailsMap, fieldIdentificationStrategyVoList);
                iterateMapAndSetData(setter, otherCompliancesMap, fieldIdentificationStrategyVoList);

                List<Map<String, Object>> itemLevelInvDetails = invoiceAndValuationDetailList.stream()
                        .filter(invDetailsMap -> invDetailsMap.get(BoeDetailsFields.INV_SN.getValue()).equals(invSNo))
                        .collect(Collectors.toList());

                if (!itemLevelInvDetails.isEmpty()) {
                    for (Map<String, Object> invDetailsMap : itemLevelInvDetails) {
                        iterateMapAndSetData(setter, invDetailsMap, fieldIdentificationStrategyVoList);
                    }
                }

                iterateMapAndSetData(setter, otherCompliancesMap, fieldIdentificationStrategyVoList);
            }

            //Set the Item value if present.
            if (Objects.nonNull(itemSNo)) {
                if(null != itemDetails && !itemDetails.isEmpty()) {
                    //Get the item details for the item sr.No
                    Optional<Map<String, Object>> itemDtlsOptional = itemDetails.stream().filter(itemDtl ->
                            itemSNo.equals(itemDtl.get(BoeDetailsFields.ITEM_SN.getValue())) &&
                                    invSNo.equals(itemDtl.get(BoeDetailsFields.INV_SN.getValue()).toString())
                    ).findFirst();

                    if (itemDtlsOptional.isPresent()) {
                        setter.append(BoeDetailsFields.ITEM_VALUE.getValue(),
                                Normalizer.normalizeAndGetValue(BoeDetailsFields.ITEM_VALUE.getValue(),
                                        itemDtlsOptional.get().get(BoeDetailsFields.ITEM_VALUE.getValue()), dataNormalizerVOList));
                    }
                }

                addItemLevelDetails(additionalSvbDetails, itemSNo, invSNo, setter, fieldIdentificationStrategyVoList);
                addItemLevelDetails(additionalPrevBoeDetails, itemSNo, invSNo, setter, fieldIdentificationStrategyVoList);
                addItemLevelDetails(additionalReImportDetails, itemSNo, invSNo, setter, fieldIdentificationStrategyVoList);
                addItemLevelDetails(additionalItemManufacturerDetails, itemSNo, invSNo, setter, fieldIdentificationStrategyVoList);
                addItemLevelDetails(additionalAccessoryStatusDetails, itemSNo, invSNo, setter, fieldIdentificationStrategyVoList);
                addItemLevelDetails(additionalLicenceDetails, itemSNo, invSNo, setter, fieldIdentificationStrategyVoList);
            }

            bulkDocumentList.add(setter);

            if (bulkDocumentList.size() >= 1000) {
                synchronized (BillOfEntryIPdfParser.class) {
                    mongoDao.bulkInsert(bulkDocumentList, NoSqlDBTables.BOE_DETAILS_STATEMENT);
                }
                bulkDocumentList.clear();
            }
        }

        if (!bulkDocumentList.isEmpty()) {
            synchronized (BillOfEntryIPdfParser.class) {
                mongoDao.bulkInsert(bulkDocumentList, NoSqlDBTables.BOE_DETAILS_STATEMENT);
            }
            bulkDocumentList.clear();
        }

        addAdditionalDetailsInDb(additionalCertificateDetails, additionalHssDetails,
                additionalSingleWindowDeclarationDetails, additionalSingleWindowDeclarationConstituentsDetails,
                additionalSingleWindowDeclarationControlDetails, additionalSupportingDocDetails,
                additionalContainerDocDetails, bondDetails, warehouseDetailList, paymentDetailList, txnId, subTxnId,
                pan, boeNo);
    }

    private void addAdditionalDetailsInDb(List<Map<String, Object>> additionalCertificateDetails,
                                          List<Map<String, Object>> additionalHssDetails,
                                          List<Map<String, Object>> additionalSingleWindowDeclarationDetails,
                                          List<Map<String, Object>> additionalSingleWindowDeclarationConstituentsDetails,
                                          List<Map<String, Object>> additionalSingleWindowDeclarationControlDetails,
                                          List<Map<String, Object>> additionalSupportingDocDetails,
                                          List<Map<String, Object>> additionalContainerDocDetails,
                                          List<Map<String, Object>> bondDetails,
                                          List<Map<String, Object>> warehouseDetailList,
                                          List<Map<String, Object>> paymentDetailList,
                                          String txnId, String subTxnId, String pan, String boeNo) throws PdfReaderException {
        //Add this in the additional Details section of the BOE details
        iterateMapAndSetListData(bondDetails, BoeAdditionalDetailsFields.BOE_BOND_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(paymentDetailList, BoeAdditionalDetailsFields.BOE_PAYMENT_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(warehouseDetailList, BoeAdditionalDetailsFields.BOE_WAREHOUSE_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(additionalCertificateDetails,
                BoeAdditionalDetailsFields.ADDITIONAL_CERTIFICATE_DETAILS.getValue(), txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(additionalHssDetails, BoeAdditionalDetailsFields.ADDITIONAL_HSS_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(additionalSingleWindowDeclarationDetails, BoeAdditionalDetailsFields.SINGLE_WINDOW_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(additionalSingleWindowDeclarationConstituentsDetails,
                BoeAdditionalDetailsFields.SINGLE_WINDOW_CONST_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(additionalSingleWindowDeclarationControlDetails,
                BoeAdditionalDetailsFields.SINGLE_WINDOW_CONTROL_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(additionalSupportingDocDetails,
                BoeAdditionalDetailsFields.SUPPORTING_DOCS_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
        iterateMapAndSetListData(additionalContainerDocDetails,
                BoeAdditionalDetailsFields.ADDITIONAL_CONTAINER_DETAILS.getValue(),
                txnId, subTxnId, pan, boeNo, true);
    }

    private void addItemLevelDetails(List<Map<String, Object>> additionalDetails, Object itemSNo, Object invSNo,
                                     BasicDBObject setter, List<FieldIdentificationStrategyVO> fieldIdentificationStrategyVoList
    ) throws PdfReaderException{
        if(null != additionalDetails && !additionalDetails.isEmpty()) {
            //Get the Licence details for the item sr.No and Invoice Sr.No
            Optional<Map<String, Object>> additionalDtlsOptional = additionalDetails.stream().filter(additionalDtls ->
                    itemSNo.equals(additionalDtls.get(BoeDetailsFields.ITEM_SN.getValue())) &&
                            invSNo.equals(additionalDtls.get(BoeDetailsFields.INV_SN.getValue()).toString())
            ).findFirst();

            if (additionalDtlsOptional.isPresent()) {
                iterateMapAndSetData(setter, additionalDtlsOptional.get(), fieldIdentificationStrategyVoList);
            }
        }
    }

    private void iterateMapAndSetData(BasicDBObject setter, Map<String, Object> detailsMap, List<FieldIdentificationStrategyVO> fieldIdentificationStrategyVoList) throws PdfReaderException {
        for (Map.Entry<String, Object> details : detailsMap.entrySet()) {
            BoeDetailsFields sheetFieldEnum = BoeDetailsFields.getFieldEnum(details.getKey());
            Object value = Normalizer.normalizeAndGetValue(sheetFieldEnum.getValue(), details.getValue(), dataNormalizerVOList);

            setter.append(sheetFieldEnum.getValue(), value);
            checkAndIdentifyFields(details.getKey(), value, fieldIdentificationStrategyVoList, setter);
        }
    }

    private void iterateMapAndSetListData(List<Map<String, Object>> detailsMapList, String key,
                                          String txnId, String subTxnId, String pan, String boeNo,
                                          boolean isAdditionalField) throws PdfReaderException {
        if(null != detailsMapList && !detailsMapList.isEmpty()){
            List<BasicDBObject> setterList = new ArrayList<>();
            for (Map<String, Object> detailsMap : detailsMapList) {
                BasicDBObject detailsSetter = new BasicDBObject();
                for (Map.Entry<String, Object> details : detailsMap.entrySet()) {
                    String fieldName;
                    if(isAdditionalField){
                        BoeAdditionalDetailsFields sheetFieldEnum = BoeAdditionalDetailsFields.getFieldEnum(details.getKey());
                        fieldName = sheetFieldEnum.getValue();
                    }else {
                        BoeDetailsFields sheetFieldEnum = BoeDetailsFields.getFieldEnum(details.getKey());
                        fieldName = sheetFieldEnum.getValue();
                    }

                    Object value = Normalizer.normalizeAndGetValue(fieldName, details.getValue(), dataNormalizerVOList);
                    detailsSetter.append(fieldName, value);
                }
                setterList.add(detailsSetter);

                if(setterList.size() >= 1000){
                    BasicDBList andList = new BasicDBList();
                    andList.add(new BasicDBObject(BoeDetailsFields.TXN_ID.getValue(), txnId));
                    andList.add(new BasicDBObject(BoeDetailsFields.PAN.getValue(), pan));
                    andList.add(new BasicDBObject(BoeDetailsFields.SUB_TXN_ID.getValue(), subTxnId));
                    andList.add(new BasicDBObject(BoeDetailsFields.BOE_NO.getValue(), boeNo));

                    BasicDBObject query = new BasicDBObject(QueryOperators.$and, andList);
                    BasicDBObject setter = new BasicDBObject();
                    setter.append(key, new BasicDBObject(QueryOperators.$each, setterList));

                    BasicDBObject update = new BasicDBObject().append(QueryOperators.$setOnInsert,
                            new BasicDBObject(SBInvoiceDetailsFields.CREATED_AT.getValue(), new Date()));

                    update.append(QueryOperators.$push, setter);
                    update.append(QueryOperators.$set, setter);
                    List<WriteModel<BasicDBObject>> bulkDocumentList = new ArrayList<>();
                    bulkDocumentList.add(new UpdateManyModel<>(query, update, new UpdateOptions().upsert(true)));

                    LOG.info("PdfParsingProcessorImpl >> putDataIntoMongoDb >> COLLECTION_NAME >> " + NoSqlDBTables.BOE_ADDITIONAL_DETAILS_STATEMENT + " >>  Putting Data into Mongo db" + bulkDocumentList);
                    mongoDao.bulkUpsert(bulkDocumentList, NoSqlDBTables.BOE_ADDITIONAL_DETAILS_STATEMENT);
                    bulkDocumentList.clear();
                    setterList.clear();
                }
            }

            if(!setterList.isEmpty()){
                BasicDBList andList = new BasicDBList();
                andList.add(new BasicDBObject(BoeDetailsFields.TXN_ID.getValue(), txnId));
                andList.add(new BasicDBObject(BoeDetailsFields.PAN.getValue(), pan));
                andList.add(new BasicDBObject(BoeDetailsFields.SUB_TXN_ID.getValue(), subTxnId));
                andList.add(new BasicDBObject(BoeDetailsFields.BOE_NO.getValue(), boeNo));

                BasicDBObject query = new BasicDBObject(QueryOperators.$and, andList);
                BasicDBObject setter = new BasicDBObject();
                setter.append(key, new BasicDBObject(QueryOperators.$each, setterList));

                BasicDBObject update = new BasicDBObject().append(QueryOperators.$setOnInsert,
                        new BasicDBObject(SBInvoiceDetailsFields.CREATED_AT.getValue(), new Date()));

                update.append(QueryOperators.$push, setter);
                List<WriteModel<BasicDBObject>> bulkDocumentList = new ArrayList<>();
                bulkDocumentList.add(new UpdateManyModel<>(query, update, new UpdateOptions().upsert(true)));

                LOG.info("PdfParsingProcessorImpl >> putDataIntoMongoDb >> COLLECTION_NAME >> " + NoSqlDBTables.BOE_ADDITIONAL_DETAILS_STATEMENT + " >>  Putting Data into Mongo db" + bulkDocumentList);
                mongoDao.bulkUpsert(bulkDocumentList, NoSqlDBTables.BOE_ADDITIONAL_DETAILS_STATEMENT);
                bulkDocumentList.clear();
                setterList.clear();
            }
        }
    }

    private void checkAndIdentifyFields(String key, Object sourceValue, List<FieldIdentificationStrategyVO> fieldIdentificationStrategyVoList, BasicDBObject setter) throws PdfReaderException {

        if(null != sourceValue && StringUtils.isNotBlank(sourceValue.toString())){
            Optional<FieldIdentificationStrategyVO> strategyVoOptional = fieldIdentificationStrategyVoList.stream()
                    .filter(strategyVo -> strategyVo.getSourceField().equals(key))
                    .findFirst();

            if (strategyVoOptional.isPresent()) {
                FieldIdentificationStrategyVO strategyVO = strategyVoOptional.get();
                String targetFieldValue = FieldIdentificationHelper.getFieldValueUsingStrategy(strategyVO, sourceValue.toString());

                if (StringUtils.isNotBlank(targetFieldValue)) {
                    setter.append(strategyVO.getTargetField(), targetFieldValue);
                }

            }
        }
    }
}
