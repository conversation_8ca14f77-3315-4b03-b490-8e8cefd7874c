package com.perennialsys.pdfreader.controller;

import com.perennialsys.pdfreader.constants.PdfReaderApiHeaders;
import com.perennialsys.pdfreader.constants.PdfReaderApiParams;
import com.perennialsys.pdfreader.constants.PdfReaderApiUrls;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.dto.PdfFileTxnDetailsDto;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.SearchAndSorKeyEnum;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.IPdfParsingHandler;
import com.perennialsys.pdfreader.helper.ResponseHelper;
import com.perennialsys.pdfreader.util.StringUtils;
import com.perennialsys.pdfreader.validator.ValidationProcessor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiParam;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;

@EnableSwagger2
@Api(value = PdfReaderApiUrls.FILES, description = "Pdf reader operations")
@RestController
@RequestMapping(value = PdfReaderApiUrls.FILES)
public class PdfParsingController {

    private final Logger LOG = Logger.getLogger(PdfParsingController.class);

    private final IPdfParsingHandler pdfParsingHandler;

    @Autowired
    PdfParsingController(IPdfParsingHandler pdfParsingHandler) {
        this.pdfParsingHandler = pdfParsingHandler;
    }

    @GetMapping(value = PdfReaderApiUrls.FILE_PROCESSING_DETAILS, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "FILE_PROCESSING_DTLS",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> getFileProcessingDetails(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true)
            @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.FILE_TYPE, required = true,
                    allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL, ALL")
            @RequestHeader(value = PdfReaderApiHeaders.FILE_TYPE) String fileType) throws Exception {
        List<PdfFileTxnDetailsDto> response;
        LOG.info("START >> PdfParsingController >> getFileProcessingDetails");
        if (StringUtils.isBlank(pan)) {
            LOG.error("ERROR: PdfParsingController >> getFileProcessingDetails >> Invalid pan Value");
            throw new PdfReaderException(ResponseCode.BLANK_PAN, ResponseMessage.BLANK_PAN);
        }
        if (fileType.equals("ALL") || fileType.equals(PdfFileType.SHIPPING_BILL.name())
                || fileType.equals(PdfFileType.BILL_OF_ENTRY.name())) {
            response = pdfParsingHandler.getFileProcessingDetails(pan, fileType);
        } else {
            LOG.error("ERROR: PdfParsingController >> getFileProcessingDetails >> Invalid fileType Value");
            throw new PdfReaderException(ResponseCode.INVALID_FILE_TYPE, ResponseMessage.INVALID_FILE_TYPE);
        }

        LOG.info("END >> PdfParsingController >> getFileProcessingDetails");
        return new ResponseEntity<>(ResponseHelper.success(response), HttpStatus.OK);
    }

    @PostMapping(value = PdfReaderApiUrls.UPLOAD_PDF, produces = MediaType.APPLICATION_JSON_VALUE, consumes =
            MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "PDF_FILE_UPLOAD",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> uploadPdfInvoice(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true)
            @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.FILE_TYPE, required = true, allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL")
            @RequestHeader(value = PdfReaderApiHeaders.FILE_TYPE) String fileType,
            @RequestPart(value = "attachedFiles") List<MultipartFile> attachedFilesList) throws Exception {

        LOG.info("START >> PdfParsingController >> uploadPdfInvoice >> PAN " + pan + " >> FILE_TYPE >> " + fileType);

        if (StringUtils.isBlank(pan) || StringUtils.isBlank(fileType)) {
            LOG.error("ERROR >> PdfParsingController >> uploadPdfInvoice >> PAN " + pan + " >> FILE_TYPE >> " + fileType
                    + " >> "+ ResponseMessage.MISSING_MANDATORY_HEADERS);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        PdfFileType pdfFileType = PdfFileType.validateExportFileType(fileType);

        //List<MultipartFile> attachedFilesList = fileRequest.getFiles("attachedFiles");

        if (attachedFilesList.isEmpty() || attachedFilesList.get(0) == null) {
            LOG.error("ERROR >> PdfParsingController >> uploadPdfInvoice >> PAN " + pan + " >> FILE_TYPE >> " + fileType
                    + " >> "+ ResponseMessage.UNABLE_TO_READ_FILE_DATA);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_READ_FILE_DATA, ResponseMessage.UNABLE_TO_READ_FILE_DATA);
        }else {
            ValidationProcessor.validateValidPdfFiles(attachedFilesList);
        }

        Map<String, Object> sheetData = pdfParsingHandler.uploadPdfInvoices(pan, attachedFilesList, pdfFileType);

        LOG.info("END >> PdfParsingController >> uploadPdfInvoice >> PAN " + pan + " >> FILE_TYPE >> " + fileType);
        return new ResponseEntity<>(sheetData, HttpStatus.OK);
    }

    @PutMapping(value = PdfReaderApiUrls.CONVERT_TO_HTML, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "CONVERT_PDF_TO_HTML",

                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",

                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",

                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<?> convertPdfToHtml(MultipartHttpServletRequest fileRequest) throws Exception {

        LOG.info("START >> PdfParsingController >> convertPdfToHtml");

        List<MultipartFile> attachedFilesList = fileRequest.getFiles("attachedFiles");

        if (attachedFilesList.isEmpty() || attachedFilesList.get(0) == null) {
            LOG.error("ERROR >> PdfParsingController >> convertPdfToHtml >> " + ResponseMessage.UNABLE_TO_READ_FILE_DATA);
            throw new PdfReaderException(ResponseCode.UNABLE_TO_READ_FILE_DATA, ResponseMessage.UNABLE_TO_READ_FILE_DATA);
        }

        ResponseEntity<?> responseEntity = pdfParsingHandler.convertPdfToHtml(attachedFilesList.get(0));

        LOG.info("END >> PdfParsingController >> convertPdfToHtml");
        return responseEntity;
    }

    @GetMapping(value = PdfReaderApiUrls.GET_FILE_UPLOAD_HISTORY, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "FILE_UPLOAD_HISTORY",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> getFileUploadHistory(
            @ApiParam(defaultValue = "1") @RequestParam(value = PdfReaderApiParams.PAGE_NO, required = false) int pageNo,
            @ApiParam(defaultValue = "5") @RequestParam(value = PdfReaderApiParams.LIMIT, required = false) int limit,
            @ApiParam(value = PdfReaderApiHeaders.SEARCH_KEY,
                    allowableValues = "last-updated-date, processing-status, last-updated-by")
            @RequestHeader(value = PdfReaderApiHeaders.SEARCH_KEY, required = false) String searchKey,
            @ApiParam(value = PdfReaderApiHeaders.SEARCH_VALUE)
            @RequestHeader(value = PdfReaderApiHeaders.SEARCH_VALUE, required = false) String searchValue,
            @ApiParam(value = PdfReaderApiHeaders.SORT_BY,
                    allowableValues = "last-updated-date, processing-status, last-updated-by")
            @RequestHeader(value = PdfReaderApiHeaders.SORT_BY, required = false) String sortBy,
            @ApiParam(value = PdfReaderApiHeaders.SORTING_ORDER,
                    allowableValues = "1, -1")
            @RequestHeader(value = PdfReaderApiHeaders.SORTING_ORDER, required = false, defaultValue = "-1")
            @Pattern(regexp = "1|-1", message = ResponseMessage.INVALID_SORTING_ORDER) short sortingOrder,
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true)
            @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.FILE_TYPE, required = true,
                    allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL")
            @RequestHeader(value = PdfReaderApiHeaders.FILE_TYPE) String fileType) throws PdfReaderException {
        LOG.info("START >> PdfParsingController >> getFileUploadHistory >> PAN >> "+pan+" >> FILE_TYPE >> "+fileType
                +" SEARCH_KEY >> "+searchKey+" >> SEARCH_VALUE >> "+searchValue);
        if (StringUtils.isBlank(pan)
                || StringUtils.isBlank(fileType)) {
            LOG.error("ERROR >> PdfParsingController >> getFileUploadHistory >> PAN >> "+pan+" >> FILE_TYPE >> "+fileType
                +" SEARCH_KEY >> "+searchKey+" >> SEARCH_VALUE >> "+searchValue+" >> "+ResponseMessage.MISSING_MANDATORY_HEADERS);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        if (!fileType.equals(PdfFileType.SHIPPING_BILL.name())
                && !fileType.equals(PdfFileType.BILL_OF_ENTRY.name())) {
            LOG.error("ERROR >> PdfParsingController >> getFileUploadHistory >> PAN >> "+pan+" >> FILE_TYPE >> "+fileType
                +" SEARCH_KEY >> "+searchKey+" >> SEARCH_VALUE >> "+searchValue+" >> "+ResponseMessage.INVALID_DATA);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_DATA);
        }

        Map<String, Object> responseMap = null;

        if(StringUtils.isBlank(searchValue) || SearchAndSorKeyEnum.validateKey(searchKey, "UPLOAD_FILE_HISTORY")) {
            pageNo = pageNo <= 0 ? 0 : pageNo - 1;
            limit = limit <= 0 ? 5 : limit;
            responseMap = pdfParsingHandler.getFileUploadHistory(pan, fileType, pageNo, limit, searchKey, searchValue,
                    sortBy, sortingOrder);
        }

        LOG.info("END >> PdfParsingController >> getFileUploadHistory >> PAN >> "+pan+" >> FILE_TYPE >> "+fileType
                +" SEARCH_KEY >> "+searchKey+" >> SEARCH_VALUE >> "+searchValue);
        return new ResponseEntity<>(responseMap, HttpStatus.OK);
    }

    @GetMapping(value = PdfReaderApiUrls.GET_FILE_UPLOAD_DETAILS, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "FILE_UPLOAD_DETAILS",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> getFileUploadDetails(
            @ApiParam(defaultValue = "1") @RequestParam(value = PdfReaderApiParams.PAGE_NO, required = false) int pageNo,
            @ApiParam(defaultValue = "5") @RequestParam(value = PdfReaderApiParams.LIMIT, required = false) int limit,
            @ApiParam(value = PdfReaderApiHeaders.SEARCH_KEY,
                allowableValues = "file-name")
                @RequestHeader(value = PdfReaderApiHeaders.SEARCH_KEY, required = false) String searchKey,
            @ApiParam(value = PdfReaderApiHeaders.SEARCH_VALUE)
                @RequestHeader(value = PdfReaderApiHeaders.SEARCH_VALUE, required = false) String searchValue,
            @ApiParam(value = PdfReaderApiHeaders.SORT_BY,
                allowableValues = "file-name")
                @RequestHeader(value = PdfReaderApiHeaders.SORT_BY, required = false) String sortBy,
            @ApiParam(value = PdfReaderApiHeaders.SORTING_ORDER,
                allowableValues = "1, -1")
                @RequestHeader(value = PdfReaderApiHeaders.SORTING_ORDER, required = false, defaultValue = "-1")
            @Pattern(regexp = "1|-1", message = ResponseMessage.INVALID_SORTING_ORDER) short sortingOrder,
            @ApiParam(defaultValue = "false", required = true)
                @RequestParam(value = PdfReaderApiParams.FAILED_FILES, defaultValue = "false") boolean returnFailedFiles,
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true) @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.TXN_ID, required = true)
                @RequestHeader(value = PdfReaderApiHeaders.TXN_ID) String txnId) throws PdfReaderException {
        LOG.info("START >> PdfParsingController >> getFileUploadDetails >> PAN >> "+pan+" >> TXN_ID >> "+txnId);
        if (StringUtils.isBlank(pan) || StringUtils.isBlank(txnId)) {
            LOG.error("ERROR >> PdfParsingController >> getFileUploadDetails >> PAN >> "+pan+" >> TXN_ID >> "
                    +txnId+" >> "+ResponseMessage.MISSING_MANDATORY_HEADERS);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        Map<String, Object> responseMap = null;
        if(StringUtils.isBlank(searchValue) || SearchAndSorKeyEnum.validateKey(searchKey, "UPLOAD_FILE_DETAILS")) {

            pageNo = pageNo <= 0 ? 0 : pageNo - 1;
            limit = limit <= 0 ? 5 : limit;

            responseMap = pdfParsingHandler.getFileUploadDetails(pan, txnId, returnFailedFiles, pageNo,
                    limit, searchKey, searchValue, sortBy, sortingOrder);
        }
        LOG.info("END >> PdfParsingController >> getFileUploadDetails >> PAN >> "+pan+" >> TXN_ID >> "+txnId);
        return new ResponseEntity<>(responseMap, HttpStatus.OK);
    }

    @GetMapping(value = PdfReaderApiUrls.PDF_FILE,
            produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "GET_PDF_FILE",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<?> getPdfFile(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true) @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.FILE_TYPE, required = true, allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL")
                @RequestHeader(value = PdfReaderApiHeaders.FILE_TYPE) String fileType,
            @ApiParam(value = PdfReaderApiHeaders.FILE_ID)
                @RequestHeader(value = PdfReaderApiHeaders.FILE_ID, required = false) String fileTxnId,
            @ApiParam(value = PdfReaderApiHeaders.TXN_ID)
                @RequestHeader(value = PdfReaderApiHeaders.TXN_ID, required = false) String txnId,
            @ApiParam(value = PdfReaderApiHeaders.SHOULD_RETURN_FILE, required = true, defaultValue = "false")
                @RequestHeader(value = PdfReaderApiHeaders.SHOULD_RETURN_FILE, defaultValue = "false") boolean shouldReturnFile)
            throws PdfReaderException {
        LOG.info("START >> PdfParsingController >> getPdfFile >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+" >> TXN_ID >> "
            +txnId+" >> FILE_TYPE "+fileType);

        if (StringUtils.isBlank(pan) || (StringUtils.isBlank(fileTxnId) && StringUtils.isBlank(txnId)) || StringUtils.isBlank(fileType)) {
            LOG.error("ERROR >> PdfParsingController >> getPdfFile >> PAN >> "+pan+" >> FILE_ID >> "
                +fileTxnId+" >> TXN_ID >> "+txnId+" >> FILE_TYPE "+fileType+" >> "+ResponseMessage.MISSING_MANDATORY_HEADERS);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        ResponseEntity<?> responseEntity = pdfParsingHandler.getFiles(pan, txnId, fileTxnId, fileType, shouldReturnFile);
        LOG.info("END >> PdfParsingController >> getPdfFile >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+" >> TXN_ID >> "
            +txnId+" >> FILE_TYPE "+fileType);
        return responseEntity;
    }

    @DeleteMapping(value = PdfReaderApiUrls.PDF_FILE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "DELETE_PDF_FILE",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<?> deletePdfFile(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true) @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.FILE_TYPE, required = true, allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL")
                @RequestHeader(value = PdfReaderApiHeaders.FILE_TYPE) String fileType,
            @ApiParam(value = PdfReaderApiHeaders.FILE_ID)
                @RequestHeader(value = PdfReaderApiHeaders.FILE_ID, required = false) String fileTxnId,
            @ApiParam(value = PdfReaderApiHeaders.TXN_ID)
                @RequestHeader(value = PdfReaderApiHeaders.TXN_ID, required = false) String txnId)
            throws PdfReaderException {
        LOG.info("START >> PdfParsingController >> deletePdfFile >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+" >> FILE_TYPE "+fileType);

        if (StringUtils.isBlank(pan) || (StringUtils.isBlank(fileTxnId) && StringUtils.isBlank(txnId)) || StringUtils.isBlank(fileType)) {
            LOG.error("ERROR >> PdfParsingController >> deletePdfFile >> PAN >> "+pan+" >> FILE_ID >> "
                +fileTxnId+" >> FILE_TYPE "+fileType+" >> "+ResponseMessage.MISSING_MANDATORY_HEADERS);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        Map<String, Object> responseMap = pdfParsingHandler.deleteFile(pan, fileTxnId, txnId, fileType);

        LOG.info("END >> PdfParsingController >> deletePdfFile >> PAN >> "+pan+" >> FILE_ID >> "+fileTxnId+" >> FILE_TYPE "+fileType);
        return new ResponseEntity<>(responseMap, HttpStatus.OK);
    }

    @GetMapping(value = PdfReaderApiUrls.PROCESSED_FILES_DETAILS,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "GET_PROCESSED_FILES",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> getProcessedFilesDetails(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true) @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.FILE_TYPE, required = true, allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL")
            @RequestHeader(value = PdfReaderApiHeaders.FILE_TYPE) String fileType,
            @ApiParam(value = PdfReaderApiHeaders.START_PERIOD)
            @RequestHeader(value = PdfReaderApiHeaders.START_PERIOD, required = false) String startPeriod,
            @ApiParam(value = PdfReaderApiHeaders.END_PERIOD)
            @RequestHeader(value = PdfReaderApiHeaders.END_PERIOD, required = false) String endPeriod,
            @ApiParam(value = PdfReaderApiHeaders.TXN_ID)
            @RequestHeader(value = PdfReaderApiHeaders.TXN_ID, required = false) String txnId,
            @ApiParam(defaultValue = "1") @RequestParam(value = PdfReaderApiParams.PAGE_NO, required = false) int pageNo,
            @ApiParam(defaultValue = "5") @RequestParam(value = PdfReaderApiParams.LIMIT, required = false) int limit,
            @ApiParam(value = PdfReaderApiHeaders.SEARCH_KEY,
                allowableValues = "sb-no, sb-date, boe-no, boe-date, be-type")
                @RequestHeader(value = PdfReaderApiHeaders.SEARCH_KEY, required = false) String searchKey,
            @ApiParam(value = PdfReaderApiHeaders.SEARCH_VALUE)
                @RequestHeader(value = PdfReaderApiHeaders.SEARCH_VALUE, required = false) String searchValue,
            @ApiParam(value = PdfReaderApiHeaders.SORT_BY,
                allowableValues = "sb-no, sb-date, boe-no, boe-date, be-type")
                @RequestHeader(value = PdfReaderApiHeaders.SORT_BY, required = false) String sortBy,
            @ApiParam(value = PdfReaderApiHeaders.SORTING_ORDER,
                allowableValues = "1, -1")
                @RequestHeader(value = PdfReaderApiHeaders.SORTING_ORDER, required = false, defaultValue = "-1")
            @Pattern(regexp = "1|-1", message = ResponseMessage.INVALID_SORTING_ORDER) short sortingOrder)
            throws PdfReaderException {
        LOG.info("START >> PdfParsingController >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
            " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> TXN_ID >> "+txnId);

        if (StringUtils.isBlank(pan) || (StringUtils.isBlank(startPeriod) && StringUtils.isBlank(endPeriod)
            && StringUtils.isBlank(txnId))  || StringUtils.isBlank(fileType)) {
            LOG.error("ERROR >> PdfParsingController >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
                " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> TXN_ID >> "+txnId+" >> "+
                ResponseMessage.MISSING_MANDATORY_HEADERS);
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        Map<String, Object> responseMap = null;
        if(StringUtils.isBlank(searchValue) || SearchAndSorKeyEnum.validateKey(searchKey, "PROCESSED_FILE_DETAILS")) {
            pageNo = pageNo <= 0 ? 0 : pageNo - 1;
            limit = limit <= 0 ? 5 : limit;

            responseMap = pdfParsingHandler.getProcessedFilesDetails(pan, fileType, startPeriod,
                    endPeriod, txnId, pageNo, limit, searchKey, searchValue,
                    sortBy, sortingOrder);
        }

        LOG.info("END >> PdfParsingController >> getProcessedFilesDetails >> PAN >> "+pan+" >> FILE_TYPE "+fileType+
            " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> TXN_ID >> "+txnId);
        return new ResponseEntity<>(responseMap, HttpStatus.OK);
    }


    @GetMapping(value = PdfReaderApiUrls.SHIPPING_BILL_DETAILS,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "SHIPPING_BILL_DETAILS",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
                    paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> getShippingBillDetails(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true) @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.IEC_CODE, required = true) @RequestHeader(value = PdfReaderApiHeaders.IEC_CODE) String iecCode,

            @ApiParam(value = PdfReaderApiHeaders.START_PERIOD)
            @RequestHeader(value = PdfReaderApiHeaders.START_PERIOD, required = false) String startPeriod,
            @ApiParam(value = PdfReaderApiHeaders.END_PERIOD)
            @RequestHeader(value = PdfReaderApiHeaders.END_PERIOD, required = false) String endPeriod,
            @ApiParam(value = PdfReaderApiHeaders.SERVICE_NAME, allowableValues = "MOOWR, EBRC")
            @RequestHeader(value = PdfReaderApiHeaders.SERVICE_NAME) String serviceName,
            @ApiParam(defaultValue = "1") @RequestParam(value = PdfReaderApiParams.PAGE_NO, required = false) int pageNo,
            @ApiParam(defaultValue = "5") @RequestParam(value = PdfReaderApiParams.LIMIT, required = false) int limit)
            throws PdfReaderException {
        LOG.info("START >> PdfParsingController >> getShippingBillDetails >> PAN >> "+pan+
                " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> PRODUCT_DTL_REQ >> "+serviceName);

        if (StringUtils.isBlank(pan) || (StringUtils.isBlank(startPeriod) && StringUtils.isBlank(endPeriod)
                && StringUtils.isBlank(iecCode))) {

            LOG.error("ERROR >> PdfParsingController >> getShippingBillDetails >> PAN >> "+pan+
                    " >> IEC_CODE >> "+iecCode+" >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+
                    " >> PRODUCT_DTL_REQ >> "+serviceName+" >> "+ResponseMessage.MISSING_MANDATORY_HEADERS);

            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        Map<String, Object> responseMap = null;
            pageNo = pageNo <= 0 ? 0 : pageNo - 1;
            limit = limit <= 0 ? 5 : limit;

            responseMap = pdfParsingHandler.getShippingBillDetails(pan, iecCode, startPeriod,
                    endPeriod, serviceName, pageNo, limit);

        LOG.info("END >> PdfParsingController >> getShippingBillDetails >> PAN >> "+pan+
                " >> START_PERIOD >> "+startPeriod+" >> END_PERIOD >> "+endPeriod+" >> PRODUCT_DTL_REQ >> "+serviceName);
        return new ResponseEntity<>(responseMap, HttpStatus.OK);
    }
}
