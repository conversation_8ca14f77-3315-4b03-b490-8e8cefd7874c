package com.perennialsys.pdfreader.controller;

import com.perennialsys.pdfreader.constants.PdfReaderApiHeaders;
import com.perennialsys.pdfreader.constants.PdfReaderApiParams;
import com.perennialsys.pdfreader.constants.PdfReaderApiUrls;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.dto.FieldIdentificationStrategyDto;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.IPdfFieldIdentificationStrategyHandler;
import com.perennialsys.pdfreader.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiParam;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(value = PdfReaderApiUrls.FIELD_IDENTIFICATION_STRATEGY, description = "Pdf Field Identification Operations")
@RestController
@RequestMapping(value = PdfReaderApiUrls.FIELD_IDENTIFICATION_STRATEGY)
public class PdfFieldIdentificationController {

    private final Logger LOG = Logger.getLogger(PdfFieldIdentificationController.class);

    private final IPdfFieldIdentificationStrategyHandler fieldIdentificationStrategyHandler;

    @Autowired
    PdfFieldIdentificationController(IPdfFieldIdentificationStrategyHandler fieldIdentificationStrategyHandler) {
        this.fieldIdentificationStrategyHandler = fieldIdentificationStrategyHandler;
    }

    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "SET_FIELD_IDENTIFICATION_STRATEGY", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email", paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> setFieldIdentificationStrategy(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true) @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @RequestBody FieldIdentificationStrategyDto fieldIdentificationStrategyDto
    ) throws PdfReaderException {
        LOG.info("START >> PdfFieldIdentificationController >> setFieldIdentificationStrategy >> PAN >> " + pan);
        if (StringUtils.isBlank(pan)) {
            LOG.error("ERROR >> PdfFieldIdentificationController >> setFieldIdentificationStrategy >> PAN >> " + pan + " >> Empty PAN");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_DATA);
        }

        fieldIdentificationStrategyDto.validate();

        Map<String, Object> responseMap = fieldIdentificationStrategyHandler.setFieldIdentificationStrategy(pan, fieldIdentificationStrategyDto);
        LOG.info("END >> PdfFieldIdentificationController >> setFieldIdentificationStrategy >> PAN >> " + pan);
        return new ResponseEntity<>(responseMap, HttpStatus.OK);
    }

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "GET_FIELD_IDENTIFICATION_STRATEGY", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email", paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> getFieldIdentificationStrategy(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true) @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.FILE_TYPE, required = true) @RequestHeader(value = PdfReaderApiHeaders.FILE_TYPE, required = false) String fileType
    ) throws PdfReaderException {
        LOG.info("START >> PdfFieldIdentificationController >> getFieldIdentificationStrategy >> PAN >> " + pan + " >> FILE_TYPE >> " + fileType);
        if (StringUtils.isBlank(pan)
                || StringUtils.isBlank(fileType)) {
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_DATA);
        }

        if (!fileType.equals(PdfFileType.SHIPPING_BILL.name())
                && !fileType.equals(PdfFileType.BILL_OF_ENTRY.name())) {
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_DATA);
        }

        Map<String, Object> responseMap = fieldIdentificationStrategyHandler.getFieldIdentificationStrategies(pan, fileType);
        LOG.info("END >> PdfFieldIdentificationController >> getFieldIdentificationStrategy >> PAN >> " + pan + " >> FILE_TYPE >> " + fileType);
        return new ResponseEntity<>(responseMap, HttpStatus.OK);
    }

    @DeleteMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "DELETE_FIELD_IDENTIFICATION_STRATEGY", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email", paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> deleteFieldIdentificationStrategy(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true) @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.FILE_TYPE, required = true) @RequestHeader(value = PdfReaderApiHeaders.FILE_TYPE) String fileType,
            @ApiParam(value = PdfReaderApiHeaders.TARGET_FIELD, required = true) @RequestHeader(value = PdfReaderApiHeaders.TARGET_FIELD) String targetField
    ) throws PdfReaderException {
        LOG.info("START >> PdfFieldIdentificationController >> getFieldIdentificationStrategy >> PAN >> " + pan + " >> FILE_TYPE >> " + fileType);
        if (StringUtils.isBlank(pan)
                || StringUtils.isBlank(fileType)
                || StringUtils.isBlank(targetField)) {
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_DATA);
        }

        if (!fileType.equals(PdfFileType.SHIPPING_BILL.name())
                && !fileType.equals(PdfFileType.BILL_OF_ENTRY.name())) {
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.INVALID_DATA);
        }

        Map<String, Object> responseMap = fieldIdentificationStrategyHandler.deleteFieldIdentificationStrategies(pan, fileType, targetField);
        LOG.info("END >> PdfFieldIdentificationController >> getFieldIdentificationStrategy >> PAN >> " + pan + " >> FILE_TYPE >> " + fileType);
        return new ResponseEntity<>(responseMap, HttpStatus.OK);
    }

}
