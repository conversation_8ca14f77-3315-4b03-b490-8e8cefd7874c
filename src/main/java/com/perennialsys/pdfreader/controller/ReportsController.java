package com.perennialsys.pdfreader.controller;

import com.perennialsys.pdfreader.constants.PdfReaderApiHeaders;
import com.perennialsys.pdfreader.constants.PdfReaderApiParams;
import com.perennialsys.pdfreader.constants.PdfReaderApiUrls;
import com.perennialsys.pdfreader.constants.ResponseCode;
import com.perennialsys.pdfreader.constants.ResponseMessage;
import com.perennialsys.pdfreader.dto.ExportRequestDetailsDto;
import com.perennialsys.pdfreader.enums.ExportFormats;
import com.perennialsys.pdfreader.enums.PdfFileType;
import com.perennialsys.pdfreader.enums.SearchAndSorKeyEnum;
import com.perennialsys.pdfreader.exception.PdfReaderException;
import com.perennialsys.pdfreader.handler.IReportsHandler;
import com.perennialsys.pdfreader.helper.ResponseHelper;
import com.perennialsys.pdfreader.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiParam;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.validation.constraints.Pattern;
import java.io.File;
import java.util.Map;

@EnableSwagger2
@Api(value = PdfReaderApiUrls.REPORTS, description = "Reports Related Operations")
@RestController
@RequestMapping(value = PdfReaderApiUrls.REPORTS)
public class ReportsController {

    private final Logger LOG = Logger.getLogger(ReportsController.class);

    private final IReportsHandler reportsHandler;

    @Autowired
    ReportsController(IReportsHandler reportsHandler) {
        this.reportsHandler = reportsHandler;
    }

    //This API will be used for sync report generation and get the report based on the report generation ID/file ID
    @PutMapping(value = PdfReaderApiUrls.EXPORT_REPORTS, produces =
            {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true,
                allowableValues = "EXPORT_REPORT, ADVANCE_EXPORT", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email", paramType = "header",
                dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                paramType = "header", dataType = "string")
    })
    public ResponseEntity<?> exportReports(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true)
                @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.TXN_ID)
                @RequestHeader(value = PdfReaderApiHeaders.TXN_ID, required = false) String txnId,
            @ApiParam(value = PdfReaderApiHeaders.REPORT_GENERATION_ID)
                @RequestHeader(value = PdfReaderApiHeaders.REPORT_GENERATION_ID, required = false) String reportGenId,
            @ApiParam(value = PdfReaderApiHeaders.START_PERIOD)
                @RequestHeader(value = PdfReaderApiHeaders.START_PERIOD, required = false) String startPeriod,
            @ApiParam(value = PdfReaderApiHeaders.END_PERIOD)
                @RequestHeader(value = PdfReaderApiHeaders.END_PERIOD, required = false) String endPeriod,
            @ApiParam(value = PdfReaderApiHeaders.REPORT_TYPE, required = true, allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL")
                @RequestHeader(value = PdfReaderApiHeaders.REPORT_TYPE) String reportType,
            @ApiParam(value = PdfReaderApiHeaders.SHOULD_RETURN_FILE, required = true)
                @RequestHeader(value = PdfReaderApiHeaders.SHOULD_RETURN_FILE, defaultValue = "false") boolean shouldReturnFile,
            @ApiParam(value = PdfReaderApiParams.EXPORT_FORMAT, required = true,
                    allowableValues = "DEFAULT, DUTY_DRAWBACK, MOOWR", defaultValue = "DEFAULT")
                @RequestHeader(value = PdfReaderApiParams.EXPORT_FORMAT, defaultValue = "DEFAULT") String exportFormat,
            @RequestBody(required = false) ExportRequestDetailsDto requestDetails
    ) throws PdfReaderException {

        LOG.info("START >> ReportsController >> exportReports >> PAN " + pan + " >> TXN_ID >> " + txnId +
            " >> REPORT_TYPE >> " + reportType + " >> EXPORT_FORMAT >> " + exportFormat);

        if (StringUtils.isBlank(pan)
            || ((null == requestDetails || null == requestDetails.getFileIdList()
                    || requestDetails.getFileIdList().isEmpty())
                && StringUtils.isBlank(txnId)
                && StringUtils.isBlank(reportGenId)
                && StringUtils.isBlank(startPeriod)
                && StringUtils.isBlank(endPeriod))
            || StringUtils.isBlank(reportType)
            || StringUtils.isBlank(exportFormat)) {
            LOG.error("ERROR >> ReportsController >> exportReports >> PAN " + pan + " >> TXN_ID >> " + txnId +
                " >> REPORT_TYPE >> " + reportType + " >> EXPORT_FORMAT >> " + exportFormat + " >> Missing Mandatory Headers");
            throw new PdfReaderException(ResponseCode.MISSING_MANDATORY_HEADERS, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        PdfFileType.validateExportFileType(reportType);
        ExportFormats.validateExportFormat(reportType, exportFormat);
        File file;
        exportFormat = ExportFormats.getExportFileType(reportType, exportFormat);

        if(null != requestDetails && null != requestDetails.getFileIdList() && !requestDetails.getFileIdList().isEmpty()) {
            file = reportsHandler.exportReportForFileTxn(pan, null, startPeriod, endPeriod, shouldReturnFile, requestDetails.getFileIdList(), requestDetails.getDescription(), exportFormat, reportType
            );
        }else if(StringUtils.isNotBlank(txnId)) {
            file = reportsHandler.exportReportForTxnId(pan, txnId, reportType, shouldReturnFile,
                requestDetails, exportFormat);
        }else {
            file = reportsHandler.exportReport(pan, reportGenId, reportType, startPeriod, endPeriod,
                    shouldReturnFile, requestDetails, exportFormat);
        }

        LOG.info("END >> ReportsController >> exportReports >> PAN " + pan + " >> TXN_ID >> " + txnId +
            " >> REPORT_TYPE >> " + reportType + " >> EXPORT_FORMAT >> " + exportFormat);

        return ResponseHelper.handleFileResponse(file, shouldReturnFile);
    }

    //This API will be used for async report generation
    @PutMapping(value = PdfReaderApiUrls.GENERATE_REPORTS, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
        @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true,
            allowableValues = "EXPORT_REPORT, ADVANCE_EXPORT", paramType = "query", dataType = "string"),
        @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email",
            paramType = "header", dataType = "string")
            ,
        @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
            paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> startReportsGeneration(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true)
            @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.TXN_ID)
            @RequestHeader(value = PdfReaderApiHeaders.TXN_ID, required = false) String txnId,
            @ApiParam(value = PdfReaderApiHeaders.START_PERIOD)
            @RequestHeader(value = PdfReaderApiHeaders.START_PERIOD, required = false) String startPeriod,
            @ApiParam(value = PdfReaderApiHeaders.END_PERIOD)
            @RequestHeader(value = PdfReaderApiHeaders.END_PERIOD, required = false) String endPeriod,
            @ApiParam(value = PdfReaderApiHeaders.REPORT_TYPE, required = true, allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL")
            @RequestHeader(value = PdfReaderApiHeaders.REPORT_TYPE) String reportType,
            @ApiParam(value = PdfReaderApiParams.EXPORT_FORMAT, required = true)
            @RequestHeader(value = PdfReaderApiParams.EXPORT_FORMAT, defaultValue = "DEFAULT") String exportFormat,
            @RequestBody(required = false) ExportRequestDetailsDto requestDetails
    ) throws PdfReaderException {

        LOG.info("START >> ReportsController >> startReportsGeneration >> PAN " + pan + " >> TXN_ID >> " + txnId +
                " >> REPORT_TYPE >> " + reportType + " >> EXPORT_FORMAT >> " + exportFormat);

        if (StringUtils.isBlank(pan) ||
            ((null == requestDetails || null == requestDetails.getFileIdList() || requestDetails.getFileIdList().isEmpty()) &&
                    (StringUtils.isBlank(txnId) && StringUtils.isBlank(startPeriod) && StringUtils.isBlank(endPeriod))) ||
            StringUtils.isBlank(reportType) || StringUtils.isBlank(exportFormat)) {
            LOG.error("ERROR >> ReportsController >> startReportsGeneration >> PAN " + pan + " >> TXN_ID >> " + txnId +
                    " >> REPORT_TYPE >> " + reportType + " >> EXPORT_FORMAT >> " + exportFormat + " >> Missing Mandatory Headers");
            throw new PdfReaderException(ResponseCode.MISSING_MANDATORY_HEADERS, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        PdfFileType.validateExportFileType(reportType);
        ExportFormats.validateExportFormat(reportType, exportFormat);
        Map<String,Object> result = null;
        exportFormat = ExportFormats.getExportFileType(reportType, exportFormat);

        if((null != requestDetails && null != requestDetails.getFileIdList() && !requestDetails.getFileIdList().isEmpty()) ||
                (StringUtils.isNotBlank(startPeriod) && StringUtils.isNotBlank(endPeriod))) {
            result = reportsHandler.generateReportForFileTxnOrPeriod(pan, reportType, startPeriod, endPeriod, requestDetails, exportFormat);
        }else if(StringUtils.isNotBlank(txnId)) {
            result = reportsHandler.generateReportForTxnId(pan, txnId, reportType, requestDetails, exportFormat);
        }

        LOG.info("END >> ReportsController >> startReportsGeneration >> PAN " + pan + " >> TXN_ID >> " + txnId +
                " >> REPORT_TYPE >> " + reportType + " >> EXPORT_FORMAT >> " + exportFormat);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping(value = PdfReaderApiUrls.EXPORT_REPORTS_HISTORY, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "GET_FILE_EXPORT_HISTORY",
                paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email", paramType = "header",
                dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> exportReportsHistory(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true)
            @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.REPORT_TYPE, required = true, allowableValues = "BILL_OF_ENTRY, SHIPPING_BILL")
            @RequestHeader(value = PdfReaderApiHeaders.REPORT_TYPE) String reportType,
            @ApiParam(defaultValue = "1") @RequestParam(value = PdfReaderApiParams.PAGE_NO, required = false) int pageNo,
            @ApiParam(defaultValue = "5") @RequestParam(value = PdfReaderApiParams.LIMIT, required = false) int limit,
            @ApiParam(value = PdfReaderApiHeaders.SEARCH_KEY,
                allowableValues = "export-time, exported-by, remark")
                @RequestHeader(value = PdfReaderApiHeaders.SEARCH_KEY, required = false) String searchKey,
            @ApiParam(value = PdfReaderApiHeaders.SEARCH_VALUE)
                @RequestHeader(value = PdfReaderApiHeaders.SEARCH_VALUE, required = false) String searchValue,
            @ApiParam(value = PdfReaderApiHeaders.SORT_BY,
                allowableValues = "export-time, exported-by, remark")
                @RequestHeader(value = PdfReaderApiHeaders.SORT_BY, required = false) String sortBy,
            @ApiParam(value = PdfReaderApiHeaders.SORTING_ORDER,
                allowableValues = "1, -1")
                @RequestHeader(value = PdfReaderApiHeaders.SORTING_ORDER, required = false, defaultValue = "-1")
            @Pattern(regexp = "1|-1", message = ResponseMessage.INVALID_SORTING_ORDER) short sortingOrder
    ) throws PdfReaderException {

        LOG.info("START >> ReportsController >> exportReportsHistory >> PAN " + pan +" >> REPORT_TYPE >> " + reportType);

        if (StringUtils.isBlank(pan) || StringUtils.isBlank(reportType)) {
            LOG.error("ERROR >> ReportsController >> exportReportsHistory >> PAN " + pan +
                    " >> REPORT_TYPE >> " + reportType + " >> Missing Mandatory Headers");
            throw new PdfReaderException(ResponseCode.MISSING_MANDATORY_HEADERS, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        Map<String, Object> result = null;
        PdfFileType.validateExportFileType(reportType);
        if(StringUtils.isBlank(searchValue) || SearchAndSorKeyEnum.validateKey(searchKey, "EXPORT_HISTORY")) {
            pageNo = pageNo <= 0 ? 0 : pageNo - 1;
            limit = limit <= 0 ? 5 : limit;

            result = reportsHandler.getExportReportHistory(pan, reportType, pageNo, limit, searchKey, searchValue,
                    sortBy, sortingOrder);
        }

        LOG.info("END >> ReportsController >> exportReportsHistory >> PAN " + pan +" >> REPORT_TYPE >> " + reportType);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PutMapping(value = PdfReaderApiUrls.EDIT_REMARK, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = PdfReaderApiParams.ACTION, required = true, value = "GET_FILE_EXPORT_HISTORY",
                    paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.EMAIL, required = true, value = "Email", paramType = "header",
                    dataType = "string"),
            @ApiImplicitParam(name = PdfReaderApiHeaders.SESSION_TOKEN, required = true, value = "SessionToken",
                    paramType = "header", dataType = "string")
    })
    public ResponseEntity<Map<String, Object>> editExportRemark(
            @ApiParam(value = PdfReaderApiHeaders.PAN, required = true)
            @RequestHeader(value = PdfReaderApiHeaders.PAN) String pan,
            @ApiParam(value = PdfReaderApiHeaders.REPORT_GENERATION_ID)
            @RequestHeader(value = PdfReaderApiHeaders.REPORT_GENERATION_ID, required = false) String reportGenId,
            @RequestBody(required = false) ExportRequestDetailsDto requestDetails
    ) throws PdfReaderException {

        LOG.info("START >> ReportsController >> editExportRemark >> PAN " + pan +" >> REPORT_GENERATION_ID >> " + reportGenId);

        if (StringUtils.isBlank(pan) || StringUtils.isBlank(reportGenId)) {
            LOG.error("ERROR >> ReportsController >> editExportRemark >> PAN " + pan +
                    " >> REPORT_GENERATION_ID >> " + reportGenId + " >> Missing Mandatory Headers");
            throw new PdfReaderException(ResponseCode.MISSING_MANDATORY_HEADERS, ResponseMessage.MISSING_MANDATORY_HEADERS);
        }

        /*if(null ==  requestDetails || StringUtils.isBlank(requestDetails.getDescription())){
            LOG.error("ERROR >> ReportsController >> editExportRemark >> PAN " + pan +
                    " >> REPORT_GENERATION_ID >> " + reportGenId + " >> Missing remark in the body.");
            throw new PdfReaderException(ResponseCode.INVALID_DATA, ResponseMessage.MISSING_REMARK);
        }*/

        Map<String, Object> result = reportsHandler.editExportRemark(pan, reportGenId, requestDetails);

        LOG.info("END >> ReportsController >> editExportRemark >> PAN " + pan +" >> REPORT_GENERATION_ID >> " + reportGenId);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
