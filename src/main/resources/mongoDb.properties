#Localhost Config
mongo_host=localhost
mongo_port=27017
mongo_username=root
mongo_password=root
mongo_database=pdf_reader
mongo_host_url=mongodb://${mongo_username}:${mongo_password}@${mongo_host}:${mongo_port}/${mongo_database}
#mongo_host_url=mongodb://${mongo_host}:${mongo_port}/${mongo_database}

##QA Config
#mongo_host=localhost
#mongo_port=27017
#mongo_username=gsthero_qa_mongo_user
#mongo_password=perennialQA123
#mongo_database=pdf_reader_qa
#mongo_host_url=mongodb://${mongo_username}:${mongo_password}@${mongo_host}:${mongo_port}/${mongo_database}

##Production Config
#mongo_host=*************
#mongo_port=27017
#mongo_username=gsthero_db_user_prod
#mongo_password=G5THeR0m0nG0
#mongo_database=pdf_reader_prod
#mongo_host_url=mongodb://${mongo_username}:${mongo_password}@${mongo_host}:${mongo_port}/${mongo_database}

##Connection limit
mongo_min_connectionsper_host=10
mongo_max_connectionsper_host=30
mongo_max_connection_idle_time=600000
##for dev environment value will be false else true,
is_authenticate=false