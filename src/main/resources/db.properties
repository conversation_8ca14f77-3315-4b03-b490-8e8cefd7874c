#DEV Db Credentials
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.database.name=pdf_reader
spring.datasource.url=***************************************************

#QA Db Credentials
#spring.datasource.username=root
#spring.datasource.password=G$t@952952
#spring.datasource.database.name=pdf_reader_qa

# Temp QA Db Credentials
#spring.datasource.username=qa_app_user
#spring.datasource.password=G$t@952952
#spring.datasource.database.name=pdf_reader_qa
#spring.datasource.url=****************************************************************************************************

spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=10
spring.datasource.hikari.idleTimeout=40000
spring.datasource.hikari.pool-name=pdf-reader-service-pool
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=70000
spring.datasource.hikari.leak-detection-threshold=240000

#Hibernate configuration
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.connection.driver_class=com.mysql.jdbc.Driver
hibernate.connection.verifyServerCertificate=true
hibernate.connection.useSSL=false
hibernate.connection.requireSSL=false
hibernate.format_sql=false
hibernate.show_sql=false
hibernate.hbm2ddl.auto=update
hibernate.jdbc.batch_size=50
hibernate.allow_update_outside_transaction=true
hibernate.current_session_context_class=thread
hibernate.cache.use_second_level_cache=false
hibernate.cache.use_query_cache=false
hibernate.cache.region.factory_class=org.hibernate.cache.ehcache.EhCacheRegionFactory
hibernate.connection.isolation=2
hibernate.c3p0.min_size=5
hibernate.c3p0.max_size=10
hibernate.c3p0.timeout=3300
hibernate.c3p0.max_statements=50
hibernate.c3p0.idle_test_period=3000
hibernate.c3p0.validate=true
hibernate.c3p0.acquireRetryAttempts=1
hibernate.c3p0.breakOnAcquireFailure=true
hibernate.c3p0.preferredTestQuery=SELECT 1
hibernate.connection.provider_class=org.hibernate.service.jdbc.connections.internal.C3P0ConnectionProvider
#net.sf.cache.ehcache.configurationResourceName = /ehcache-failsafe.xml -->