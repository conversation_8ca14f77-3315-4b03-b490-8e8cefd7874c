# Adaptor setting to use storage account
#Storage_Account_To_Use=Azure Storage
Storage_Account_To_Use=AWS S3
#Storage_Account_To_Use=LOCAL
#Azure blob storage settings
dev.asp.gsp.azure.blobcontainer.name=devgspstorage
dev.asp.gsp.azure.account.key=GZEy5eBFrktnfKPUKWa7yTb/EY65Z/Q6Yi5Dh9jkW0LF5oHYSnJMs12RmYWa3UPwuaW2qUKr3Z52fGpKCcx8jw==
qa.asp.gsp.azure.blobcontainer.name=qagspstorage
qa.asp.gsp.azure.account.key=YP15xPEPnRyZ7JiAO+aYBB/rGfzGBtv8Qs86LqGRdLWDlhBJUPGfokCIxfA+XiGVoBQ0rE4Tdn8y7jVNA3ENtA==
prod.asp.gsp.azure.blobcontainer.name=prodgspstorage
prod.asp.gsp.azure.account.key=GdOjd8mJNYRvKv1GYoncBhMhvNaTNR57HiLgi6J3nJ/He/jHC8iq5yuhC0roY5hquOx3/lq/zxLRQk+gKcc+XA==
#AWS s3 storage settings
# IAM user credentials
s3.aws_access_key_id=********************
s3.aws_secret_access_key=UjqIrVAI5zsbrIMonyYL8DKPo2tbryoOKSNeBAFt
# AWS S3 bucket names
dev.asp.gsp.aws.s3.bucket.name=devgspstorage
qa.asp.gsp.aws.s3.bucket.name=qagspstorage
prod.asp.gsp.aws.s3.bucket.name=prodgspstorage
gsp.aws.s3.upload.folder.name=uploads
gsp.aws.s3.root.folder.name=pdfReader
remove.invalid.data.files=true
dev.logger.path=/home/<USER>/PdfReader/logs/
qa.logger.path=/home/<USER>/PdfReader/logs/
prod.logger.path=/home/<USER>/PdfReader/logs/
dev.resources.root.path=/home/<USER>/PdfReader/
dev.resources.upload.folder=uploads
dev.resources.export.folder=exports

qa.resources.root.path=/home/<USER>/PdfReader/
qa.resources.upload.folder=uploads
qa.resources.export.folder=exports

prod.resources.root.path=/home/<USER>/PdfReader/
prod.resources.upload.folder=uploads
prod.resources.export.folder=exports
max.allowed.parsing.files=5
max.allowed.processing.export.files=5
enable.swagger=true

sftp.retry.count=5