# Root logger option
log4j.rootLogger=DEBUG, stdout,  file , error
# Redirect log messages to console
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p [ PDFReader ] %c{1}:%L - %m%n
# Redirect log messages to a log file
log4j.appender.file=org.apache.log4j.RollingFileAppender
#outputs to Tomcat homekreditap
log4j.appender.file.File=/home/<USER>/logs/pdf_reader/application.log
#log4j.appender.error.append=true
#log4j.appender.error.Threshold=DEBUG
log4j.appender.file.MaxFileSize=10MB
log4j.appender.file.MaxBackupIndex=50
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p [ PDFReader ] %c{1}:%L - %m%n
# Redirect error log messages to a log file
log4j.appender.error=org.apache.log4j.RollingFileAppender
log4j.appender.error.File=/home/<USER>/logs/pdf_reader/error.log
log4j.appender.error.append=true
log4j.appender.error.Threshold=ERROR
log4j.appender.error.MaxFileSize=10MB
log4j.appender.error.MaxBackupIndex=50
log4j.appender.error.layout=org.apache.log4j.PatternLayout
log4j.appender.error.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p [ PDFReader ] %c{1}:%L - %m%n
log4j.logger.org.hibernate=ERROR
log4j.logger.org.hibernate.SQL=ERROR
log4j.logger.org.hibernate.type=ERROR
log4j.logger.org.hibernate.hql.ast.AST=info
log4j.logger.org.hibernate.tool.hbm2ddl=warn
log4j.logger.org.hibernate.hql=ERROR
log4j.logger.org.hibernate.cache=info
log4j.logger.org.hibernate.jdbc=ERROR
log4j.logger.httpclient.wire.header=WARN
log4j.logger.httpclient.wire.content=WARN
log4j.logger.org.apache.http=warn
log4j.logger.com.mchange.v2=ERROR
log4j.logger.org.springframework.data.mongodb.core.MongoTemplate=INFO
log4j.logger.org.springframework.data.mongodb.core.MongoDbUtils=INFO
# End here

# This config is done for the PDF Parsing Job scheduler
log4j.logger.com.perennialsys.pdfreader.scheduler.PdfParsingJobScheduler=INFO, PdfParsingJobScheduler
log4j.additivity.com.perennialsys.pdfreader.scheduler.PdfParsingJobScheduler=false
log4j.appender.PdfParsingJobScheduler=org.apache.log4j.RollingFileAppender
log4j.appender.PdfParsingJobScheduler.File=/home/<USER>/logs/pdf_reader/PdfParsingJobScheduler.log
log4j.appender.PdfParsingJobScheduler.layout=org.apache.log4j.PatternLayout
log4j.appender.PdfParsingJobScheduler.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p [ PDFReader ] %c{1}:%L - %m%n
# End here

# This config is done for the SFTP Job scheduler
log4j.logger.com.perennialsys.pdfreader.scheduler.SftpJobScheduler=INFO, SftpJobScheduler
log4j.additivity.com.perennialsys.pdfreader.scheduler.SftpJobScheduler=false
log4j.appender.SftpJobScheduler=org.apache.log4j.RollingFileAppender
log4j.appender.SftpJobScheduler.File=/home/<USER>/logs/pdf_reader/SftpJobScheduler.log
log4j.appender.SftpJobScheduler.layout=org.apache.log4j.PatternLayout
log4j.appender.SftpJobScheduler.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p [ PDFReader ] %c{1}:%L - %m%n
# End here




