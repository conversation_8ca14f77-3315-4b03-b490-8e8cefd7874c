server.servlet.context-path=/pdf-reader
# create and drop tables and sequences, loads import.sql
spring.jpa.hibernate.ddl-auto=none
# logging
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n
logging.level.org.hibernate.SQL=debug
# app level properties
sample.publickey=sample_publickey.txt
server.port=8082
max.record.upsert.batch.size=1000
#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB