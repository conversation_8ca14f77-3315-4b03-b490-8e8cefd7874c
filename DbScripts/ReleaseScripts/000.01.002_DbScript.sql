-- #################################################################
-- ### Server version: 			    1.0
-- ### Date (DD-MM-YYYY):           19/06/2023
-- ### Developer Name: 		        Mrunal Nagare
-- ### Comments:                    DB Script for SFTP integration
-- #################################################################

-- Create table to store the SFTP details
drop table if exists sftp_details;
create table sftp_details
(
    ID            bigint(20) primary key auto_increment,
    PAN           varchar(15)  not null,
    USR_ID        varchar(100),
    HOST_IP       varchar(20)  not null,
    USER_NAME     varchar(20),
    PASSWORD      varchar(100),
    IS_ACTIVE     bit      default 1,
    AUTH_TYPE     varchar(10)  not null,
    PPK_FILE_PATH varchar(100),
    PORT_NUMBER   int,
    CREATED_AT    datetime default now(),
    UPDATED_AT    datetime
);

create index pan_index on sftp_details (PAN);

-- Create table to store the SFTP Operations details
drop table if exists sftp_operation_audit_log;
create table sftp_operation_audit_log
(
    ID            bigint(20) primary key auto_increment,
    PAN           varchar(15)  not null,
    TXN_ID        varchar(255) ,
    HOST_IP       varchar(20)  not null,
    OPERATION     varchar(50)  not null,
    STATUS        varchar(50),
    OTHER_DETAILS mediumtext,
    RETRY_COUNT   INT      default 0,
    CREATED_AT    datetime default now(),
    UPDATED_AT    datetime
);

create index pan_index on sftp_operation_audit_log (PAN);
create index txn_index on sftp_operation_audit_log (TXN_ID);
create index operation_index on sftp_operation_audit_log (OPERATION);

-- Create table to store the SFTP Operations details
drop table if exists sftp_file_path_mapping;
create table sftp_file_path_mapping
(
    ID            bigint(20) primary key auto_increment,
    SFTP_ID       bigint(20) not null ,
    FILE_TYPE     varchar(50) not null ,
    IN_FILE_PATH  varchar(255) not null ,
    OUT_FILE_PATH varchar(255) not null ,
    CREATED_AT    datetime default now(),
    UPDATED_AT    datetime,
    CONSTRAINT sftp_details_fk FOREIGN KEY (SFTP_ID) references sftp_details (ID)
);