-- #################################################################
-- ### Server version: 			    1.0
-- ### Date (DD-MM-YYYY):           16/06/2023
-- ### Developer Name: 		        Mr<PERSON>l <PERSON>e
-- ### Comments:                    <PERSON> to add the IGCRD BOND related fields in BOE PDF parsing
-- #################################################################


INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.BOND NO.', 'bondNo', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.PORT', 'bondPort', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.BOND CD', 'bondCode', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.DEBT AMT', 'bondDebtAmt', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.BG AMT', 'bondBgAmt', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';

-- Update entries From the export report template mapping for BOE to add the Bond related fields

-- Delete the existing entries from the template mapping table for BILL_OF_ENTRY
delete from export_report_template_mapping where MAPPING_ENTITY = 'BILL_OF_ENTRY';

-- Create new Entries for BILL_OF_ENTRY PDF
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'BILL_OF_ENTRY', 'sr_no', -2, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item Code', 'Item Code', 'BILL_OF_ENTRY', 'item_code', -2, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Description & Technical Characteristics', 'Description & Technical Characteristics', 'BILL_OF_ENTRY', 'desc_technical_characteristics', -2, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'BILL_OF_ENTRY', 'bill_of_entry_no', -2, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry Date', 'Bill of Entry Date', 'BILL_OF_ENTRY', 'bill_of_entry_date', -2, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name of Customs House', 'Name of Customs House', 'BILL_OF_ENTRY', 'name_of_customs_house', -2, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('HSN', 'HSN', 'BILL_OF_ENTRY', 'hsn', -2, 7);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Qty. Imported', 'Qty. Imported', 'BILL_OF_ENTRY', 'imported_qty', -2, 8);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Unit of Measurement', 'Unit of Measurement', 'BILL_OF_ENTRY', 'uqc', -2, 9);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Assessable Value as per BOE(Rs.)', 'Assessable Value as per BOE(Rs.)', 'BILL_OF_ENTRY', 'assessable_value_as_per_boe', -2, 10);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BCD rate', 'BCD rate', 'BILL_OF_ENTRY', 'bcd_rate', -2, 11);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Customs Cess rate', 'Customs Cess rate', 'BILL_OF_ENTRY', 'customs_cess_rate', -2, 12);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Country from where imported', 'Country from where imported', 'BILL_OF_ENTRY', 'imported_country_from', -2, 13);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name of supplier', 'Name of supplier', 'BILL_OF_ENTRY', 'supplier_name', -2, 14);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name & full address of the supplier in case the foreign materials/ components obtained locally', 'Name & full address of the supplier in case the foreign materials/ components obtained locally', 'BILL_OF_ENTRY', 'foreign_material_details', -2, 15);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('supplierAddress', 'supplierAddress', 'BILL_OF_ENTRY', 'supplier_address', -2, 16);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('invNo', 'invNo', 'BILL_OF_ENTRY', 'inv_no', -2, 17);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('invDate', 'invDate', 'BILL_OF_ENTRY', 'inv_dt', -2, 18);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('igmNo', 'igmNo', 'BILL_OF_ENTRY', 'igm_no', -2, 19);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('hss', 'hss', 'BILL_OF_ENTRY', 'hss', -2, 20);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('provFinal', 'provFinal', 'BILL_OF_ENTRY', 'prov_final', -2, 21);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('portOfLoading', 'portOfLoading', 'BILL_OF_ENTRY', 'port_of_loading', -2, 22);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('igmDate', 'igmDate', 'BILL_OF_ENTRY', 'igm_date', -2, 23);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('mode', 'mode', 'BILL_OF_ENTRY', 'mode', -2, 24);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('inwDate', 'inwDate', 'BILL_OF_ENTRY', 'inw_date', -2, 25);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('sec48', 'sec48', 'BILL_OF_ENTRY', 'sec_48', -2, 26);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('reImp', 'reImp', 'BILL_OF_ENTRY', 're_imp', -2, 27);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('adCode', 'adCode', 'BILL_OF_ENTRY', 'ad_code', -2, 28);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('beType', 'beType', 'BILL_OF_ENTRY', 'be_type', -2, 29);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('exchangeRate', 'exchangeRate', 'BILL_OF_ENTRY', 'exchange_rate', -2, 30);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('gstin', 'gstin', 'BILL_OF_ENTRY', 'gstin', -2, 31);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('iec', 'iec', 'BILL_OF_ENTRY', 'iec', -2, 32);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('portCode', 'portCode', 'BILL_OF_ENTRY', 'port_code', -2, 33);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('bcdAmount', 'bcdAmount', 'BILL_OF_ENTRY', 'bcd_amount', -2, 34);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('unitPrice', 'unitPrice', 'BILL_OF_ENTRY', 'unit_price', -2, 35);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('acdRate', 'acdRate', 'BILL_OF_ENTRY', 'acd_rate', -2, 36);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('acdAmount', 'acdAmount', 'BILL_OF_ENTRY', 'acd_amount', -2, 37);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('swsRate', 'swsRate', 'BILL_OF_ENTRY', 'sws_rate', -2, 38);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('swsAmount', 'swsAmount', 'BILL_OF_ENTRY', 'sws_amount', -2, 39);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('totalDuty', 'totalDuty', 'BILL_OF_ENTRY', 'total_duty', -2, 40);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('sadRate', 'sadRate', 'BILL_OF_ENTRY', 'sad_rate', -2, 41);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('sadAmount', 'sadAmount', 'BILL_OF_ENTRY', 'sad_amount', -2, 42);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('igstRate', 'igstRate', 'BILL_OF_ENTRY', 'igst_rate', -2, 43);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('igstAmount', 'igstAmount', 'BILL_OF_ENTRY', 'igst_amount', -2, 44);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('cessAmount', 'cessAmount', 'BILL_OF_ENTRY', 'cess_amount', -2, 45);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('addRate', 'addRate', 'BILL_OF_ENTRY', 'add_rate', -2, 46);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('addAmount', 'addAmount', 'BILL_OF_ENTRY', 'add_amount', -2, 47);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('cvdRate', 'cvdRate', 'BILL_OF_ENTRY', 'cvd_rate', -2, 48);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('cvdAmount', 'cvdAmount', 'BILL_OF_ENTRY', 'cvd_amount', -2, 49);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('invValue', 'invValue', 'BILL_OF_ENTRY', 'inv_value', -2, 50);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('invCurrency', 'invCurrency', 'BILL_OF_ENTRY', 'inv_currency', -2, 51);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('term', 'term', 'BILL_OF_ENTRY', 'term', -2, 52);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('freight', 'freight', 'BILL_OF_ENTRY', 'freight', -2, 53);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('itemValue', 'itemValue', 'BILL_OF_ENTRY', 'item_value', -2, 54);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('oocDate', 'oocDate', 'BILL_OF_ENTRY', 'ooc_date', -2, 55);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('oocNo', 'oocNo', 'BILL_OF_ENTRY', 'ooc_no', -2, 56);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bond no', 'Bond no', 'BILL_OF_ENTRY', 'bond_no', -2, 57);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bond CD', 'Bond CD', 'BILL_OF_ENTRY', 'bond_code', -2, 58);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Debt Amt', 'Debt Amt', 'BILL_OF_ENTRY', 'bond_debt_amt', -2, 59);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BG Amt', 'BG Amt', 'BILL_OF_ENTRY', 'bond_bg_amt', -2, 60);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Port', 'Port', 'BILL_OF_ENTRY', 'bond_port', -2, 61);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Custom 1', 'Custom 1', 'BILL_OF_ENTRY', 'custom_1', -2, 62);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Custom 2', 'Custom 2', 'BILL_OF_ENTRY', 'custom_2', -2, 63);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Custom 3', 'Custom 3', 'BILL_OF_ENTRY', 'custom_3', -2, 64);