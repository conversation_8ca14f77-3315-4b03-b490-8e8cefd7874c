-- #################################################################
-- ### Server version: 			    1.0
-- ### Date (DD-MM-YYYY):           03/07/2023
-- ### Developer Name: 		        Mr<PERSON><PERSON> Nagare
-- ### Comments:                    DB Script Field identification functionality integration
-- #################################################################

-- Create table to store the field identification strategies

CREATE TABLE field_identification_strategy
(
    ID            BIGINT(20) PRIMARY KEY AUTO_INCREMENT,
    PAN           VARCHAR(15)  NOT NULL,
    FILE_TYPE     VARCHAR(50)  NOT NULL,
    TARGET_FIELD  VARCHAR(100) NOT NULL,
    SOURCE_FIELD  VARCHAR(100) NOT NULL,
    STRATEGY      VARCHAR(50)  NOT NULL,
    TERMINATION   VARCHAR(50) DEFAULT 'SPACE',
    PREFIX_SUFFIX VARCHAR(50) DEFAULT 'SPACE',
    LENGTH        INT         DEFAULT 0,
    SPECIAL_CHAR  VARCHAR(10),
    IS_ACTIVE     BIT         DEFAULT true,
    CREATED_AT    DATETIME    DEFAULT NOW(),
    UPDATED_AT    DATETIME,
    CONSTRAINT UNIQUE (PAN, FILE_TYPE, TARGET_FIELD)
);

CREATE INDEX pan_index ON field_identification_strategy (PAN);
CREATE INDEX file_type_index ON field_identification_strategy (FILE_TYPE);