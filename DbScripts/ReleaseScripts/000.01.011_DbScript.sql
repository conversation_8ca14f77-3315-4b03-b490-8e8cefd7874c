-- #################################################################
-- ### Server version       :       1.0
-- ### Date (DD-MM-YYYY)    :       23/11/2023
-- ### Developer Name       : 		Mrunal Nagare
-- ### Comments             :       DB Script to add user related columns in the required tables
-- #################################################################

alter table export_reports_manager add column USER_ID bigint not null default 0 after PAN;
alter table export_reports_manager add column USER_NAME tinytext not null default '' after USER_ID;

alter table transactions_manager add column USER_ID bigint not null default 0 after PAN;
alter table transactions_manager add column USER_NAME tinytext not null default '' after USER_ID;

alter table file_upload_details add column USER_ID bigint not null default 0 after PAN;
alter table file_upload_details add column USER_NAME tinytext not null default '' after USER_ID;