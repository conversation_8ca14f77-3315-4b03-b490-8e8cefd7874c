-- #################################################################
-- ### Server version       :       1.0
-- ### Date (DD-MM-YYYY)    :       12/12/2023
-- ### Developer Name       : 		Mr<PERSON>l <PERSON>e
-- ### Comments             :       <PERSON> for adding field strategy mapping for remaining fields for BOE and SB PDF parsing
-- #################################################################

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.BE STATUS', 'beStatus', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.DEF BE', 'defBe', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.KACHA', 'kacha', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '14.COUNTRY OF CONSIGNMENT', 'countryOfConsignment', 'bill_of_entry_summary', ID, 0.000, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '16.PORT OF SHIPMENT', 'portOfShipment', 'bill_of_entry_summary', ID, 0.000, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.CB NAME', 'cbName', 'bill_of_entry_summary', ID, 0.000, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.AEO', 'aeo', 'bill_of_entry_summary', ID, 0.000, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.UCR', 'ucr', 'bill_of_entry_summary', ID, 0.000, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.BCD', 'bcd', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.ACD', 'acd', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.SWS', 'sws', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.NCCD', 'nccd', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.ADD', 'add', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.CVD', 'cvd', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.IGST', 'igst', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.G.CESS', 'cess', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '18.TOT.ASS VAL', 'totalAssValue', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.SG', 'sg', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.SAED', 'aed', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '11.GSIA', 'gsia', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '12.TTA', 'tta', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '13.HEALTH', 'health', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '14.TOTAL DUTY', 'dutySummaryTotalDuty', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '15.INT', 'int', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '16.PNLTY', 'penalty', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '17.FINE', 'fine', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '19.TOT. AMOUNT', 'totalAmount', 'bill_of_entry_summary', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.PURCHASE ORDER NO & DT', 'purOrdeNoAndDate', 'invoice_and_valuation_details', ID, 10.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SEPARATED_BY_NEW_LINE_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.LC NO & DATE', 'lcNoAndDate', 'invoice_and_valuation_details', ID, 10.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SEPARATED_BY_NEW_LINE_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.CONTRACT NO & DATE', 'contracNoAndDate', 'invoice_and_valuation_details', ID, 10.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SEPARATED_BY_NEW_LINE_ML';

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.BUYER''S NAME & ADDRESS', 'buyersNameAndAddress', 'invoice_and_valuation_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'NAME_AND_ADDRESS_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.SELLER''S NAME & ADDRESS', 'sellersNameAndAddress', 'invoice_and_valuation_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'NAME_AND_ADDRESS_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.THIRD PARTY NAME & ADDRESS', 'thirdPartyNameAndAddress', 'invoice_and_valuation_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'NAME_AND_ADDRESS_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.AEO', 'transactingPartiesAeo', 'invoice_and_valuation_details', ID, 0, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.AD CODE', 'transactingPartiesAdCode', 'invoice_and_valuation_details', ID, 0, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.INSURANCE', 'insurance', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.HSS.', 'valuationHss', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.LOADING', 'loading', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.COMMN', 'commn', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.PAY TERMS', 'payTerms', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.VALUATION METHOD', 'valTerms', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.RELTD', 'reltd', 'invoice_and_valuation_details', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.SVB CH', 'svbCh', 'invoice_and_valuation_details', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '11.SVB NO', 'svbNo', 'invoice_and_valuation_details', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '12.DATE', 'valuationDate', 'invoice_and_valuation_details', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '13LOA', 'loa', 'invoice_and_valuation_details', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.C&B', 'cb', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.CoC', 'coc', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.CoP', 'cop', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.HND CHG', 'hindChg', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.G&S', 'gs', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.DOC. CH', 'docCh', 'invoice_and_valuation_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.COO', 'coo', 'invoice_and_valuation_details', ID, 3.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.R & LF', 'rLf', 'invoice_and_valuation_details', ID, 3.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.OTH COST', 'othCost', 'invoice_and_valuation_details', ID, 3.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.LD / ULD', 'ldUld', 'invoice_and_valuation_details', ID, 3.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '11.WS', 'ws', 'invoice_and_valuation_details', ID, 3.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '12.OTC', 'otc', 'invoice_and_valuation_details', ID, 3.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '13.MISC CHARGE', 'miscChg', 'invoice_and_valuation_details', ID, 3.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '14.ASS. VALUE', 'assValue', 'invoice_and_valuation_details', ID, 3.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.FS', 'fs', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.PQ', 'pq', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.DC', 'dc', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.WC', 'wc', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.AQ', 'aq', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '11.UPI', 'upi', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '12.COO', 'itemDtlsCoo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '13.C.QTY', 'cQty', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '14.C.UQC', 'cUqc', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '15.S.QTY', 'sQty', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '16.S.UQC', 'sUqc', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '17.SCH', 'sch', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '18.STND/PR', 'stndPr', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '19.RSP', 'rsp', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '20.REIMP', 'reimp', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '21.PROV', 'prov', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '22.END USE', 'endUse', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '23.PRODN', 'prodn', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '24.CNTRL', 'cntrl', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '25.QUALFR', 'qualfr', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '26.CONTNT', 'contnt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '27.STMNT', 'stmnt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '28.SUP DOCS', 'supDocs', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';

insert into pdf_parsing_strategies (strategy, is_field_specific) values ('BOE_DUTY_NOTN_NO_VALUE', 0), ('BOE_DUTY_NOTN_SNO_VALUE', 0), ('BOE_DUTY_FG_VALUE', 0);

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1. BCD', 'notnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1. BCD', 'notnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1. BCD', 'bcdDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.ACD', 'acdDutyNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.ACD', 'acdDutyNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.ACD', 'acdDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.SWS', 'swsDutyNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.SWS', 'swsDutyNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.SWS', 'swsDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.SAD', 'sadDutyNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.SAD', 'sadDutyNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.SAD', 'sadDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.IGST', 'igstNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.IGST', 'igstNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.IGST', 'igstDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.G. CESS', 'cessNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.G. CESS', 'cessNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.G. CESS', 'cessDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.ADD', 'addDutyNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.ADD', 'addDutyNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.ADD', 'addDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CVD', 'cvdDutyNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CVD', 'cvdDutyNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CVD', 'cvdDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.SG', 'sgNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.SG', 'sgNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.SG', 'sgRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.SG', 'sgAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.SG', 'sgDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.SP EXD', 'spExdNotn_no', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.SP EXD', 'spExdNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.SP EXD', 'spExdRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.SP EXD', 'spExdAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.SP EXD', 'spExdDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.CHCESS', 'chCessNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.CHCESS', 'chCessNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.CHCESS', 'chCessRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.CHCESS', 'chCessAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.CHCESS', 'chCessDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.TTA', 'ttaNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.TTA', 'ttaNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.TTA', 'ttaRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.TTA', 'ttaAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.TTA', 'ttaDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.CESS', 'othDutyCessNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.CESS', 'othDutyCessNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.CESS', 'othDutyCessRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.CESS', 'othDutyCessAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.CESS', 'othDutyCessDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.CVD EDC', 'cvdEdcNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.CVD EDC', 'cvdEdcNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.CVD EDC', 'cvdEdcRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.CVD EDC', 'cvdEdcAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.CVD EDC', 'cvdEdcDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.CVD HEC', 'cvdHecNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.CVD HEC', 'cvdHecNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.CVD HEC', 'cvdHecRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.CVD HEC', 'cvdHecAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.CVD HEC', 'cvdHecDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.CUS EDC', 'cusEdcNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.CUS EDC', 'cusEdcNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.CUS EDC', 'cusEdcRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.CUS EDC', 'cusEdcAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.CUS EDC', 'cusEdcDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CUS HEC', 'cusHecNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CUS HEC', 'cusHecNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CUS HEC', 'cusHecRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CUS HEC', 'cusHecAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CUS HEC', 'cusHecDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.NCD', 'ncdNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.NCD', 'ncdNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.NCD', 'ncdRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.NCD', 'ncdAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.NCD', 'ncdDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.AGGR', 'aggrNotnNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_NO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.AGGR', 'aggrNotnSrNo', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_NOTN_SNO_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.AGGR', 'aggrRate', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.AGGR', 'aggrAmt', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.AGGR', 'aggrDutyFg', 'duties', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_FG_VALUE';


INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUE ('VALUE_BESIDE_FIELD_MULTI_COLUMN', 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'Submission', 'billOfEntrySubmission', 'bill_of_entry_summary', ID, 0, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_MULTI_COLUMN';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'Assessment', 'billOfEntryAssessment', 'bill_of_entry_summary', ID, 0, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_MULTI_COLUMN';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'Examination', 'billOfEntryExamination', 'bill_of_entry_summary', ID, 0, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_MULTI_COLUMN';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'OOC', 'billOfEntryOoc', 'bill_of_entry_summary', ID, 0, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_MULTI_COLUMN';

alter table export_reports_manager modify OTHER_DETAILS mediumtext null;