-- #################################################################
-- ### Server version       :       8.0
-- ### Date (DD-MM-YYYY)    :       02/06/2025
-- ### Developer Name       : 		Mr<PERSON><PERSON>
-- ### Comments             :       <PERSON> for adding field strategy mapping for remaining fields for SB PDF parsing
-- #################################################################

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '3.INSURANC', 'sbInsurance', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '4.DISCOU', 'sbDiscount', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '5.COM', 'sbCommission', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '6.DEDUCTIONS', 'sbOtherDeductions', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '3.THIRD PARTY NAME & ADDRESS', 'thirdPartyNameAndAddress', 'invoice_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'NAME_AND_ADDRESS_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '7.DEDUCT', 'otherDeductions', 'invoice_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';


UPDATE pdf_field_strategy_mapping set STRATEGY_ID = (select ID from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SEPARATED_BY_SPACE_SL') where FIELD_NAME = '4.INSURANCE5DISCOUNT6.COMMISON' and FILE_TYPE = 'SHIPPING_BILL';


-- Update entries From the export report template mapping for BOE to add the Bond related fields

-- Delete the existing entries from the template mapping table for BILL_OF_ENTRY
delete from export_report_template_mapping where MAPPING_ENTITY = 'BILL_OF_ENTRY';

-- Create new Entries for BILL_OF_ENTRY PDF
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID,
                                            MAPPING_SEQUENCE)
VALUES ('Sr. No.', 'Sr. No.', 'BILL_OF_ENTRY', 'sr_no', -2, 1),
       ('IEC', 'IEC', 'BILL_OF_ENTRY', 'iec', -2, 2),
       ('GSTIN', 'GSTIN', 'BILL_OF_ENTRY', 'gstin', -2, 3),
       ('GSTIN Type', 'GSTIN Type', 'BILL_OF_ENTRY', 'gstin_type', -2, 4),
       ('Bill of Entry No.', 'Bill of Entry No.', 'BILL_OF_ENTRY', 'bill_of_entry_no', -2, 5),
       ('Bill of Entry Date', 'Bill of Entry Date', 'BILL_OF_ENTRY', 'bill_of_entry_date', -2, 6),
       ('beType', 'beType', 'BILL_OF_ENTRY', 'be_type', -2, 7),
       ('portCode', 'portCode', 'BILL_OF_ENTRY', 'port_code', -2, 8),
       ('Name of Customs House', 'Name of Customs House', 'BILL_OF_ENTRY', 'name_of_customs_house', -2, 9),
       ('No Of Invoices', 'No Of Invoices', 'BILL_OF_ENTRY', 'no_of_invoices', -2, 10),
       ('No Of Items', 'No Of Items', 'BILL_OF_ENTRY', 'no_of_items', -2, 11),
       ('BE Status', 'BE Status', 'BILL_OF_ENTRY', 'be_status', -2, 12),
       ('mode', 'mode', 'BILL_OF_ENTRY', 'mode', -2, 13),
       ('DEF BE', 'DEF BE', 'BILL_OF_ENTRY', 'def_be', -2, 14),
       ('KACHA', 'KACHA', 'BILL_OF_ENTRY', 'kacha', -2, 15),
       ('Sec 48', 'Sec 48', 'BILL_OF_ENTRY', 'sec_48', -2, 16),
       ('Reimport', 'Reimport', 'BILL_OF_ENTRY', 're_imp', -2, 17),
       ('ADV', 'ADV', 'BILL_OF_ENTRY', 'adv', -2, 18),
       ('Assess', 'Assess', 'BILL_OF_ENTRY', 'assess', -2, 19),
       ('EXAM', 'EXAM', 'BILL_OF_ENTRY', 'exam', -2, 20),
       ('Highseas', 'Highseas', 'BILL_OF_ENTRY', 'highseas', -2, 21),
       ('First Check', 'First Check', 'BILL_OF_ENTRY', 'first_check', -2, 22),
       ('Prov / Final', 'Prov / Final', 'BILL_OF_ENTRY', 'prov_final', -2, 23),
       ('Country or origin', 'Country or origin', 'BILL_OF_ENTRY', 'imported_country_from', -2, 24),
       ('Country of consignment', 'Country of consignment', 'BILL_OF_ENTRY', 'country_of_consignment', -2, 25),
       ('Port Of Loading', 'Port Of Loading', 'BILL_OF_ENTRY', 'port_of_loading', -2, 26),
       ('Port Of shipment', 'Port Of shipment', 'BILL_OF_ENTRY', 'port_of_shipment', -2, 27),
       ('Name of Importer', 'Name of Importer', 'BILL_OF_ENTRY', 'importers_name', -2, 28),
       ('Address of Importer', 'Address of Importer', 'BILL_OF_ENTRY', 'importers_address', -2, 29),
       ('AD Code', 'AD Code', 'BILL_OF_ENTRY', 'ad_code', -2, 30),
       ('CB Name', 'CB Name', 'BILL_OF_ENTRY', 'cb_name', -2, 31),
       ('AEO', 'AEO', 'BILL_OF_ENTRY', 'aeo', -2, 32),
       ('UCR', 'UCR', 'BILL_OF_ENTRY', 'ucr', -2, 33),
       ('BCD', 'BCD', 'BILL_OF_ENTRY', 'bcd', -2, 34),
       ('ACD', 'ACD', 'BILL_OF_ENTRY', 'acd', -2, 35),
       ('SWS', 'SWS', 'BILL_OF_ENTRY', 'sws', -2, 36),
       ('NCCD', 'NCCD', 'BILL_OF_ENTRY', 'nccd', -2, 37),
       ('ADD', 'ADD', 'BILL_OF_ENTRY', 'add', -2, 38),
       ('CVD', 'CVD', 'BILL_OF_ENTRY', 'cvd', -2, 39),
       ('IGST', 'IGST', 'BILL_OF_ENTRY', 'igst', -2, 40),
       ('Cess', 'Cess', 'BILL_OF_ENTRY', 'cess', -2, 41),
       ('Total ass value', 'Total ass value', 'BILL_OF_ENTRY', 'total_ass_value', -2, 42),
       ('SG', 'SG', 'BILL_OF_ENTRY', 'sg', -2, 43),
       ('AED', 'AED', 'BILL_OF_ENTRY', 'aed', -2, 44),
       ('GSIA', 'GSIA', 'BILL_OF_ENTRY', 'gsia', -2, 45),
       ('TTA', 'TTA', 'BILL_OF_ENTRY', 'tta', -2, 46),
       ('Health', 'Health', 'BILL_OF_ENTRY', 'health', -2, 47),
       ('Total Duty', 'Total Duty', 'BILL_OF_ENTRY', 'duty_summary_total_duty', -2, 48),
       ('INT', 'INT', 'BILL_OF_ENTRY', 'int', -2, 49),
       ('Penalty', 'Penalty', 'BILL_OF_ENTRY', 'penalty', -2, 50),
       ('Fine', 'Fine', 'BILL_OF_ENTRY', 'fine', -2, 51),
       ('Total amount', 'Total amount', 'BILL_OF_ENTRY', 'total_amount', -2, 52),
       ('IGM No', 'IGM No', 'BILL_OF_ENTRY', 'manifiest_igm_no', -2, 53),
       ('IGM Date', 'IGM Date', 'BILL_OF_ENTRY', 'manifiest_igm_date', -2, 54),
       ('Inward date', 'Inward date', 'BILL_OF_ENTRY', 'manifiest_inw_date', -2, 55),
       ('GIG MNO', 'GIG MNO', 'BILL_OF_ENTRY', 'manifiest_gig_mno', -2, 56),
       ('GIG MDT', 'GIG MDT', 'BILL_OF_ENTRY', 'manifiest_gig_mdt', -2, 57),
       ('MAWB No', 'MAWB No', 'BILL_OF_ENTRY', 'manifiest_mawb_no', -2, 58),
       ('Date', 'Date', 'BILL_OF_ENTRY', 'manifiest_date', -2, 59),
       ('HAWB No', 'HAWB No', 'BILL_OF_ENTRY', 'manifiest_hawb_no', -2, 60),
       ('Date', 'Date', 'BILL_OF_ENTRY', 'manifiest_m_date', -2, 61),
       ('PKG', 'PKG', 'BILL_OF_ENTRY', 'manifiest_pkg', -2, 62),
       ('GW', 'GW', 'BILL_OF_ENTRY', 'manifiest_gw', -2, 63),
       ('Submission Date', 'Submission Date', 'BILL_OF_ENTRY', 'bill_of_entry_submission_date', -2, 64),
       ('Submission Time', 'Submission Time', 'BILL_OF_ENTRY', 'bill_of_entry_submission_time', -2, 65),
       ('Assessment Date', 'Assessment Date', 'BILL_OF_ENTRY', 'bill_of_entry_assessment_date', -2, 66),
       ('Assessment Time', 'Assessment Time', 'BILL_OF_ENTRY', 'bill_of_entry_assessment_time', -2, 67),
       ('Examination Date', 'Examination Date', 'BILL_OF_ENTRY', 'bill_of_entry_examination_date', -2, 68),
       ('Examination Time', 'Examination Time', 'BILL_OF_ENTRY', 'bill_of_entry_examination_time', -2, 69),
       ('OOC Date', 'OOC Date', 'BILL_OF_ENTRY', 'bill_of_entry_ooc_date', -2, 70),
       ('OOC Time', 'OOC Time', 'BILL_OF_ENTRY', 'bill_of_entry_ooc_time', -2, 71),
       ('Exchange rate', 'Exchange rate', 'BILL_OF_ENTRY', 'exchange_rate', -2, 72),
       ('Invoice Value', 'Invoice Value', 'BILL_OF_ENTRY', 'inv_value', -2, 73),
       ('Invoice Currency', 'Invoice Currency', 'BILL_OF_ENTRY', 'inv_currency', -2, 74),
       ('Invoice No', 'Invoice No', 'BILL_OF_ENTRY', 'inv_no', -2, 75),
       ('Invoice Date', 'Invoice Date', 'BILL_OF_ENTRY', 'inv_dt', -2, 76),
       ('Pur order no.', 'Pur order no.', 'BILL_OF_ENTRY', 'pur_orde_no', -2, 77),
       ('Pur order date', 'Pur order date', 'BILL_OF_ENTRY', 'pur_order_date', -2, 78),
       ('LC No.', 'LC No.', 'BILL_OF_ENTRY', 'lc_no', -2, 79),
       ('LC date', 'LC date', 'BILL_OF_ENTRY', 'lc_date', -2, 80),
       ('Contract no', 'Contract no', 'BILL_OF_ENTRY', 'contrac_no', -2, 81),
       ('Contract date', 'Contract date', 'BILL_OF_ENTRY', 'contract_date', -2, 82),
       ('Buyer\'s name', 'Buyer\'s name', 'BILL_OF_ENTRY', 'buyers_name', -2, 83),
       ('Buyer\'s address', 'Buyer\'s address', 'BILL_OF_ENTRY', 'buyers_address', -2, 84),
       ('Seller\'s name', 'Seller\'s name', 'BILL_OF_ENTRY', 'sellers_name', -2, 85),
       ('Seller\'s address', 'Seller\'s address', 'BILL_OF_ENTRY', 'sellers_address', -2, 86),
       ('Supplier name', 'Supplier name', 'BILL_OF_ENTRY', 'supplier_name', -2, 87),
       ('Supplier address', 'Supplier address', 'BILL_OF_ENTRY', 'supplier_address', -2, 88),
       ('Third party name', 'Third party name', 'BILL_OF_ENTRY', 'third_party_name', -2, 89),
       ('Third party address', 'Third party address', 'BILL_OF_ENTRY', 'third_party_address', -2, 90),
       ('AEO', 'AEO', 'BILL_OF_ENTRY', 'transacting_parties_aeo', -2, 91),
       ('AD code', 'AD code', 'BILL_OF_ENTRY', 'transacting_parties_ad_code', -2, 92),
       ('Invoice Value', 'Invoice Value', 'BILL_OF_ENTRY', 'valuation_inv_value', -2, 93),
       ('Freight', 'Freight', 'BILL_OF_ENTRY', 'freight', -2, 94),
       ('Insurance', 'Insurance', 'BILL_OF_ENTRY', 'insurance', -2, 95),
       ('HSS', 'HSS', 'BILL_OF_ENTRY', 'hss', -2, 96),
       ('Loading', 'Loading', 'BILL_OF_ENTRY', 'loading', -2, 97),
       ('Commn', 'Commn', 'BILL_OF_ENTRY', 'commn', -2, 98),
       ('Pay terms', 'Pay terms', 'BILL_OF_ENTRY', 'pay_terms', -2, 99),
       ('Valuation terms', 'Valuation terms', 'BILL_OF_ENTRY', 'valuation_terms', -2, 100),
       ('RELTD', 'RELTD', 'BILL_OF_ENTRY', 'reltd', -2, 101),
       ('SVB CH', 'SVB CH', 'BILL_OF_ENTRY', 'svb_ch', -2, 102),
       ('SVB No.', 'SVB No.', 'BILL_OF_ENTRY', 'svb_no', -2, 103),
       ('Date', 'Date', 'BILL_OF_ENTRY', 'valuation_date', -2, 104),
       ('LOA', 'LOA', 'BILL_OF_ENTRY', 'loa', -2, 105),
       ('Invoice Currency', 'Invoice Currency', 'BILL_OF_ENTRY', 'valuation_inv_currency', -2, 106),
       ('term', 'term', 'BILL_OF_ENTRY', 'term', -2, 107),
       ('C&B', 'C&B', 'BILL_OF_ENTRY', 'c_b', -2, 108),
       ('CoC', 'CoC', 'BILL_OF_ENTRY', 'coc', -2, 109),
       ('CoP', 'CoP', 'BILL_OF_ENTRY', 'cop', -2, 110),
       ('HIND Chg', 'HIND Chg', 'BILL_OF_ENTRY', 'hind_chg', -2, 111),
       ('G&S', 'G&S', 'BILL_OF_ENTRY', 'g_s', -2, 112),
       ('DOC CH', 'DOC CH', 'BILL_OF_ENTRY', 'doc_ch', -2, 113),
       ('COO', 'COO', 'BILL_OF_ENTRY', 'coo', -2, 114),
       ('R&LF', 'R&LF', 'BILL_OF_ENTRY', 'r_lf', -2, 115),
       ('OTH Cost', 'OTH Cost', 'BILL_OF_ENTRY', 'oth_cost', -2, 116),
       ('LD/ULD', 'LD/ULD', 'BILL_OF_ENTRY', 'ld_uld', -2, 117),
       ('WS', 'WS', 'BILL_OF_ENTRY', 'ws', -2, 118),
       ('OTC', 'OTC', 'BILL_OF_ENTRY', 'otc', -2, 119),
       ('MISC Chg', 'MISC Chg', 'BILL_OF_ENTRY', 'misc_chg', -2, 120),
       ('Ass Value', 'Ass Value', 'BILL_OF_ENTRY', 'ass_value', -2, 121),
       ('Item Code', 'Item Code', 'BILL_OF_ENTRY', 'item_code', -2, 122),
       ('HSN', 'HSN', 'BILL_OF_ENTRY', 'hsn', -2, 123),
       ('Description & Technical Characteristics', 'Description & Technical Characteristics', 'BILL_OF_ENTRY',
        'desc_technical_characteristics', -2, 124),
       ('Unit Price', 'Unit Price', 'BILL_OF_ENTRY', 'unit_price', -2, 125),
       ('Qty. Imported', 'Qty. Imported', 'BILL_OF_ENTRY', 'imported_qty', -2, 126),
       ('Unit of Measurement', 'Unit of Measurement', 'BILL_OF_ENTRY', 'uqc', -2, 127),
       ('Item Value', 'Item Value', 'BILL_OF_ENTRY', 'item_value', -2, 128),
       ('Item assessable value', 'Item assessable value', 'BILL_OF_ENTRY', 'item_assessable_value', -2, 129),
       ('Total Duty', 'Total Duty', 'BILL_OF_ENTRY', 'total_duty', -2, 130),
       ('FS', 'FS', 'BILL_OF_ENTRY', 'fs', -2, 131),
       ('PQ', 'PQ', 'BILL_OF_ENTRY', 'pq', -2, 132),
       ('DC', 'DC', 'BILL_OF_ENTRY', 'dc', -2, 133),
       ('WC', 'WC', 'BILL_OF_ENTRY', 'wc', -2, 134),
       ('AQ', 'AQ', 'BILL_OF_ENTRY', 'aq', -2, 135),
       ('UPI', 'UPI', 'BILL_OF_ENTRY', 'upi', -2, 136),
       ('COO', 'COO', 'BILL_OF_ENTRY', 'item_dtls_coo', -2, 137),
       ('C. QTY', 'C. QTY', 'BILL_OF_ENTRY', 'c_qty', -2, 138),
       ('C. UQC', 'C. UQC', 'BILL_OF_ENTRY', 'c_uqc', -2, 139),
       ('S. QTY', 'S. QTY', 'BILL_OF_ENTRY', 's_qty', -2, 140),
       ('S. UQC', 'S. UQC', 'BILL_OF_ENTRY', 's_uqc', -2, 141),
       ('SCH', 'SCH', 'BILL_OF_ENTRY', 'sch', -2, 142),
       ('STND/PR', 'STND/PR', 'BILL_OF_ENTRY', 'stnd_pr', -2, 143),
       ('RSP', 'RSP', 'BILL_OF_ENTRY', 'rsp', -2, 144),
       ('REIMP', 'REIMP', 'BILL_OF_ENTRY', 'reimp', -2, 145),
       ('PROV', 'PROV', 'BILL_OF_ENTRY', 'prov', -2, 146),
       ('End Use', 'End Use', 'BILL_OF_ENTRY', 'end_use', -2, 147),
       ('PRODN', 'PRODN', 'BILL_OF_ENTRY', 'prodn', -2, 148),
       ('CNTRL', 'CNTRL', 'BILL_OF_ENTRY', 'cntrl', -2, 149),
       ('QUALFR', 'QUALFR', 'BILL_OF_ENTRY', 'qualfr', -2, 150),
       ('CONTNT', 'CONTNT', 'BILL_OF_ENTRY', 'contnt', -2, 151),
       ('STMNT', 'STMNT', 'BILL_OF_ENTRY', 'stmnt', -2, 152),
       ('SUP DOCS', 'SUP DOCS', 'BILL_OF_ENTRY', 'sup_docs', -2, 153),
       ('Notn no.', 'Notn no.', 'BILL_OF_ENTRY', 'notn_no', -2, 154),
       ('Notn Sr. no.', 'Notn Sr. no.', 'BILL_OF_ENTRY', 'notn_sr_no', -2, 155),
       ('BCD rate', 'BCD rate', 'BILL_OF_ENTRY', 'bcd_rate', -2, 156),
       ('BCD Amount', 'BCD Amount', 'BILL_OF_ENTRY', 'bcd_amount', -2, 157),
       ('BCD Duty FG', 'BCD Duty FG', 'BILL_OF_ENTRY', 'bcd_duty_fg', -2, 158),
       ('Acd Rate', 'Acd Rate', 'BILL_OF_ENTRY', 'acd_rate', -2, 159),
       ('Acd Value', 'Acd Value', 'BILL_OF_ENTRY', 'acd_amount', -2, 160),
       ('ACD Duty FG', 'ACD Duty FG', 'BILL_OF_ENTRY', 'acd_duty_fg', -2, 161),
       ('Sws Rate', 'Sws Rate', 'BILL_OF_ENTRY', 'sws_rate', -2, 162),
       ('Sws Value', 'Sws Value', 'BILL_OF_ENTRY', 'sws_amount', -2, 163),
       ('SWS Duty FG', 'SWS Duty FG', 'BILL_OF_ENTRY', 'sws_duty_fg', -2, 164),
       ('Sad Rate', 'Sad Rate', 'BILL_OF_ENTRY', 'sad_rate', -2, 165),
       ('Sad Value', 'Sad Value', 'BILL_OF_ENTRY', 'sad_amount', -2, 166),
       ('SAD Duty FG', 'SAD Duty FG', 'BILL_OF_ENTRY', 'sad_duty_fg', -2, 167),
       ('IGST Notn no.', 'IGST Notn no.', 'BILL_OF_ENTRY', 'igst_notn_no', -2, 168),
       ('IGST Notn Sr. no.', 'IGST Notn Sr. no.', 'BILL_OF_ENTRY', 'igst_notn_sr_no', -2, 169),
       ('Igst Rate', 'Igst Rate', 'BILL_OF_ENTRY', 'igst_rate', -2, 170),
       ('Igst Value', 'Igst Value', 'BILL_OF_ENTRY', 'igst_amount', -2, 171),
       ('IGST Duty FG', 'IGST Duty FG', 'BILL_OF_ENTRY', 'igst_duty_fg', -2, 172),
       ('Cess Notn no.', 'Cess Notn no.', 'BILL_OF_ENTRY', 'cess_notn_no', -2, 173),
       ('Cess Notn Sr. no.', 'Cess Notn Sr. no.', 'BILL_OF_ENTRY', 'cess_notn_sr_no', -2, 174),
       ('Customs Cess rate', 'Customs Cess rate', 'BILL_OF_ENTRY', 'customs_cess_rate', -2, 175),
       ('Cess Value', 'Cess Value', 'BILL_OF_ENTRY', 'cess_amount', -2, 176),
       ('Cess Duty FG', 'Cess Duty FG', 'BILL_OF_ENTRY', 'cess_duty_fg', -2, 177),
       ('Add Rate', 'Add Rate', 'BILL_OF_ENTRY', 'add_rate', -2, 178),
       ('Add Value', 'Add Value', 'BILL_OF_ENTRY', 'add_amount', -2, 179),
       ('ADD Duty FG', 'ADD Duty FG', 'BILL_OF_ENTRY', 'add_duty_fg', -2, 180),
       ('Cvd Rate', 'Cvd Rate', 'BILL_OF_ENTRY', 'cvd_rate', -2, 181),
       ('Cvd Value', 'Cvd Value', 'BILL_OF_ENTRY', 'cvd_amount', -2, 182),
       ('CVD Duty FG', 'CVD Duty FG', 'BILL_OF_ENTRY', 'cvd_duty_fg', -2, 183),
       ('SG rate', 'SG rate', 'BILL_OF_ENTRY', 'sg_rate', -2, 184),
       ('SG amt', 'SG amt', 'BILL_OF_ENTRY', 'sg_amt', -2, 185),
       ('SG Duty FG', 'SG Duty FG', 'BILL_OF_ENTRY', 'sg_duty_fg', -2, 186),
       ('SP EXD rate', 'SP EXD rate', 'BILL_OF_ENTRY', 'sp_exd_rate', -2, 187),
       ('SP EXD amt', 'SP EXD amt', 'BILL_OF_ENTRY', 'sp_exd_amt', -2, 188),
       ('CH Cess rate', 'CH Cess rate', 'BILL_OF_ENTRY', 'ch_cess_rate', -2, 189),
       ('CH Cess amt', 'CH Cess amt', 'BILL_OF_ENTRY', 'ch_cess_amt', -2, 190),
       ('TTA rate', 'TTA rate', 'BILL_OF_ENTRY', 'tta_rate', -2, 191),
       ('TTA amt', 'TTA amt', 'BILL_OF_ENTRY', 'tta_amt', -2, 192),
       ('Cess rate', 'Cess rate', 'BILL_OF_ENTRY', 'oth_duty_cess_rate', -2, 193),
       ('Cess amt', 'Cess amt', 'BILL_OF_ENTRY', 'oth_duty_cess_amt', -2, 194),
       ('CVD EDC rate', 'CVD EDC rate', 'BILL_OF_ENTRY', 'cvd_edc_rate', -2, 195),
       ('CVD EDC amt', 'CVD EDC amt', 'BILL_OF_ENTRY', 'cvd_edc_amt', -2, 196),
       ('CVD HEC rate', 'CVD HEC rate', 'BILL_OF_ENTRY', 'cvd_hec_rate', -2, 197),
       ('CVD HEC amt', 'CVD HEC amt', 'BILL_OF_ENTRY', 'cvd_hec_amt', -2, 198),
       ('CUS EDC rate', 'CUS EDC rate', 'BILL_OF_ENTRY', 'cus_edc_rate', -2, 199),
       ('CUS EDC amt', 'CUS EDC amt', 'BILL_OF_ENTRY', 'cus_edc_amt', -2, 200),
       ('CUS HEC rate', 'CUS HEC rate', 'BILL_OF_ENTRY', 'cus_hec_rate', -2, 201),
       ('CUS HEC amt', 'CUS HEC amt', 'BILL_OF_ENTRY', 'cus_hec_amt', -2, 202),
       ('NCD rate', 'NCD rate', 'BILL_OF_ENTRY', 'ncd_rate', -2, 203),
       ('NCD amt', 'NCD amt', 'BILL_OF_ENTRY', 'ncd_amt', -2, 204),
       ('AGGR rate', 'AGGR rate', 'BILL_OF_ENTRY', 'aggr_rate', -2, 205),
       ('AGGR amt', 'AGGR amt', 'BILL_OF_ENTRY', 'aggr_amt', -2, 206),
       ('REF No.', 'REF No.', 'BILL_OF_ENTRY', 'REF_No.', -2, 207),
       ('REF Dt', 'REF Dt', 'BILL_OF_ENTRY', 'ref_dt', -2, 208),
       ('PRT CD', 'PRT CD', 'BILL_OF_ENTRY', 'prt_cd', -2, 209),
       ('LAB', 'LAB', 'BILL_OF_ENTRY', 'lab', -2, 210),
       ('P/F', 'P/F', 'BILL_OF_ENTRY', 'P/f', -2, 211),
       ('LOAD Date', 'LOAD Date', 'BILL_OF_ENTRY', 'load_date', -2, 212),
       ('INV Sr. No.', 'INV Sr. No.', 'BILL_OF_ENTRY', 'inv_sr_no', -2, 213),
       ('Item Sr. No.', 'Item Sr. No.', 'BILL_OF_ENTRY', 'item_sr_no', -2, 214),
       ('Notn No.', 'Notn No.', 'BILL_OF_ENTRY', 're_imp_notn_no', -2, 215),
       ('SLNO', 'SLNO', 'BILL_OF_ENTRY', 'slno', -2, 216),
       ('FRT', 'FRT', 'BILL_OF_ENTRY', 'frt', -2, 217),
       ('INS', 'INS', 'BILL_OF_ENTRY', 'ins', -2, 218),
       ('Duty', 'Duty', 'BILL_OF_ENTRY', 'duty', -2, 219),
       ('SB No.', 'SB No.', 'BILL_OF_ENTRY', 'sb_no', -2, 220),
       ('SB DT', 'SB DT', 'BILL_OF_ENTRY', 'sb_dt', -2, 221),
       ('PORT CD', 'PORT CD', 'BILL_OF_ENTRY', 'port_cd', -2, 222),
       ('SINV', 'SINV', 'BILL_OF_ENTRY', 'sinv', -2, 223),
       ('SITEMN', 'SITEMN', 'BILL_OF_ENTRY', 'sitemn', -2, 224),
       ('Licence SL No.', 'Licence SL No.', 'BILL_OF_ENTRY', 'lic_sl_no', -2, 225),
       ('Licence No.', 'Licence No.', 'BILL_OF_ENTRY', 'lic_no', -2, 226),
       ('Licence Date', 'Licence Date', 'BILL_OF_ENTRY', 'lic_date', -2, 227),
       ('Licence Code', 'Licence Code', 'BILL_OF_ENTRY', 'lic_code', -2, 228),
       ('Licence Port', 'Licence Port', 'BILL_OF_ENTRY', 'lic_port', -2, 229),
       ('Licence Debit Value', 'Licence Debit Value', 'BILL_OF_ENTRY', 'lic_debit_value', -2, 230),
       ('Licence Qty.', 'Licence Qty.', 'BILL_OF_ENTRY', 'lic_qty', -2, 231),
       ('Licence Debit Duty', 'Licence Debit Duty', 'BILL_OF_ENTRY', 'lic_debit_duty', -2, 232),
       ('Licence UQC', 'Licence UQC', 'BILL_OF_ENTRY', 'lic_uqc', -2, 233),
       ('Certificate No.', 'Certificate No.', 'BILL_OF_ENTRY', 'certificate_no', -2, 234),
       ('Date', 'Date', 'BILL_OF_ENTRY', 'certificate_date', -2, 235),
       ('Type', 'Type', 'BILL_OF_ENTRY', 'certificate_type', -2, 236),
       ('PRC Level', 'PRC Level', 'BILL_OF_ENTRY', 'hss_prc_level', -2, 237),
       ('IEC', 'IEC', 'BILL_OF_ENTRY', 'hss_iec', -2, 238),
       ('Baranch SL No.', 'Baranch SL No.', 'BILL_OF_ENTRY', 'hss_baranch_sl_no', -2, 239),
       ('OCC No', 'OCC No', 'BILL_OF_ENTRY', 'ooc_no', -2, 240),
       ('OCC Date', 'OCC Date', 'BILL_OF_ENTRY', 'ooc_date', -2, 241);