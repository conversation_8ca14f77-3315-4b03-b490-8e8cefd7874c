-- #################################################################
-- ### Server version       :       1.0
-- ### Date (DD-MM-YYYY)    :       11/09/2023
-- ### Developer Name       : 		Mr<PERSON><PERSON>e
-- ### Comments             :       DB Script for Integration of the PArt-4 of the BOE Parsing
-- #################################################################

-- Add entry for new sections for the warehouse and payment details
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'bill_of_entry_summary_bond_details', 'PART - I - BILL OF ENTRY SUMMARY', 17.666, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'bill_of_entry_summary_payment_details', 'PART - I - BILL OF ENTRY SUMMARY', 17.666, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'bill_of_entry_summary_warehouse_details', 'PART - I - BILL OF ENTRY SUMMARY', 17.666, now());

-- update bond details field parsing papping with new section
UPDATE pdf_field_strategy_mapping SET SECTION_KEY = 'bill_of_entry_summary_bond_details', STRATEGY_ID = (SELECT ID FROM pdf_parsing_strategies WHERE STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL') WHERE MAPPING_KEY IN ('bondNo', 'bondPort', 'bondCode', 'bondDebtAmt', 'bondBgAmt');

-- Add new entry for warehouse details in the field parsing strategy details table
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.WBE NO.', 'whWbeNo', 'bill_of_entry_summary_warehouse_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.DATE', 'whDate', 'bill_of_entry_summary_warehouse_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.WBE SITE', 'whWbeSite', 'bill_of_entry_summary_warehouse_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.WH CODE', 'whCode', 'bill_of_entry_summary_warehouse_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';

-- Add new entries into the data normalizer Vo for the Warehouse details fields
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('whWbeNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('whDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('whWbeSite', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('whCode', 'NORM_TO_TEXT_VAL', null, null);

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.CHALLAN NO', 'paymentChallanNo', 'bill_of_entry_summary_payment_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.PAID NO', 'paymentPaidNo', 'bill_of_entry_summary_payment_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.AMOUNT(Rs.)', 'paymentAmount', 'bill_of_entry_summary_payment_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';

-- Add new entries into the data normalizer Vo for the Additional Invoice details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('paymentChallanNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('paymentPaidNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('paymentAmount', 'NORM_TO_DOUBLE', null, null);

INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'ADDITIONAL_CONTAINER_DETAILS', 'sr_no', -7, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'ADDITIONAL_CONTAINER_DETAILS', 'bill_of_entry_no', -7, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Container Number', 'Container Number', 'ADDITIONAL_CONTAINER_DETAILS', 'additional_container_no', -7, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Truck Number', 'Truck Number', 'ADDITIONAL_CONTAINER_DETAILS', 'additional_container_truck_no', -7, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Seal Number', 'Seal Number', 'ADDITIONAL_CONTAINER_DETAILS', 'additional_container_seal_no', -7, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('FCL/LCL', 'FCL/LCL', 'ADDITIONAL_CONTAINER_DETAILS', 'additional_container_fcl_lcl', -7, 6);

INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'BOE_WAREHOUSE_DETAILS', 'sr_no', -8, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'BOE_WAREHOUSE_DETAILS', 'bill_of_entry_no', -8, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Ware House BOE No.', 'Ware House BOE No.', 'BOE_WAREHOUSE_DETAILS', 'wh_wbe_no', -8, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Date', 'Date', 'BOE_WAREHOUSE_DETAILS', 'wh_date', -8, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Warehouse BOE Site', 'Warehouse BOE Site', 'BOE_WAREHOUSE_DETAILS', 'wh_wbe_site', -8, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Warehouse Code', 'Warehouse Code', 'BOE_WAREHOUSE_DETAILS', 'wh_code', -8, 6);

INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'BOE_PAYMENT_DETAILS', 'sr_no', -9, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'BOE_PAYMENT_DETAILS', 'bill_of_entry_no', -9, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Challan No.', 'Challan No.', 'BOE_PAYMENT_DETAILS', 'payment_challan_no', -9, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Paid No', 'Paid No', 'BOE_PAYMENT_DETAILS', 'payment_paid_no', -9, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Amount', 'Amount', 'BOE_PAYMENT_DETAILS', 'payment_amount', -9, 5);

INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'BOE_BOND_DETAILS', 'sr_no', -10, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'BOE_BOND_DETAILS', 'bill_of_entry_no', -10, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bond no.', 'Bond no.', 'BOE_BOND_DETAILS', 'bond_no', -10, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Port', 'Port', 'BOE_BOND_DETAILS', 'bond_port', -10, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bond CD', 'Bond CD', 'BOE_BOND_DETAILS', 'bond_code', -10, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Debt Amt', 'Debt Amt', 'BOE_BOND_DETAILS', 'bond_debt_amt', -10, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BG Amt', 'BG Amt', 'BOE_BOND_DETAILS', 'bond_bg_amt', -10, 7);