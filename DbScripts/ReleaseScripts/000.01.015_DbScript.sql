-- #################################################################
-- ### Server version       :       1.0
-- ### Date (DD-MM-YYYY)    :       01-12-2024
-- ### Developer Name       : 		Mruna<PERSON> Nagare
-- ### Comments             :       <PERSON> Script for adding Subscription service request audit log table
-- #################################################################

drop table if exists subscription_service_request_audit_log;
create table subscription_service_request_audit_log
(
    ID              bigint auto_increment primary key,
    PAN             varchar(20)  not null,
    TXN_ID          varchar(100) null,
    ACTION          varchar(100) not null,
    API_KEY         varchar(100) not null,
    RESOURCE_PATH   varchar(255) not null,
    REQUEST_HEADERS text         null,
    REQUEST_PARAMS  text         null,
    REQUEST_PAYLOAD mediumtext   null,
    STATUS          varchar(50)  null,
    RESPONSE        mediumtext   null,
    CREATED_AT      datetime     null,
    UPDATED_AT      datetime     null
);

create index pan_index
    on subscription_service_request_audit_log (PAN);

create index action_index
    on subscription_service_request_audit_log (ACTION);

create index status_index
    on subscription_service_request_audit_log (STATUS);


