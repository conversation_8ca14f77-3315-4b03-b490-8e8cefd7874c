-- #################################################################
-- ### Server version: 			    1.0
-- ### Date (DD-MM-YYYY):           19/06/2023
-- ### Developer Name: 		        Mrunal Nagare
-- ### Comments:                    DB Script for Temporary subscription integration
-- #################################################################

-- Create user table
drop table if exists user;
create table user
(
    ID            bigint(20) auto_increment primary key,
    UUID          varchar(100),
    EMAIL         varchar(255) not null,
    FIRST_NAME    varchar(50)  not null,
    LAST_NAME     varchar(50)  not null,
    MOBILE        varchar(15)  not null,
    PAN           varchar(32),
    PASSWORD      varchar(255) not null,
    STATUS        varchar(30)  not null,
    SESSION_TOKEN varchar(32),
    LAST_LOGIN    datetime,
    IS_DELETED    tinyint(1)   not null,
    CREATED_AT    datetime default now(),
    UPDATED_AT    datetime,
    constraint EMAIL unique (EMAIL)
);


create index EMAIL_INDEX on user (EMAIL);


drop table if exists user_pan_sub_mapping;
-- Create user PAN mapping table
create table user_pan_sub_mapping
(
    ID            bigint(20) auto_increment primary key,
    USER_ID       bigint(20)  not null,
    PAN           varchar(25) not null,
    EXPIRY_DATE   datetime not null,
    IS_SUB_ACTIVE bit      default 1,
    CREATED_AT    datetime default now(),
    UPDATED_AT    datetime,

    constraint user_fk foreign key (USER_ID) references user (ID)
);