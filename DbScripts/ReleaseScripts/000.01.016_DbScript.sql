-- #####################################################################################################################
-- ### Server version: 			    1.0
-- ### Date (DD-MM-YYYY):           03-24-2024
-- ### Developer Name: 		        Mrunal Nagare
-- ### Comments:                    <PERSON> for adding the field identification strategies for ADV BE, EXAM and FIRST_CHECK fields
-- #####################################################################################################################

-- Create new Field Strategy mapping entries for ADV BE, EXAM and FIRST_CHECK fields for BOE file parsing
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.ADV BE', 'advBe', 'bill_of_entry_summary', ID, 10.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.EXAM', 'exam', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '11.FIRST', 'firstCheck', 'bill_of_entry_summary', ID, 10.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';