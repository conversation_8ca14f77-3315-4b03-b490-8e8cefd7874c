-- #################################################################
-- ### Server version       :       1.0
-- ### Date (DD-MM-YYYY)    :       06/12/2023
-- ### Developer Name       : 		Mr<PERSON>l <PERSON>e
-- ### Comments             :       <PERSON> to Modify the field strategy mapping for exchange rate field
-- #################################################################

update pdf_field_strategy_mapping
set STRATEGY_ID  = (select ID from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_ML'),
    BUFFER_VALUE = 6.080
where MAPPING_KEY = 'exchangeRate'
  and SECTION_KEY = 'bill_of_entry_summary'
  and FILE_TYPE = 'BILL_OF_ENTRY';


INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD)
select 'BILL_OF_ENTRY', '4.GIGMNO', 'gigmNo', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD)
select 'BILL_OF_ENTRY', '5.GIGMDT', 'gigmDt', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD)
select 'BILL_OF_ENTRY', '6.MAWB NO', 'maqbNo', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD)
select 'BILL_OF_ENTRY', '7.DATE', 'date', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD)
select 'BILL_OF_ENTRY', '8.HAWB NO', 'hawbNo', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD)
select 'BILL_OF_ENTRY', '9.DATE', 'manifiestDate', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD)
select 'BILL_OF_ENTRY', '10.PKG', 'pkg', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD)
select 'BILL_OF_ENTRY', '11.GW', 'gw', 'bill_of_entry_summary', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';

INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('gigmNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('gigmDt', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('maqbNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('hawbNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('manifiestDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('pkg', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('gw', 'NORM_TO_TEXT_VAL', null, null);