-- #################################################################
-- ### Server version       :       1.0
-- ### Date (DD-MM-YYYY)    :       08/11/2023
-- ### Developer Name       : 		Mr<PERSON>l Nagare
-- ### Comments             :       <PERSON> Script Add the export file templates for Duty drawback and MOOWR files
-- #################################################################

-- delete the template mapping for the Duty drawback file type if present
DELETE FROM export_report_template_mapping where MAPPING_ENTITY = 'BILL_OF_ENTRY_DUTY_DRAWBACK';

-- insert new template mappings for the Duty drawback file format
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'sr_no', -11, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item Code', 'Item Code', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'item_code', -11, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Description & Technical Characteristics', 'Description & Technical Characteristics', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'desc_technical_characteristics', -11, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'bill_of_entry_no', -11, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry Date', 'Bill of Entry Date', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'bill_of_entry_date', -11, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Purchase inv no', 'Purchase inv no', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'purchase_inv_no', -11, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Purchase inv date', 'Purchase inv date', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'purchase_inv_date', -11, 7);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name of Customs House', 'Name of Customs House', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'name_of_customs_house', -11, 8);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('HSN', 'HSN', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'hsn', -11, 9);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Qty. Imported', 'Qty. Imported', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'imported_qty', -11, 10);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Available Qty', 'Available Qty', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'available_qty', -11, 11);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Utilized Qty', 'Utilized Qty', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'utilized_uty', -11, 12);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Unit of Measurement', 'Unit of Measurement', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'uqc', -11, 13);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Assessable Value as per BOE(Rs.)', 'Assessable Value as per BOE(Rs.)', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'assessable_value_as_per_boe', -11, 14);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BCD rate', 'BCD rate', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'bcd_rate', -11, 15);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Customs Cess rate', 'Customs Cess rate', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'customs_cess_rate', -11, 16);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Country from where imported', 'Country from where imported', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'imported_country_from', -11, 17);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name of supplier', 'Name of supplier', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'supplier_name', -11, 18);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Is Assessment Final?', 'Is Assessment Final?', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'is_assessment_final', -11, 19);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name & full address of the supplier in case the foreign materials/ components obtained locally', 'Name & full address of the supplier in case the foreign materials/ components obtained locally', 'BILL_OF_ENTRY_DUTY_DRAWBACK', 'foreign_material_details', -11, 20);

-- delete the template mapping for the MOOWR file Type if present
DELETE FROM export_report_template_mapping where MAPPING_ENTITY = 'BILL_OF_ENTRY_MOOWR';

-- insert new template mappings for the MOOWR file format
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No', 'Sr. No', 'BILL_OF_ENTRY_MOOWR', 'sr_no', -12, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'BILL_OF_ENTRY_MOOWR', 'bill_of_entry_no', -12, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Date', 'Date', 'BILL_OF_ENTRY_MOOWR', 'bill_of_entry_date', -12, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Customs Station of import', 'Customs Station of import', 'BILL_OF_ENTRY_MOOWR', 'port_of_loading', -12, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Details of Bond', 'Details of Bond', 'BILL_OF_ENTRY_MOOWR', 'bond_no', -12, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Details of insurance', 'Details of insurance', 'BILL_OF_ENTRY_MOOWR', 'insurance_dtls', -12, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Description of goods', 'Description of goods', 'BILL_OF_ENTRY_MOOWR', 'desc_technical_characteristics', -12, 7);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice No', 'Invoice No', 'BILL_OF_ENTRY_MOOWR', 'purchase_inv_no', -12, 8);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice date', 'Invoice date', 'BILL_OF_ENTRY_MOOWR', 'purchase_inv_date', -12, 9);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Quantity with UQC', 'Quantity with UQC', 'BILL_OF_ENTRY_MOOWR', 'imported_qty', -12, 10);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Unit of Measure', 'Unit of Measure', 'BILL_OF_ENTRY_MOOWR', 'uqc', -12, 11);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Assessable Value', 'Assessable Value', 'BILL_OF_ENTRY_MOOWR', 'assessable_value_as_per_boe', -12, 12);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BCD', 'BCD', 'BILL_OF_ENTRY_MOOWR', 'bcd_rate', -12, 13);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IGST', 'IGST', 'BILL_OF_ENTRY_MOOWR', 'igst_rate', -12, 14);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Comp. cess(Sws)', 'Comp. cess(Sws)', 'BILL_OF_ENTRY_MOOWR', 'customs_cess_rate', -12, 15);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Registration No. of means of transport', 'Registration No. of means of transport', 'BILL_OF_ENTRY_MOOWR', 'reg_no_of_transport', -12, 16);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('One-time Lock no', 'One-time Lock no', 'BILL_OF_ENTRY_MOOWR', 'one_time_lock', -12, 17);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Date and time of receipt at the warehouse', 'Date and time of receipt at the warehouse', 'BILL_OF_ENTRY_MOOWR', 'ware_house_date_time', -12, 18);