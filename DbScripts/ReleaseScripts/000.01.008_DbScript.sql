-- #################################################################
-- ### Server version: 			    1.0
-- ### Date (DD-MM-YYYY):           11/09/2023
-- ### Developer Name: 		        Mr<PERSON><PERSON>e
-- ### Comments:                    DB Script for Integration of the PArt-4 of the BOE Parsing
-- #################################################################

-- Update the section key column size in pdf_section_details table
alter table pdf_field_strategy_mapping
    drop foreign key pdf_field_strategy_mapping_pdf_section_details_SECTION_KEY_fk;

alter table pdf_field_strategy_mapping
    modify SECTION_KEY varchar(100) not null;

alter table pdf_section_details
    modify SECTION_KEY varchar(100) not null;

alter table pdf_field_strategy_mapping
    add constraint sectionKeyFk
        foreign key (SECTION_KEY) references pdf_section_details (SECTION_KEY);

alter table pdf_field_strategy_mapping
    drop key unique_strategy;

alter table pdf_field_strategy_mapping
    add constraint unique_strategy
        unique (FILE_TYPE, FIELD_NAME, STRATEGY_ID, MAPPING_KEY, SECTION_KEY);

-- Add Part-IV section details in to database
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_svb_details', 'A. SVB DETAILS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_prev_boe_details', 'B. PREVIOUS BEs', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_re_import_details', 'C. RE-IMPORT AFTER EXPORT', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_item_manufacturer_details', 'D. ITEM MANUFACTURER/PRODUCER/GROWER DETAILS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_accessory_status_details', 'E. ACCESSORY STATUS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_licence_details', 'F. LICENCE DETAILS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_certificate_details', 'G. CERTIFICATE DETAILS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_hss_details', 'H.HSS DETAILS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_single_window_declaration_details', 'I. SINGLE WINDOW DECLARATION', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_single_window_declaration_constituents_details', 'J. SINGLE WINDOW DECLARATION - CONSTITUENTS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_single_window_declaration_control_details', 'K. SINGLE WINDOW DECLARATION - CONTROL', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_supporting_doc_details', 'L. SUPPORTING DOCUMENTS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_container_details', 'M. CONTAINER DETAILS', 1.600, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_invoice_details', 'N. INVOICE DETAILS', 1.600, now());

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '8.ASSESS', 'assess', 'bill_of_entry_summary', 2, 6.120, now(), null, 0);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.SUPPLIER NAME & ADDRESS', 'supplierNameAndAddress', 'invoice_and_valuation_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'NAME_AND_ADDRESS_ML';

-- Create new Field Strategy mapping entries for additional_svb_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.INVSNO', 'invSN', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.ITMSNO', 'itemSN', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.REF NO', 'svbRefNo', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.REF DT', 'svbRefDate', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5. PRT CD', 'svbPartCode', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.LAB', 'svbLab', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.P/F', 'svbPf1', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.LOAD DATE', 'svbLoadDate', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.P/F', 'svbPf2', 'additional_svb_details', ID, 1.600, now(), null, 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';

-- Add new entries into the data normalizer Vo for the Additional SVB details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('svbRefNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('svbRefDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('svbPartCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('svbLab', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('svbPf1', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('svbLoadDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('svbPf2', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_prev_boe_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.INVSNO', 'invSN', 'additional_prev_boe_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.ITMSNO', 'itemSN', 'additional_prev_boe_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '3. BE NO', 'prevBoeNo', 'additional_prev_boe_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4. BE DATE', 'prevBoeDate', 'additional_prev_boe_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '5. PRT CD', 'prevBoePartCode', 'additional_prev_boe_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '6.UNITPRICE', 'prevBoeUnitPrice', 'additional_prev_boe_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '7.CURRENCY CODE', 'prevBoeCurrencyCode', 'additional_prev_boe_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Prev BOE details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('prevBoeNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('prevBoeDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('prevBoePartCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('prevBoeUnitPrice', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('prevBoeCurrencyCode', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_re_import_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.INVSNO', 'invSN', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.ITMSNO', 'itemSN', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '3.NOTN NO', 'reImpNoteNo', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4.SLNO', 'reImpSlNo', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '5.FRT', 'reImpFrt', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '6.INS', 'reImpUnitIns', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '7.DUTY', 'reImpDuty', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '8.SB NO', 'reImpSbNo', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '9.SB DT', 'reImpSbDate', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '10.PORTCD', 'reImpPortCd', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '11.SINV', 'reImpSinv', 'additional_re_import_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '12.SITEMN', 'reImpSitemN', 'additional_re_import_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional re Import details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpNoteNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpSlNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpFrt', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpUnitIns', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpDuty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpSbNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpSbDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpPortCd', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpSinv', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImpSitemN', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_item_manufacturer_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.INVSNO', 'invSN', 'additional_item_manufacturer_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.ITMSNO', 'itemSN', 'additional_item_manufacturer_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '3.TYPE', 'itemManuType', 'additional_item_manufacturer_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4.MANUFACT CD', 'itemManuManufacturerCode', 'additional_item_manufacturer_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '5.SOURCE CY', 'itemManuSorceCy', 'additional_item_manufacturer_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '6.TRANS CY', 'itemManuTransCy', 'additional_item_manufacturer_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '7.ADDRESS', 'itemManuAddress', 'additional_item_manufacturer_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Item Manufacturer details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemManuType', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemManuManufacturerCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemManuSorceCy', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemManuTransCy', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemManuAddress', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_accessory_status_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.INVSNO', 'invSN', 'additional_accessory_status_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.ITMSNO', 'itemSN', 'additional_accessory_status_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '3.ACESSORY ITEM DETAILS', 'accessoryItmDtls', 'additional_accessory_status_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Accessory details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('accessoryItmDtls', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_single_window_declaration_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.INVSN', 'invSN', 'additional_single_window_declaration_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.ITMSNO3.INFO', 'itemSN', 'additional_single_window_declaration_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', 'TYP', 'singleWindowType', 'additional_single_window_declaration_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4.QUALIFIER', 'singleWindowQualifier', 'additional_single_window_declaration_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '5.INFO CD', 'singleWindowInfoCode', 'additional_single_window_declaration_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '6.INFO TEXT', 'singleWindowInfoText', 'additional_single_window_declaration_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '7.INFO MSR', 'singleWindowInfoMsr', 'additional_single_window_declaration_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '8.UQC', 'singleWindowUqc', 'additional_single_window_declaration_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Single Window Decleration details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowType', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowQualifier', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowInfoCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowInfoText', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowInfoMsr', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowUqc', 'NORM_TO_TEXT_VAL', null, null);


-- Create new Field Strategy mapping entries for additional_single_window_declaration_constituents_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.INVSN', 'invSN', 'additional_single_window_declaration_constituents_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.ITMSNO3.INFO', 'itemSN', 'additional_single_window_declaration_constituents_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '3.CS NO', 'singleWindowConstCsNo', 'additional_single_window_declaration_constituents_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4.NAME', 'singleWindowConstName', 'additional_single_window_declaration_constituents_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '5.CODE', 'singleWindowConstCode', 'additional_single_window_declaration_constituents_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '6.PERCENTAGE', 'singleWindowConstPercentage', 'additional_single_window_declaration_constituents_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '7.YIELD PCT', 'singleWindowConstYieldPct', 'additional_single_window_declaration_constituents_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '8.ING', 'singleWindowConstIng', 'additional_single_window_declaration_constituents_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Single Window Declaration constituents details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowConstCsNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowConstName', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowConstCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowConstPercentage', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowConstYieldPct', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowConstIng', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_single_window_declaration_control_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.INVSN', 'invSN', 'additional_single_window_declaration_control_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.ITMSNO', 'itemSN', 'additional_single_window_declaration_control_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '3.CONTROL TYPE', 'singleWindowControlType', 'additional_single_window_declaration_control_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4.LOCATION', 'singleWindowControlLocation', 'additional_single_window_declaration_control_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '5.SRT DT', 'singleWindowControlStartDt', 'additional_single_window_declaration_control_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '6.END DT', 'singleWindowControlEndDt', 'additional_single_window_declaration_control_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '7.RES CD', 'singleWindowControlResCd', 'additional_single_window_declaration_control_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '8.RES TEXT', 'singleWindowControlResText', 'additional_single_window_declaration_control_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Single Window Declaration Control details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowControlType', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowControlLocation', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowControlStartDt', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowControlEndDt', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowControlResCd', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('singleWindowControlResText', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_supporting_doc_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.INVSN', 'invSN', 'additional_supporting_doc_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.ITMSNO3.TYP', 'itemSN', 'additional_supporting_doc_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4.ICEGATE ID', 'supportingDocsIceGateId', 'additional_supporting_doc_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '5.IRN', 'supportingDocIrn', 'additional_supporting_doc_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '6.DOC', 'supportingDocsCd', 'additional_supporting_doc_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', 'CODE7.ISSUE PLACE8.ISSUE', 'supportingDocsIssuePlace', 'additional_supporting_doc_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', 'DT', 'supportingDocsIssueDt', 'additional_supporting_doc_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '9.EXP DT', 'supportingDocsExportDt', 'additional_supporting_doc_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Supporting Document details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supportingDocsType', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supportingDocsIceGateId', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supportingDocIrn', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supportingDocsCd', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supportingDocsIssuePlace', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supportingDocsIssueDt', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supportingDocsExportDt', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_container_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '1.CONTAINER NUMBER', 'additionalContainerNo', 'additional_container_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2.TRUCK NUMBER', 'additionalContainerTruckNo', 'additional_container_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '3.SEAL NUMBER', 'additionalContainerSealNo', 'additional_container_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4.FCL/LCL', 'additionalContainerFclLcL', 'additional_container_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Container details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('additionalContainerNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('additionalContainerTruckNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('additionalContainerSealNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('additionalContainerFclLcL', 'NORM_TO_TEXT_VAL', null, null);

-- Create new Field Strategy mapping entries for additional_invoice_details
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '2. INVOICE NO', 'additionalInvNo', 'additional_invoice_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '3. INVOICE AMOUNT', 'additionalInvAmt', 'additional_invoice_details', 9, 1.600, now(), null, 1);
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, UPDATED_AT, OPTIONAL_FIELD) VALUES ('BILL_OF_ENTRY', '4. CUR', 'additionalInvCurrency', 'additional_invoice_details', 9, 1.600, now(), null, 1);

-- Add new entries into the data normalizer Vo for the Additional Invoice details
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('additionalInvNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('additionalInvAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('additionalInvCurrency', 'NORM_TO_TEXT_VAL', null, null);


-- Delete the existing entries from the template mapping table for BILL_OF_ENTRY
delete from export_report_template_mapping where MAPPING_ENTITY = 'BILL_OF_ENTRY';

-- Create new Entries for BILL_OF_ENTRY PDF
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'BILL_OF_ENTRY', 'sr_no', -2, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IEC', 'IEC', 'BILL_OF_ENTRY', 'iec', -2, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('gstin', 'gstin', 'BILL_OF_ENTRY', 'gstin', -2, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'BILL_OF_ENTRY', 'bill_of_entry_no', -2, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry Date', 'Bill of Entry Date', 'BILL_OF_ENTRY', 'bill_of_entry_date', -2, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('beType', 'beType', 'BILL_OF_ENTRY', 'be_type', -2, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('portCode', 'portCode', 'BILL_OF_ENTRY', 'port_code', -2, 7);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name of Customs House', 'Name of Customs House', 'BILL_OF_ENTRY', 'name_of_customs_house', -2, 8);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('No Of Invoices', 'No Of Invoices', 'BILL_OF_ENTRY', 'no_of_invoices', -2, 9);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('No Of Items', 'No Of Items', 'BILL_OF_ENTRY', 'no_of_items', -2, 10);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BE Status', 'BE Status', 'BILL_OF_ENTRY', 'be_status', -2, 11);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('mode', 'mode', 'BILL_OF_ENTRY', 'mode', -2, 12);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('DEF BE', 'DEF BE', 'BILL_OF_ENTRY', 'def_be', -2, 13);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('KACHA', 'KACHA', 'BILL_OF_ENTRY', 'kacha', -2, 14);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sec 48', 'Sec 48', 'BILL_OF_ENTRY', 'sec_48', -2, 15);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Reimport', 'Reimport', 'BILL_OF_ENTRY', 're_imp', -2, 16);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('ADV', 'ADV', 'BILL_OF_ENTRY', 'adv', -2, 17);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Assess', 'Assess', 'BILL_OF_ENTRY', 'assess', -2, 18);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('EXAM', 'EXAM', 'BILL_OF_ENTRY', 'exam', -2, 19);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Highseas', 'Highseas', 'BILL_OF_ENTRY', 'highseas', -2, 20);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('First Check', 'First Check', 'BILL_OF_ENTRY', 'first_check', -2, 21);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Prov / Final', 'Prov / Final', 'BILL_OF_ENTRY', 'prov_final', -2, 22);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Country or origin', 'Country or origin', 'BILL_OF_ENTRY', 'imported_country_from', -2, 23);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Country of consignment', 'Country of consignment', 'BILL_OF_ENTRY', 'country_of_consignment', -2, 24);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Port Of Loading', 'Port Of Loading', 'BILL_OF_ENTRY', 'port_of_loading', -2, 25);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Port Of shipment', 'Port Of shipment', 'BILL_OF_ENTRY', 'port_of_shipment', -2, 26);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name of Importer', 'Name of Importer', 'BILL_OF_ENTRY', 'importers_name', -2, 27);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Address of Importer', 'Address of Importer', 'BILL_OF_ENTRY', 'importers_address', -2, 28);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('AD Code', 'AD Code', 'BILL_OF_ENTRY', 'ad_code', -2, 29);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CB Name', 'CB Name', 'BILL_OF_ENTRY', 'cb_name', -2, 30);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('AEO', 'AEO', 'BILL_OF_ENTRY', 'aeo', -2, 31);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('UCR', 'UCR', 'BILL_OF_ENTRY', 'ucr', -2, 32);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BCD', 'BCD', 'BILL_OF_ENTRY', 'bcd', -2, 33);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('ACD', 'ACD', 'BILL_OF_ENTRY', 'acd', -2, 34);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SWS', 'SWS', 'BILL_OF_ENTRY', 'sws', -2, 35);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('NCCD', 'NCCD', 'BILL_OF_ENTRY', 'nccd', -2, 36);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('ADD', 'ADD', 'BILL_OF_ENTRY', 'add', -2, 37);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CVD', 'CVD', 'BILL_OF_ENTRY', 'cvd', -2, 38);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IGST', 'IGST', 'BILL_OF_ENTRY', 'igst', -2, 39);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cess', 'Cess', 'BILL_OF_ENTRY', 'cess', -2, 40);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Total ass value', 'Total ass value', 'BILL_OF_ENTRY', 'total_ass_value', -2, 41);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SG', 'SG', 'BILL_OF_ENTRY', 'sg', -2, 42);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('AED', 'AED', 'BILL_OF_ENTRY', 'aed', -2, 43);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('GSIA', 'GSIA', 'BILL_OF_ENTRY', 'gsia', -2, 44);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('TTA', 'TTA', 'BILL_OF_ENTRY', 'tta', -2, 45);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Health', 'Health', 'BILL_OF_ENTRY', 'health', -2, 46);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Total Duty', 'Total Duty', 'BILL_OF_ENTRY', 'duty_summary_total_duty', -2, 47);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('INT', 'INT', 'BILL_OF_ENTRY', 'int', -2, 48);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Penalty', 'Penalty', 'BILL_OF_ENTRY', 'penalty', -2, 49);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Fine', 'Fine', 'BILL_OF_ENTRY', 'fine', -2, 50);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Total amount', 'Total amount', 'BILL_OF_ENTRY', 'total_amount', -2, 51);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IGM No', 'IGM No', 'BILL_OF_ENTRY', 'manifiest_igm_no', -2, 52);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IGM Date', 'IGM Date', 'BILL_OF_ENTRY', 'manifiest_igm_date', -2, 53);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Inward date', 'Inward date', 'BILL_OF_ENTRY', 'manifiest_inw_date', -2, 54);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('GIG MNO', 'GIG MNO', 'BILL_OF_ENTRY', 'manifiest_gig_mno', -2, 55);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('GIG MDT', 'GIG MDT', 'BILL_OF_ENTRY', 'manifiest_gig_mdt', -2, 56);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('MAWB No', 'MAWB No', 'BILL_OF_ENTRY', 'manifiest_mawb_no', -2, 57);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Date', 'Date', 'BILL_OF_ENTRY', 'manifiest_date', -2, 58);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('HAWB No', 'HAWB No', 'BILL_OF_ENTRY', 'manifiest_hawb_no', -2, 59);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Date', 'Date', 'BILL_OF_ENTRY', 'manifiest_m_date', -2, 60);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('PKG', 'PKG', 'BILL_OF_ENTRY', 'manifiest_pkg', -2, 61);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('GW', 'GW', 'BILL_OF_ENTRY', 'manifiest_gw', -2, 62);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Submission Date', 'Submission Date', 'BILL_OF_ENTRY', 'bill_of_entry_submission_date', -2, 63);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Submission Time', 'Submission Time', 'BILL_OF_ENTRY', 'bill_of_entry_submission_time', -2, 64);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Assessment Date', 'Assessment Date', 'BILL_OF_ENTRY', 'bill_of_entry_assessment_date', -2, 65);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Assessment Time', 'Assessment Time', 'BILL_OF_ENTRY', 'bill_of_entry_assessment_time', -2, 66);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Examination Date', 'Examination Date', 'BILL_OF_ENTRY', 'bill_of_entry_examination_date', -2, 67);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Examination Time', 'Examination Time', 'BILL_OF_ENTRY', 'bill_of_entry_examination_time', -2, 68);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('OOC Date', 'OOC Date', 'BILL_OF_ENTRY', 'bill_of_entry_ooc_date', -2, 69);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('OOC Time', 'OOC Time', 'BILL_OF_ENTRY', 'bill_of_entry_ooc_time', -2, 70);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Exchange rate', 'Exchange rate', 'BILL_OF_ENTRY', 'exchange_rate', -2, 71);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice Value', 'Invoice Value', 'BILL_OF_ENTRY', 'inv_value', -2, 72);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice Currency', 'Invoice Currency', 'BILL_OF_ENTRY', 'inv_currency', -2, 73);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice No', 'Invoice No', 'BILL_OF_ENTRY', 'inv_no', -2, 74);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice Date', 'Invoice Date', 'BILL_OF_ENTRY', 'inv_dt', -2, 75);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Pur order no.', 'Pur order no.', 'BILL_OF_ENTRY', 'pur_orde_no', -2, 76);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Pur order date', 'Pur order date', 'BILL_OF_ENTRY', 'pur_order_date', -2, 77);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('LC No.', 'LC No.', 'BILL_OF_ENTRY', 'lc_no', -2, 78);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('LC date', 'LC date', 'BILL_OF_ENTRY', 'lc_date', -2, 79);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Contract no', 'Contract no', 'BILL_OF_ENTRY', 'contrac_no', -2, 80);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Contract date', 'Contract date', 'BILL_OF_ENTRY', 'contract_date', -2, 81);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Buyer\'s name', 'Buyer\'s name', 'BILL_OF_ENTRY', 'buyers_name', -2, 82);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Buyer\'s address', 'Buyer\'s address', 'BILL_OF_ENTRY', 'buyers_address', -2, 83);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Seller\'s name', 'Seller\'s name', 'BILL_OF_ENTRY', 'sellers_name', -2, 84);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Seller\'s address', 'Seller\'s address', 'BILL_OF_ENTRY', 'sellers_address', -2, 85);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Supplier name', 'Supplier name', 'BILL_OF_ENTRY', 'supplier_name', -2, 86);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Supplier address', 'Supplier address', 'BILL_OF_ENTRY', 'supplier_address', -2, 87);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Third party name', 'Third party name', 'BILL_OF_ENTRY', 'third_party_name', -2, 88);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Third party address', 'Third party address', 'BILL_OF_ENTRY', 'third_party_address', -2, 89);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('AEO', 'AEO', 'BILL_OF_ENTRY', 'transacting_parties_aeo', -2, 90);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('AD code', 'AD code', 'BILL_OF_ENTRY', 'transacting_parties_ad_code', -2, 91);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice Value', 'Invoice Value', 'BILL_OF_ENTRY', 'valuation_inv_value', -2, 92);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Freight', 'Freight', 'BILL_OF_ENTRY', 'freight', -2, 93);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Insurance', 'Insurance', 'BILL_OF_ENTRY', 'insurance', -2, 94);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('HSS', 'HSS', 'BILL_OF_ENTRY', 'hss', -2, 95);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Loading', 'Loading', 'BILL_OF_ENTRY', 'loading', -2, 96);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Commn', 'Commn', 'BILL_OF_ENTRY', 'commn', -2, 97);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Pay terms', 'Pay terms', 'BILL_OF_ENTRY', 'pay_terms', -2, 98);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Valuation terms', 'Valuation terms', 'BILL_OF_ENTRY', 'valuation_terms', -2, 99);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('RELTD', 'RELTD', 'BILL_OF_ENTRY', 'reltd', -2, 100);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SVB CH', 'SVB CH', 'BILL_OF_ENTRY', 'svb_ch', -2, 101);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SVB No.', 'SVB No.', 'BILL_OF_ENTRY', 'svb_no', -2, 102);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Date', 'Date', 'BILL_OF_ENTRY', 'valuation_date', -2, 103);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('LOA', 'LOA', 'BILL_OF_ENTRY', 'loa', -2, 104);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice Currency', 'Invoice Currency', 'BILL_OF_ENTRY', 'valuation_inv_currency', -2, 105);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('term', 'term', 'BILL_OF_ENTRY', 'term', -2, 106);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('C&B', 'C&B', 'BILL_OF_ENTRY', 'c_b', -2, 107);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CoC', 'CoC', 'BILL_OF_ENTRY', 'coc', -2, 108);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CoP', 'CoP', 'BILL_OF_ENTRY', 'cop', -2, 109);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('HIND Chg', 'HIND Chg', 'BILL_OF_ENTRY', 'hind_chg', -2, 110);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('G&S', 'G&S', 'BILL_OF_ENTRY', 'g_s', -2, 111);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('DOC CH', 'DOC CH', 'BILL_OF_ENTRY', 'doc_ch', -2, 112);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('COO', 'COO', 'BILL_OF_ENTRY', 'coo', -2, 113);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('R&LF', 'R&LF', 'BILL_OF_ENTRY', 'r_lf', -2, 114);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('OTH Cost', 'OTH Cost', 'BILL_OF_ENTRY', 'oth_cost', -2, 115);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('LD/ULD', 'LD/ULD', 'BILL_OF_ENTRY', 'ld_uld', -2, 116);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('WS', 'WS', 'BILL_OF_ENTRY', 'ws', -2, 117);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('OTC', 'OTC', 'BILL_OF_ENTRY', 'otc', -2, 118);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('MISC Chg', 'MISC Chg', 'BILL_OF_ENTRY', 'misc_chg', -2, 119);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Ass Value', 'Ass Value', 'BILL_OF_ENTRY', 'ass_value', -2, 120);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item Code', 'Item Code', 'BILL_OF_ENTRY', 'item_code', -2, 121);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('HSN', 'HSN', 'BILL_OF_ENTRY', 'hsn', -2, 122);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Description & Technical Characteristics', 'Description & Technical Characteristics', 'BILL_OF_ENTRY', 'desc_technical_characteristics', -2, 123);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Unit Price', 'Unit Price', 'BILL_OF_ENTRY', 'unit_price', -2, 124);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Qty. Imported', 'Qty. Imported', 'BILL_OF_ENTRY', 'imported_qty', -2, 125);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Unit of Measurement', 'Unit of Measurement', 'BILL_OF_ENTRY', 'uqc', -2, 126);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item Value', 'Item Value', 'BILL_OF_ENTRY', 'item_value', -2, 127);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item assessable value', 'Item assessable value', 'BILL_OF_ENTRY', 'item_assessable_value', -2, 128);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Total Duty', 'Total Duty', 'BILL_OF_ENTRY', 'total_duty', -2, 129);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('FS', 'FS', 'BILL_OF_ENTRY', 'fs', -2, 130);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('PQ', 'PQ', 'BILL_OF_ENTRY', 'pq', -2, 131);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('DC', 'DC', 'BILL_OF_ENTRY', 'dc', -2, 132);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('WC', 'WC', 'BILL_OF_ENTRY', 'wc', -2, 133);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('AQ', 'AQ', 'BILL_OF_ENTRY', 'aq', -2, 134);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('UPI', 'UPI', 'BILL_OF_ENTRY', 'upi', -2, 135);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('COO', 'COO', 'BILL_OF_ENTRY', 'item_dtls_coo', -2, 136);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('C. QTY', 'C. QTY', 'BILL_OF_ENTRY', 'c_qty', -2, 137);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('C. UQC', 'C. UQC', 'BILL_OF_ENTRY', 'c_uqc', -2, 138);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('S. QTY', 'S. QTY', 'BILL_OF_ENTRY', 's_qty', -2, 139);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('S. UQC', 'S. UQC', 'BILL_OF_ENTRY', 's_uqc', -2, 140);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SCH', 'SCH', 'BILL_OF_ENTRY', 'sch', -2, 141);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('STND/PR', 'STND/PR', 'BILL_OF_ENTRY', 'stnd_pr', -2, 142);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('RSP', 'RSP', 'BILL_OF_ENTRY', 'rsp', -2, 143);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('REIMP', 'REIMP', 'BILL_OF_ENTRY', 'reimp', -2, 144);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('PROV', 'PROV', 'BILL_OF_ENTRY', 'prov', -2, 145);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('End Use', 'End Use', 'BILL_OF_ENTRY', 'end_use', -2, 146);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('PRODN', 'PRODN', 'BILL_OF_ENTRY', 'prodn', -2, 147);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CNTRL', 'CNTRL', 'BILL_OF_ENTRY', 'cntrl', -2, 148);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('QUALFR', 'QUALFR', 'BILL_OF_ENTRY', 'qualfr', -2, 149);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CONTNT', 'CONTNT', 'BILL_OF_ENTRY', 'contnt', -2, 150);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('STMNT', 'STMNT', 'BILL_OF_ENTRY', 'stmnt', -2, 151);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SUP DOCS', 'SUP DOCS', 'BILL_OF_ENTRY', 'sup_docs', -2, 152);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Notn no.', 'Notn no.', 'BILL_OF_ENTRY', 'notn_no', -2, 153);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Notn Sr. no.', 'Notn Sr. no.', 'BILL_OF_ENTRY', 'notn_sr_no', -2, 154);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BCD rate', 'BCD rate', 'BILL_OF_ENTRY', 'bcd_rate', -2, 155);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BCD Amount', 'BCD Amount', 'BILL_OF_ENTRY', 'bcd_amount', -2, 156);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('BCD Duty FG', 'BCD Duty FG', 'BILL_OF_ENTRY', 'bcd_duty_fg', -2, 157);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Acd Rate', 'Acd Rate', 'BILL_OF_ENTRY', 'acd_rate', -2, 158);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Acd Value', 'Acd Value', 'BILL_OF_ENTRY', 'acd_amount', -2, 159);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('ACD Duty FG', 'ACD Duty FG', 'BILL_OF_ENTRY', 'acd_duty_fg', -2, 160);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sws Rate', 'Sws Rate', 'BILL_OF_ENTRY', 'sws_rate', -2, 161);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sws Value', 'Sws Value', 'BILL_OF_ENTRY', 'sws_amount', -2, 162);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SWS Duty FG', 'SWS Duty FG', 'BILL_OF_ENTRY', 'sws_duty_fg', -2, 163);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sad Rate', 'Sad Rate', 'BILL_OF_ENTRY', 'sad_rate', -2, 164);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sad Value', 'Sad Value', 'BILL_OF_ENTRY', 'sad_amount', -2, 165);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SAD Duty FG', 'SAD Duty FG', 'BILL_OF_ENTRY', 'sad_duty_fg', -2, 166);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IGST Notn no.', 'IGST Notn no.', 'BILL_OF_ENTRY', 'igst_notn_no', -2, 167);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IGST Notn Sr. no.', 'IGST Notn Sr. no.', 'BILL_OF_ENTRY', 'igst_notn_sr_no', -2, 168);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Igst Rate', 'Igst Rate', 'BILL_OF_ENTRY', 'igst_rate', -2, 169);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Igst Value', 'Igst Value', 'BILL_OF_ENTRY', 'igst_amount', -2, 170);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IGST Duty FG', 'IGST Duty FG', 'BILL_OF_ENTRY', 'igst_duty_fg', -2, 171);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cess Notn no.', 'Cess Notn no.', 'BILL_OF_ENTRY', 'cess_notn_no', -2, 172);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cess Notn Sr. no.', 'Cess Notn Sr. no.', 'BILL_OF_ENTRY', 'cess_notn_sr_no', -2, 173);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Customs Cess rate', 'Customs Cess rate', 'BILL_OF_ENTRY', 'customs_cess_rate', -2, 174);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cess Value', 'Cess Value', 'BILL_OF_ENTRY', 'cess_amount', -2, 175);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cess Duty FG', 'Cess Duty FG', 'BILL_OF_ENTRY', 'cess_duty_fg', -2, 176);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Add Rate', 'Add Rate', 'BILL_OF_ENTRY', 'add_rate', -2, 177);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Add Value', 'Add Value', 'BILL_OF_ENTRY', 'add_amount', -2, 178);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('ADD Duty FG', 'ADD Duty FG', 'BILL_OF_ENTRY', 'add_duty_fg', -2, 179);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cvd Rate', 'Cvd Rate', 'BILL_OF_ENTRY', 'cvd_rate', -2, 180);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cvd Value', 'Cvd Value', 'BILL_OF_ENTRY', 'cvd_amount', -2, 181);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CVD Duty FG', 'CVD Duty FG', 'BILL_OF_ENTRY', 'cvd_duty_fg', -2, 182);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SG rate', 'SG rate', 'BILL_OF_ENTRY', 'sg_rate', -2, 183);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SG amt', 'SG amt', 'BILL_OF_ENTRY', 'sg_amt', -2, 184);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SG Duty FG', 'SG Duty FG', 'BILL_OF_ENTRY', 'sg_duty_fg', -2, 185);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SP EXD rate', 'SP EXD rate', 'BILL_OF_ENTRY', 'sp_exd_rate', -2, 186);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SP EXD amt', 'SP EXD amt', 'BILL_OF_ENTRY', 'sp_exd_amt', -2, 187);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CH Cess rate', 'CH Cess rate', 'BILL_OF_ENTRY', 'ch_cess_rate', -2, 188);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CH Cess amt', 'CH Cess amt', 'BILL_OF_ENTRY', 'ch_cess_amt', -2, 189);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('TTA rate', 'TTA rate', 'BILL_OF_ENTRY', 'tta_rate', -2, 190);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('TTA amt', 'TTA amt', 'BILL_OF_ENTRY', 'tta_amt', -2, 191);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cess rate', 'Cess rate', 'BILL_OF_ENTRY', 'oth_duty_cess_rate', -2, 192);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Cess amt', 'Cess amt', 'BILL_OF_ENTRY', 'oth_duty_cess_amt', -2, 193);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CVD EDC rate', 'CVD EDC rate', 'BILL_OF_ENTRY', 'cvd_edc_rate', -2, 194);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CVD EDC amt', 'CVD EDC amt', 'BILL_OF_ENTRY', 'cvd_edc_amt', -2, 195);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CVD HEC rate', 'CVD HEC rate', 'BILL_OF_ENTRY', 'cvd_hec_rate', -2, 196);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CVD HEC amt', 'CVD HEC amt', 'BILL_OF_ENTRY', 'cvd_hec_amt', -2, 197);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CUS EDC rate', 'CUS EDC rate', 'BILL_OF_ENTRY', 'cus_edc_rate', -2, 198);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CUS EDC amt', 'CUS EDC amt', 'BILL_OF_ENTRY', 'cus_edc_amt', -2, 199);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CUS HEC rate', 'CUS HEC rate', 'BILL_OF_ENTRY', 'cus_hec_rate', -2, 200);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('CUS HEC amt', 'CUS HEC amt', 'BILL_OF_ENTRY', 'cus_hec_amt', -2, 201);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('NCD rate', 'NCD rate', 'BILL_OF_ENTRY', 'ncd_rate', -2, 202);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('NCD amt', 'NCD amt', 'BILL_OF_ENTRY', 'ncd_amt', -2, 203);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('AGGR rate', 'AGGR rate', 'BILL_OF_ENTRY', 'aggr_rate', -2, 204);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('AGGR amt', 'AGGR amt', 'BILL_OF_ENTRY', 'aggr_amt', -2, 205);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('REF No.', 'REF No.', 'BILL_OF_ENTRY', 'REF_No.', -2, 206);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('REF Dt', 'REF Dt', 'BILL_OF_ENTRY', 'ref_dt', -2, 207);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('PRT CD', 'PRT CD', 'BILL_OF_ENTRY', 'prt_cd', -2, 208);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('LAB', 'LAB', 'BILL_OF_ENTRY', 'lab', -2, 209);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('P/F', 'P/F', 'BILL_OF_ENTRY', 'P/f', -2, 210);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('LOAD Date', 'LOAD Date', 'BILL_OF_ENTRY', 'load_date', -2, 211);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('INV Sr. No.', 'INV Sr. No.', 'BILL_OF_ENTRY', 'inv_sr_no', -2, 212);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item Sr. No.', 'Item Sr. No.', 'BILL_OF_ENTRY', 'item_sr_no', -2, 213);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Notn No.', 'Notn No.', 'BILL_OF_ENTRY', 're_imp_notn_no', -2, 214);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SLNO', 'SLNO', 'BILL_OF_ENTRY', 'slno', -2, 215);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('FRT', 'FRT', 'BILL_OF_ENTRY', 'frt', -2, 216);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('INS', 'INS', 'BILL_OF_ENTRY', 'ins', -2, 217);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Duty', 'Duty', 'BILL_OF_ENTRY', 'duty', -2, 218);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SB No.', 'SB No.', 'BILL_OF_ENTRY', 'sb_no', -2, 219);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SB DT', 'SB DT', 'BILL_OF_ENTRY', 'sb_dt', -2, 220);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('PORT CD', 'PORT CD', 'BILL_OF_ENTRY', 'port_cd', -2, 221);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SINV', 'SINV', 'BILL_OF_ENTRY', 'sinv', -2, 222);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('SITEMN', 'SITEMN', 'BILL_OF_ENTRY', 'sitemn', -2, 223);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence SL No.', 'Licence SL No.', 'BILL_OF_ENTRY', 'lic_sl_no', -2, 224);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence No.', 'Licence No.', 'BILL_OF_ENTRY', 'lic_no', -2, 225);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence Date', 'Licence Date', 'BILL_OF_ENTRY', 'lic_date', -2, 226);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence Code', 'Licence Code', 'BILL_OF_ENTRY', 'lic_code', -2, 227);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence Port', 'Licence Port', 'BILL_OF_ENTRY', 'lic_port', -2, 228);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence Debit Value', 'Licence Debit Value', 'BILL_OF_ENTRY', 'lic_debit_value', -2, 229);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence Qty.', 'Licence Qty.', 'BILL_OF_ENTRY', 'lic_qty', -2, 230);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence Debit Duty', 'Licence Debit Duty', 'BILL_OF_ENTRY', 'lic_debit_duty', -2, 231);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Licence UQC', 'Licence UQC', 'BILL_OF_ENTRY', 'lic_uqc', -2, 232);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Certificate No.', 'Certificate No.', 'BILL_OF_ENTRY', 'certificate_no', -2, 233);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Date', 'Date', 'BILL_OF_ENTRY', 'certificate_date', -2, 234);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Type', 'Type', 'BILL_OF_ENTRY', 'certificate_type', -2, 235);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('PRC Level', 'PRC Level', 'BILL_OF_ENTRY', 'hss_prc_level', -2, 236);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IEC', 'IEC', 'BILL_OF_ENTRY', 'hss_iec', -2, 237);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Baranch SL No.', 'Baranch SL No.', 'BILL_OF_ENTRY', 'hss_baranch_sl_no', -2, 238);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('OCC No', 'OCC No', 'BILL_OF_ENTRY', 'ooc_no', -2, 239);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('OCC Date', 'OCC Date', 'BILL_OF_ENTRY', 'ooc_date', -2, 240);


INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'SINGLE_WINDOW_DETAILS', 'sr_no', -3, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'SINGLE_WINDOW_DETAILS', 'bill_of_entry_no', -3, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice Sr. No.', 'Invoice Sr. No.', 'SINGLE_WINDOW_DETAILS', 'inv_sn', -3, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item Sr. No.', 'Item Sr. No.', 'SINGLE_WINDOW_DETAILS', 'item_sn', -3, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Info Type', 'Info Type', 'SINGLE_WINDOW_DETAILS', 'single_window_type', -3, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Qualifier', 'Qualifier', 'SINGLE_WINDOW_DETAILS', 'single_window_qualifier', -3, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Info Cd', 'Info Cd', 'SINGLE_WINDOW_DETAILS', 'single_window_info_code', -3, 7);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Info Text', 'Info Text', 'SINGLE_WINDOW_DETAILS', 'single_window_info_text', -3, 8);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Info Msr', 'Info Msr', 'SINGLE_WINDOW_DETAILS', 'single_window_info_msr', -3, 9);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('UQC', 'UQC', 'SINGLE_WINDOW_DETAILS', 'single_window_uqc', -3, 10);

INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'SINGLE_WINDOW_CONST_DETAILS', 'sr_no', -4, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'SINGLE_WINDOW_CONST_DETAILS', 'bill_of_entry_no', -4, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice Sr. No.', 'Invoice Sr. No.', 'SINGLE_WINDOW_CONST_DETAILS', 'inv_sn', -4, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item Sr. No.', 'Item Sr. No.', 'SINGLE_WINDOW_CONST_DETAILS', 'item_sn', -4, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('C Sno.', 'C Sno.', 'SINGLE_WINDOW_CONST_DETAILS', 'single_window_const_cs_no', -4, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Name', 'Name', 'SINGLE_WINDOW_CONST_DETAILS', 'single_window_const_name', -4, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Code', 'Code', 'SINGLE_WINDOW_CONST_DETAILS', 'single_window_const_code', -4, 7);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Percentage', 'Percentage', 'SINGLE_WINDOW_CONST_DETAILS', 'single_window_const_percentage', -4, 8);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Yield Pct', 'Yield Pct', 'SINGLE_WINDOW_CONST_DETAILS', 'single_window_const_yield_pct', -4, 9);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Ing', 'Ing', 'SINGLE_WINDOW_CONST_DETAILS', 'single_window_const_ing', -4, 10);

INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'SINGLE_WINDOW_CONTROL_DETAILS', 'sr_no', -5, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'SINGLE_WINDOW_CONTROL_DETAILS', 'bill_of_entry_no', -5, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Invoice Sr. No.', 'Invoice Sr. No.', 'SINGLE_WINDOW_CONTROL_DETAILS', 'inv_sn', -5, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Item Sr. No.', 'Item Sr. No.', 'SINGLE_WINDOW_CONTROL_DETAILS', 'item_sn', -5, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Control Type', 'Control Type', 'SINGLE_WINDOW_CONTROL_DETAILS', 'single_window_control_type', -5, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Location', 'Location', 'SINGLE_WINDOW_CONTROL_DETAILS', 'single_window_control_location', -5, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Srt Dt', 'Srt Dt', 'SINGLE_WINDOW_CONTROL_DETAILS', 'single_window_control_start_dt', -5, 7);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('End Dt', 'End Dt', 'SINGLE_WINDOW_CONTROL_DETAILS', 'single_window_control_end_dt', -5, 8);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Res Cd', 'Res Cd', 'SINGLE_WINDOW_CONTROL_DETAILS', 'single_window_control_res_cd', -5, 9);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Res Text', 'Res Text', 'SINGLE_WINDOW_CONTROL_DETAILS', 'single_window_control_res_text', -5, 10);

INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Sr. No.', 'Sr. No.', 'SUPPORTING_DOCS_DETAILS', 'sr_no', -6, 1);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('Bill of Entry No.', 'Bill of Entry No.', 'SUPPORTING_DOCS_DETAILS', 'bill_of_entry_no', -6, 2);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('TYP', 'TYP', 'SUPPORTING_DOCS_DETAILS', 'supporting_docs_type', -6, 3);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('ICEGATE ID', 'ICEGATE ID', 'SUPPORTING_DOCS_DETAILS', 'supporting_docs_ice_gate_id', -6, 4);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('IRN', 'IRN', 'SUPPORTING_DOCS_DETAILS', 'supporting_doci_rn', -6, 5);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('DOC CODE', 'DOC CODE', 'SUPPORTING_DOCS_DETAILS', 'supporting_docs_cd', -6, 6);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('ISSUE PLACE', 'ISSUE PLACEt', 'SUPPORTING_DOCS_DETAILS', 'supporting_docs_issue_place', -6, 7);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('ISSUE DT', 'ISSUE DTt', 'SUPPORTING_DOCS_DETAILS', 'supporting_docs_issue_dt', -6, 8);
INSERT INTO export_report_template_mapping (COLUMN_NAME, DISPLAY_NAME, MAPPING_ENTITY, MAPPING_KEY, REF_ID, MAPPING_SEQUENCE) VALUES ('EXP DT', 'EXP DT', 'SUPPORTING_DOCS_DETAILS', 'supporting_docs_export_dt', -6, 9);