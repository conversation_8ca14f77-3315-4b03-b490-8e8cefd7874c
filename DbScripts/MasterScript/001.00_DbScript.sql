-- #################################################################
-- ### Server version: 			    1.0
-- ### Date (DD-MM-YYYY):           27-03-2023
-- ### Developer Name: 		        Mrunal Nagare
-- ### Comments:                    Master DB script
-- #################################################################

-- Create database
drop database if exists pdf_reader;
create database pdf_reader;
use  pdf_reader;

-- Create transaction manager table
drop table if exists transactions_manager;
create table transactions_manager
(
    ID            bigint auto_increment primary key,
    TXN_ID        varchar(255) null,
    PAN           varchar(20)  not null,
    STATUS        varchar(50)  not null,
    TYPE          varchar(100) not null,
    SUB_TXN_ID    varchar(255) null,
    OTH<PERSON>_DETAILS varchar(255) null,
    CREATED_AT    datetime     null,
    UPDATED_AT    datetime     null
);

create index status_index
    on transactions_manager (STATUS);

create index txn_id_index
    on transactions_manager (TXN_ID);

create index type_index
    on transactions_manager (TYPE);


-- Create export report manager table.
drop table if exists export_reports_manager;
create table export_reports_manager
(
    ID              bigint auto_increment primary key,
    TXN_ID          varchar(255) null,
    PAN             varchar(255) null,
    STATUS          varchar(255) null,
    SUB_TXN_ID      varchar(255) null,
    REPORT_NAME     varchar(255) null,
    REPORT_TYPE     varchar(255) null,
    REPORT_LOCATION varchar(255) null,
    FILE_ID         varchar(255) null,
    CREATED_AT      datetime     null,
    UPDATED_AT      datetime     null,
    OTHER_DETAILS   varchar(255) null
);


-- Create file upload details table
drop table if exists file_upload_details;
create table file_upload_details
(
    ID             bigint auto_increment primary key,
    FILE_NAME      varchar(255)         null,
    FILE_TYPE      varchar(255)         null,
    PAN            varchar(255)         null,
    STATUS         varchar(255)         null,
    UPLOAD_TIME    datetime             null,
    USER_ID        bigint               null,
    OPERATION      varchar(50)          null,
    FILE_LOC       varchar(255)         null,
    TXN_ID         varchar(255)         null,
    RETRY_COUNT    int        default 0 null,
    SHEET_METADATA mediumtext           null,
    IS_REUPLOADED  tinyint(1) default 0 null,
    SUB_TXN_ID     varchar(255)         null,
    CREATED_AT     datetime             null,
    UPDATED_AT     datetime             null
);

create index txn_id_index on file_upload_details (TXN_ID);
create index file_type_index on file_upload_details (FILE_TYPE);
create index pan_index on file_upload_details (PAN);

-- Create export report template mapping table
drop table if exists export_report_template_mapping;
create table export_report_template_mapping
(
    ID               bigint auto_increment
        primary key,
    COLUMN_NAME      varchar(255) not null,
    DISPLAY_NAME     varchar(255) null,
    MAPPING_ENTITY   varchar(30)  not null,
    MAPPING_KEY      varchar(30)  not null,
    REF_ID           bigint       not null,
    MAPPING_SEQUENCE int          not null,
    constraint MAPPING_KEY
        unique (MAPPING_KEY, MAPPING_ENTITY)
);

-- Create table data normalizer
drop table if exists data_normalizer;
create table data_normalizer
(
    ID            bigint auto_increment
        primary key,
    MAPPING_KEY   varchar(30) not null comment 'Mapping key as per file or gstn template mapping e.g idt',
    NORMALIZATION varchar(50) null comment 'Normalization need to be performed on value e.g. DATE_FORMAT',
    RULE_TYPE     varchar(30) null comment 'Configurable function type e.g. REGEX, CODE',
    RULE          text        null comment 'Configurable function body e.g. ^[0-9]{2,8}$',
    constraint MAPPING_KEY
        unique (MAPPING_KEY)
);

INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('iec_code', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('applicant_type', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('shipping_bill_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('shipping_bill_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invoice_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invoice_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('leo_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sr_no_dbk', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('product_code', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('product_desc', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invoice_value_excluding_tax', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('qty_as_per_invoice', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('uqc', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('available_stock', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('consumed_claimed_qty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('fob_value_as_per_invoice', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('custom_1', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('custom_2', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('custom_3', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('item_code', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('desc_technical_characteristics', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('bill_of_entry_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('bill_of_entry_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('name_of_customs_house', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('hsn', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('imported_qty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('available_qty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('assessable_value_as_per_boe', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('bcd_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('customs_cess_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('imported_country_from', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supplier_name', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('is_assessment_final', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('foreign_material_details', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('input_qty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('imported_status', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('irrecoverable_wastage_qty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('recoverable_wastage_qty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sale_price_waste_per_unit', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('by_product_co_product_qty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sale_value_per_unit', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('custom_house_name', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('deec_dfia', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('re_exp', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('state_of_origin', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('country_of_final_destination', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('port_of_final_destination', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('country_of_discharge', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('ad_code', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cb_name', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('ifsc_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('fob_val', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbk_claim', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igst_amt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cess_amt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rodtep_amt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('leo_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('brc_realisation_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_serial_no', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_term', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_val', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_fob_val', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('exchange_rate', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('item_serial_no', 'NORM_TO_INT', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('item_desc', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('qty_as_per_shipping_bill', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('item_val_fc', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('item_fob', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igst_stat', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itm_igst_amt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sch_code', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('schema_description', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sqc_msr', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('pt_abroad', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('comp_cess', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('end_use', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('third_party_item', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('exportor_name', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('exportor_address', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('consignee_name', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('consignee_address', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rbi_waiver_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rbi_waiver_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbk_sr_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbk_value', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('assessable_value', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbk_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbk_amt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rosctl_amt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('egm_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_rodtep_amt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_rosctl_amt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('boe_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('boe_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('custom_house', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('custom_cess_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('imported_country', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('foreign_materials_sup_name', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supplier_address', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igm_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('prov_final', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('port_of_loading', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igm_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inw_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sec_48', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('re_imp', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('be_type', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('bcd_amount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('unit_price', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('acd_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('acd_amount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sws_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sws_amount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('total_duty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sad_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sad_amount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igst_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igst_amount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cess_amount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('add_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('add_amount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cvd_rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cvd_amount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_value', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inv_currency', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('item_value', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('ooc_date', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('ooc_no', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('customHouseName', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('shippingBillNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('shippingBillDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('portCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('mode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('exim', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('meis', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbk', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('deecDfia', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dfrc', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reExp', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('lut', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('stateOfOrigin', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('countryOdFinalDestination', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('portOfFinalDestination', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('countryOfDischarge', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('adCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cbName', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('aeo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('ifscNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('fobVal', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbkClaim', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igstAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cessAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rodtepAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('leoNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('leoDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('brcRealisationDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invSerialNo', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invTerm', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invVal', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invFobVal', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('freight', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('insurance', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('exchangeRate', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemSerialNo', 'NORM_TO_INT', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemDesc', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('qtyAsPerShippingBill', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemValFc', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemFob', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('pmv', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igstStat', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itmIgstAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('schCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('schemaDescription', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sqcMsr', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('ptAbroad', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('compCess', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('endUse', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('thirdPartyItem', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('iec', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('exportorName', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('exportorAddress', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('consigneeName', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('consigneeAddress', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rbiWaiverNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rbiWaiverDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbkSrNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbkValue', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('assessableValue', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbkRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('dbkAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rosctlAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('egmNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('rodtp', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invRodtepAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invRosctlAmt', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemCode', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('boeNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('boeDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('customHouse', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('importedQty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('availableQty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('utilizedQty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('assessableValueAsPerBoe', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('bcdRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('customCessRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('importedCountry', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supplierName', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('foreignMaterialsSupName', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('supplierAddress', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igmNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('hss', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('provFinal', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('portOfLoading', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igmDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('inwDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sec48', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('reImp', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('beType', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('bcdAmount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('unitPrice', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('quantity', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('acdRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('acdAmount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('swsRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('swsAmount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('totalDuty', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sadRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('sadAmount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igstRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('igstAmount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cessAmount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('addRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('addAmount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cvdRate', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('cvdAmount', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invValue', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('invCurrency', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('term', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('itemValue', 'NORM_TO_DOUBLE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('oocDate', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('oocNo', 'NORM_TO_TEXT_VAL', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('egmDt', 'NORM_TO_DATE', null, null);
INSERT INTO data_normalizer (MAPPING_KEY, NORMALIZATION, RULE_TYPE, RULE) VALUES ('gov_dbk_rate', 'NORM_TO_DOUBLE', null, null);


-- Create table PDF parsing strategies
drop table if exists pdf_parsing_strategies;
create table pdf_parsing_strategies
(
    ID                bigint auto_increment
        primary key,
    STRATEGY          varchar(50)          not null,
    IS_FIELD_SPECIFIC tinyint(1) default 0 null,
    constraint STRATEGY
        unique (STRATEGY)
);

INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('CUSTOM_HOUSE_NAME_SB', 1);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('VALUE_BELOW_FIELD_SL', 0);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('VALUE_BESIDE_FIELD_SL', 0);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('VALUE_BELOW_FIELD_ML', 0);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('VALUE_BESIDE_FIELD_SEPARATED_BY_SPACE_SL', 1);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('NAME_AND_ADDRESS_ML', 1);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('VALUE_BELOW_FIELD_SEPARATED_BY_SPACE_SL', 1);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('CUSTOM_HOUSE_NAME_BOE', 1);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('VALUE_BELOW_FIELD_SL_TBL', 0);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('VALUE_BELOW_FIELD_SEPARATED_BY_NEW_LINE_ML', 1);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('BOE_DUTY_RATE_VALUE', 0);
INSERT INTO pdf_parsing_strategies (STRATEGY, IS_FIELD_SPECIFIC) VALUES ('BOE_DUTY_AMOUNT_VALUE', 0);



-- Create PDF section details table
drop table if exists pdf_section_details;
create table pdf_section_details
(
    ID                   bigint auto_increment
        primary key,
    FILE_TYPE            varchar(50)                             not null,
    SECTION_KEY          varchar(50)                             not null,
    SECTION_DISPLAY_NAME varchar(100)                            not null,
    BUFFER_VALUE         decimal(5, 3) default 0.000             null,
    CREATED_AT           datetime      default CURRENT_TIMESTAMP null,
    UPDATED_AT           datetime      default CURRENT_TIMESTAMP null,
    constraint SECTION_KEY
        unique (SECTION_KEY),
    constraint unique_section
        unique (FILE_TYPE, SECTION_KEY)
);

INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('SHIPPING_BILL', 'shipping_bill_details', 'PART - I - SHIPPING BILL SUMMARY', 17.666, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('SHIPPING_BILL', 'invoice_details', 'PART - II - INVOICE DETAILS', 13.186, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('SHIPPING_BILL', 'item_details', 'PART - III - ITEM DETAILS', 13.146, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('SHIPPING_BILL', 'drawback_and_rosl_claim', 'DRAWBACK & ROSL CLAIM', 6.120, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('SHIPPING_BILL', 'rodtep_details', 'RODTEP DETAILS', 6.120, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('SHIPPING_BILL', 'invoice_details_sub_section', 'H.INVOICE DETAILS', 6.120, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'bill_of_entry_summary', 'PART - I - BILL OF ENTRY SUMMARY', 17.666, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'invoice_and_valuation_details', 'PART - II - INVOICE & VALUATION DETAILS', 13.146, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'duties', 'PART - III - DUTIES', 4.186, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'additional_details', 'PART - IV - ADDITIONAL DETAILS', 13.146, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'other_compliances', 'PART - V - OTHER COMPLIANCES', 35.666, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'declaration', 'PART - VI - DECLARATION', 0.000, now());
INSERT INTO pdf_section_details (FILE_TYPE, SECTION_KEY, SECTION_DISPLAY_NAME, BUFFER_VALUE, CREATED_AT) VALUES ('BILL_OF_ENTRY', 'invoice_and_valuation_details_item_details', 'PART - II - INVOICE & VALUATION DETAILS', 13.146, now());


create index FILE_TYPE
    on pdf_section_details (FILE_TYPE);

create index FILE_TYPE_2
    on pdf_section_details (FILE_TYPE, SECTION_KEY);


-- Create PDF field stratedy mapping table
drop table if exists pdf_field_strategy_mapping;
create table pdf_field_strategy_mapping
(
    ID             bigint auto_increment
        primary key,
    FILE_TYPE      varchar(50)                 not null,
    FIELD_NAME     varchar(50)                 not null,
    MAPPING_KEY    varchar(50)                 not null,
    SECTION_KEY    varchar(50)                 not null,
    STRATEGY_ID    bigint                      not null,
    BUFFER_VALUE   decimal(5, 3) default 0.000 null,
    CREATED_AT     datetime                    null,
    UPDATED_AT     datetime                    null,
    OPTIONAL_FIELD tinyint(1)    default 0     null,
    constraint unique_strategy
        unique (FILE_TYPE, FIELD_NAME, STRATEGY_ID, MAPPING_KEY),
    constraint exim_pdf_field_strategy_mapping_ibfk_1
        foreign key (STRATEGY_ID) references pdf_parsing_strategies (ID),
    constraint exim_pdf_field_strategy_mapping_ibfk_2
        foreign key (SECTION_KEY) references pdf_section_details (SECTION_KEY)
);

create index SECTION_KEY
    on pdf_field_strategy_mapping (SECTION_KEY, FILE_TYPE);

create index strategy_fk
    on pdf_field_strategy_mapping (STRATEGY_ID);

INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'Custom House Name', 'customHouseName', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'CUSTOM_HOUSE_NAME_SB';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'SB No', 'shippingBillNo', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'SB Date', 'shippingBillDate', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'Port Code', 'portCode', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'GSTIN/TYPE', 'gstin', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SEPARATED_BY_SPACE_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1.MODE', 'mode', 'shipping_bill_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '3.EXMN', 'exim', 'shipping_bill_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '5.MEIS', 'meis', 'shipping_bill_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '6.DBK', 'dbk', 'shipping_bill_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '8.DEEC/DFIA', 'deecDfia', 'shipping_bill_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '9.DFRC', 'dfrc', 'shipping_bill_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '10.RE-EXP', 'reExp', 'shipping_bill_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '11.LUT', 'lut', 'shipping_bill_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '14.STATE OF ORIGIN', 'stateOfOrigin', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '13.COUNTRY OF FINALDESTINATION', 'countryOdFinalDestination', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '15.PORT OF FINAL DESTINATION', 'portOfFinalDestination', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '17.COUNTRY OF DISCHARGE', 'countryOfDischarge', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1.EXPORTER''S NAME & ADDRESS', 'exportorsNameAndAddress', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'NAME_AND_ADDRESS_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '7.CONSIGNEE NAME & ADDRESS', 'consigneeNameAndAddress', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'NAME_AND_ADDRESS_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'AD CODE:', 'adCode', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '4.RBI WAIVER NO.& DT', 'rbiWaiverNoAndDate', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SEPARATED_BY_SPACE_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '5.CB NAME', 'cbName', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '6.AEO', 'aeo', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'IFSC NO.', 'ifscNo', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1.DBK CLAIM', 'dbkClaim', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '2. IGST AMT', 'igstAmt', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '3.CESS AMT', 'cessAmt', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '5.RODTEP AMT', 'invRodtepAmt', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '4.LEO NO.', 'leoNo', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '6.LEO Date.', 'leoDate', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '8.BRC Realisation Date', 'brcRealisationDate', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1.S.No', 'invSerialNo', 'invoice_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '2.INVOICE No. & Dt.', 'invNoAndDate', 'invoice_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SEPARATED_BY_SPACE_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '7.INVTERM', 'invTerm', 'invoice_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1.INVOICE VALUE', 'invVal', 'invoice_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '2.FOB VALUE', 'invFobVal', 'invoice_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '3FREIGHT', 'freight', 'invoice_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '4.INSURANCE5DISCOUNT6.COMMISON', 'insurance', 'invoice_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '9.EXCHANGE RATE', 'exchangeRate', 'invoice_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1INVSN', 'invSerialNo', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '2ITEMSN', 'itemSerialNo', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '4.DESCRIPTION', 'prodDesc', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '5.QUANTITY', 'qtyAsPerShippingBill', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '6UQC', 'uqc', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '7.RATE', 'rate', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '8VALUE(F/C)', 'itemValFc', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '9.FOB (INR)', 'itemFob', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '10.PMV', 'pmv', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '15.IGSTSTAT', 'igstStat', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'IGST AMOUNT', 'itmIgstAmt', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '18SCHCOD', 'schCode', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'SCHEME DESCRIPTION', 'schemaDescription', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'SQC MSR', 'sqcMsr', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'PT Abroad', 'ptAbroad', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '25.COMP CESS', 'compCess', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SEPARATED_BY_SPACE_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '26.END USE', 'endUse', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'THIRD PARTY ITEM', 'thirdPartyItem', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'IEC/Br', 'iec', 'shipping_bill_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SEPARATED_BY_SPACE_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '3.HS CD', 'itemHsnCode', 'item_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'IGST VALUE', 'assessableValue', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '27.FTA BENEFIT AVAILED', 'ftaBenefitAvailed', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', 'REWARD BENEFIT', 'rewardBenefit', 'item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '3.DBK SNO.', 'dbkSrNo', 'drawback_and_rosl_claim', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '5.VALUE', 'dbkValue', 'drawback_and_rosl_claim', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '6.RATE', 'dbkRate', 'drawback_and_rosl_claim', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '7.DBK AMT', 'dbkAmt', 'drawback_and_rosl_claim', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '2.ITEM SNO', 'itemSno', 'drawback_and_rosl_claim', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1.INV SNO', 'invSno', 'drawback_and_rosl_claim', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '4. EGM NO.', 'egmNo', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '5. EGM DT.', 'egmDt', 'shipping_bill_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '6.ROSCTL AMT', 'invRosctlAmt', 'shipping_bill_details', ID, 1.600, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '10.ROSCTL AMT', 'rosctlAmt', 'drawback_and_rosl_claim', ID, 6.080, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1.INVSN2.ITMSN', 'rosctlInvSnItmSn', 'rodtep_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '6. VALUE', 'rodtepAmt', 'rodtep_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '7.RODTP', 'rodtp', 'shipping_bill_details', ID, 6.120, now(), 1 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '1.SNO', 'invSerialNo', 'invoice_details_sub_section', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'SHIPPING_BILL', '4.CURRENCY', 'invCurrency', 'invoice_details_sub_section', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'BE No', 'boeNo', 'bill_of_entry_summary', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'BE Date', 'boeDate', 'bill_of_entry_summary', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'Port Code', 'portCode', 'bill_of_entry_summary', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'BE Type', 'beType', 'bill_of_entry_summary', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'IEC/Br', 'iec', 'bill_of_entry_summary', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SEPARATED_BY_SPACE_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'GSTIN/TYPE', 'gstin', 'bill_of_entry_summary', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SEPARATED_BY_SPACE_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '13.COUNTRY OF ORIGIN', 'importedCountry', 'bill_of_entry_summary', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '15.PORT OF LOADING', 'portOfLoading', 'bill_of_entry_summary', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'AD CODE', 'adCode', 'bill_of_entry_summary', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.IGM NO', 'igmNo', 'bill_of_entry_summary', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.IGM DATE', 'igmDate', 'bill_of_entry_summary', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.INW DATE', 'inwDate', 'bill_of_entry_summary', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.INVSNO', 'invSN', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '14.Cur', 'invCurrency', 'invoice_and_valuation_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.INV VALUE', 'invValue', 'invoice_and_valuation_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.FREIGHT', 'freight', 'invoice_and_valuation_details', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '15.Term', 'term', 'invoice_and_valuation_details', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '29.ASSESS VALUE', 'assessableValueAsPerBoe', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'OOC No', 'oocNo', 'other_compliances', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'OOC Date', 'oocDate', 'other_compliances', ID, 0.000, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BESIDE_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'EXCHANGE RATE', 'exchangeRate', 'bill_of_entry_summary', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '12. PROV/ FINAL', 'provFinal', 'bill_of_entry_summary', ID, 10.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.MODE', 'mode', 'bill_of_entry_summary', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.REIMP', 'reImp', 'bill_of_entry_summary', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.SEC 48', 'sec48', 'bill_of_entry_summary', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.HSS', 'hss', 'bill_of_entry_summary', ID, 6.120, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.CTH', 'hsn', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.ITEM DESCRIPTION', 'itemDesc', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '11.UPI', 'unitPrice', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '13.C.QTY', 'quantity', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '14.C.UQC', 'uqc', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.AMOUNT', 'itemValue', 'invoice_and_valuation_details_item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.IMPORTER NAME & ADDRESS', 'importersNameAndAddress', 'bill_of_entry_summary', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'NAME_AND_ADDRESS_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.LIC SLNO', 'licSlno', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.LIC NO', 'licNo', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.LIC DATE', 'licDate', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.CODE', 'code', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.PORT', 'port', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.DEBIT VALUE', 'debitValue', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '9.QTY', 'licenceQty', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '10.UQC', 'licenceUqc', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '11.DEBIT DUTY', 'debitDuty', 'additional_details', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.INVOICE NO. & DT.', 'invoiceNoAndDate', 'invoice_and_valuation_details', ID, 10.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SEPARATED_BY_NEW_LINE_ML';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.ITEMSN', 'itemSN', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.S.NO', 'invSN', 'invoice_and_valuation_details', ID, 10.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '30. TOTAL DUTY', 'totalDuty', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1. BCD', 'bcdRate', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1. BCD', 'bcdAmount', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.ACD', 'acdRate', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '2.ACD', 'acdAmount', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.SWS', 'swsRate', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '3.SWS', 'swsAmount', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.SAD', 'sadRate', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '4.SAD', 'sadAmount', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.IGST', 'igstRate', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '5.IGST', 'igstAmount', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.G. CESS', 'customCessRate', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '6.G. CESS', 'cessAmount', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.ADD', 'addRate', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '7.ADD', 'addAmount', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CVD', 'cvdRate', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_RATE_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '8.CVD', 'cvdAmount', 'duties', ID, 1.600, now(), 0 from pdf_parsing_strategies where STRATEGY = 'BOE_DUTY_AMOUNT_VALUE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', 'INDIAN CUSTOMS', 'customHouse', 'bill_of_entry_summary', ID, 4.818, now(), 0 from pdf_parsing_strategies where STRATEGY = 'CUSTOM_HOUSE_NAME_BOE';
INSERT INTO pdf_field_strategy_mapping (FILE_TYPE, FIELD_NAME, MAPPING_KEY, SECTION_KEY, STRATEGY_ID, BUFFER_VALUE, CREATED_AT, OPTIONAL_FIELD) select 'BILL_OF_ENTRY', '1.S NO.', 'itemSN', 'invoice_and_valuation_details_item_details', ID, 6.080, now(), 0 from pdf_parsing_strategies where STRATEGY = 'VALUE_BELOW_FIELD_SL_TBL';